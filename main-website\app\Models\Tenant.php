<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Tenant extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'domain',
        'custom_domain',
        'custom_domain_verified',
        'description',
        'logo',
        'status',
        'plan_id',
        'owner_id',
        'settings',
        'theme_id',
        'is_active',
        'expires_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'settings' => 'array',
        'is_active' => 'boolean',
        'custom_domain_verified' => 'boolean',
        'expires_at' => 'datetime',
    ];

    /**
     * Get the owner of the tenant.
     */
    public function owner()
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    /**
     * Get the users that belong to the tenant.
     */
    public function users()
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the plan that the tenant is subscribed to.
     */
    public function plan()
    {
        return $this->belongsTo(Plan::class);
    }

    /**
     * Get the theme that the tenant is using.
     */
    public function theme()
    {
        return $this->belongsTo(Theme::class);
    }

    /**
     * Get the courses that belong to the tenant.
     */
    public function courses()
    {
        return $this->hasMany(Course::class);
    }

    /**
     * Get the students that belong to the tenant.
     */
    public function students()
    {
        return $this->hasMany(User::class)->where('role', 'student');
    }

    /**
     * Get the instructors that belong to the tenant.
     */
    public function instructors()
    {
        return $this->hasMany(User::class)->where('role', 'instructor');
    }

    /**
     * Get the subscriptions that belong to the tenant.
     */
    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get the payments that belong to the tenant.
     */
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Check if the tenant is active.
     */
    public function isActive()
    {
        return $this->status === 'active';
    }

    /**
     * Check if the tenant is pending.
     */
    public function isPending()
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the tenant is suspended.
     */
    public function isSuspended()
    {
        return $this->status === 'suspended';
    }

    /**
     * Check if the tenant has expired.
     */
    public function hasExpired()
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Get the themes for the tenant.
     */
    public function themes()
    {
        return $this->hasMany(Theme::class);
    }

    /**
     * Get the active theme for the tenant.
     */
    public function activeTheme()
    {
        return $this->belongsTo(Theme::class, 'theme_id');
    }

    /**
     * Get the modules owned by the tenant.
     */
    public function ownedModules()
    {
        return $this->hasMany(Module::class);
    }

    /**
     * Get the modules installed by the tenant.
     */
    public function installedModules()
    {
        return $this->belongsToMany(Module::class)
            ->withPivot('is_active', 'settings')
            ->withTimestamps();
    }

    /**
     * Get the active modules for the tenant.
     */
    public function activeModules()
    {
        return $this->installedModules()->wherePivot('is_active', true)->get();
    }

    /**
     * Get the full domain for the tenant.
     */
    public function getFullDomainAttribute(): string
    {
        if ($this->custom_domain && $this->custom_domain_verified) {
            return $this->custom_domain;
        }

        return $this->domain . '.' . config('app.tenant_domain', 'naxofy.com');
    }

    /**
     * Get the dashboard URL for the tenant.
     */
    public function getDashboardUrlAttribute(): string
    {
        $protocol = config('app.env') === 'production' ? 'https://' : 'http://';
        return $protocol . $this->full_domain . '/dashboard';
    }

    /**
     * Get the frontend URL for the tenant.
     */
    public function getFrontendUrlAttribute(): string
    {
        $protocol = config('app.env') === 'production' ? 'https://' : 'http://';
        return $protocol . $this->full_domain;
    }

    /**
     * Get the API URL for the tenant.
     */
    public function getApiUrlAttribute(): string
    {
        $protocol = config('app.env') === 'production' ? 'https://' : 'http://';
        return $protocol . $this->full_domain . '/api';
    }

    /**
     * Verify the custom domain.
     */
    public function verifyCustomDomain(): bool
    {
        // In a real implementation, this would check DNS records
        // and verify ownership of the domain
        if ($this->custom_domain) {
            $this->custom_domain_verified = true;
            $this->save();
            return true;
        }

        return false;
    }

    /**
     * Check if the tenant has a verified custom domain.
     */
    public function hasVerifiedCustomDomain(): bool
    {
        return $this->custom_domain && $this->custom_domain_verified;
    }
}
