<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Theme;
use App\Models\ThemeCustomization;
use App\Models\ThemeTemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ThemeCustomizationController extends Controller
{
    /**
     * Display a listing of the customizations for the authenticated tenant.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        // Only tenants can have customizations
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can have customizations'
            ], 403);
        }
        
        $themeId = $request->input('theme_id');
        $type = $request->input('type');
        
        $query = ThemeCustomization::where('tenant_id', $user->tenant_id);
        
        if ($themeId) {
            $query->where('theme_id', $themeId);
        }
        
        if ($type) {
            $query->ofType($type);
        }
        
        $customizations = $query->get();
        
        return response()->json([
            'success' => true,
            'data' => $customizations
        ]);
    }
    
    /**
     * Store a newly created customization in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'theme_id' => 'required|exists:themes,id',
            'theme_template_id' => 'nullable|exists:theme_templates,id',
            'name' => 'required|string|max:255',
            'type' => 'required|string|max:50',
            'description' => 'nullable|string',
            'content' => 'required|string',
            'variables' => 'nullable|array',
            'settings' => 'nullable|array',
            'is_active' => 'nullable|boolean',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
        
        $user = Auth::user();
        
        // Only tenants can create customizations
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can create customizations'
            ], 403);
        }
        
        $theme = Theme::find($request->input('theme_id'));
        
        if (!$theme) {
            return response()->json([
                'success' => false,
                'message' => 'Theme not found'
            ], 404);
        }
        
        // Check if user has access to this theme
        if ($theme->tenant_id !== null && $theme->tenant_id !== $user->tenant_id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }
        
        // Check if template exists and belongs to the theme
        $templateId = $request->input('theme_template_id');
        if ($templateId) {
            $template = ThemeTemplate::where('id', $templateId)
                ->where('theme_id', $theme->id)
                ->first();
                
            if (!$template) {
                return response()->json([
                    'success' => false,
                    'message' => 'Template not found or does not belong to the theme'
                ], 404);
            }
        }
        
        // Create the customization
        $customization = new ThemeCustomization();
        $customization->tenant_id = $user->tenant_id;
        $customization->theme_id = $theme->id;
        $customization->theme_template_id = $templateId;
        $customization->name = $request->input('name');
        $customization->slug = Str::slug($request->input('name'));
        $customization->type = $request->input('type');
        $customization->description = $request->input('description');
        $customization->content = $request->input('content');
        $customization->variables = $request->input('variables');
        $customization->settings = $request->input('settings');
        $customization->is_active = $request->input('is_active', true);
        $customization->save();
        
        return response()->json([
            'success' => true,
            'message' => 'Customization created successfully',
            'data' => $customization
        ], 201);
    }
    
    /**
     * Display the specified customization.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $user = Auth::user();
        
        // Only tenants can view their own customizations
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can view their own customizations'
            ], 403);
        }
        
        $customization = ThemeCustomization::where('id', $id)
            ->where('tenant_id', $user->tenant_id)
            ->first();
            
        if (!$customization) {
            return response()->json([
                'success' => false,
                'message' => 'Customization not found'
            ], 404);
        }
        
        return response()->json([
            'success' => true,
            'data' => $customization
        ]);
    }
    
    /**
     * Update the specified customization in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'content' => 'sometimes|required|string',
            'variables' => 'nullable|array',
            'settings' => 'nullable|array',
            'is_active' => 'nullable|boolean',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
        
        $user = Auth::user();
        
        // Only tenants can update their own customizations
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can update their own customizations'
            ], 403);
        }
        
        $customization = ThemeCustomization::where('id', $id)
            ->where('tenant_id', $user->tenant_id)
            ->first();
            
        if (!$customization) {
            return response()->json([
                'success' => false,
                'message' => 'Customization not found'
            ], 404);
        }
        
        // Update the customization
        if ($request->has('name')) {
            $customization->name = $request->input('name');
            $customization->slug = Str::slug($request->input('name'));
        }
        
        if ($request->has('description')) {
            $customization->description = $request->input('description');
        }
        
        if ($request->has('content')) {
            $customization->content = $request->input('content');
        }
        
        if ($request->has('variables')) {
            $customization->variables = $request->input('variables');
        }
        
        if ($request->has('settings')) {
            $customization->settings = $request->input('settings');
        }
        
        if ($request->has('is_active')) {
            $customization->is_active = $request->input('is_active');
        }
        
        $customization->save();
        
        return response()->json([
            'success' => true,
            'message' => 'Customization updated successfully',
            'data' => $customization
        ]);
    }
    
    /**
     * Remove the specified customization from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $user = Auth::user();
        
        // Only tenants can delete their own customizations
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can delete their own customizations'
            ], 403);
        }
        
        $customization = ThemeCustomization::where('id', $id)
            ->where('tenant_id', $user->tenant_id)
            ->first();
            
        if (!$customization) {
            return response()->json([
                'success' => false,
                'message' => 'Customization not found'
            ], 404);
        }
        
        $customization->delete();
        
        return response()->json([
            'success' => true,
            'message' => 'Customization deleted successfully'
        ]);
    }
    
    /**
     * Reset the customization to the original template.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function reset($id)
    {
        $user = Auth::user();
        
        // Only tenants can reset their own customizations
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can reset their own customizations'
            ], 403);
        }
        
        $customization = ThemeCustomization::where('id', $id)
            ->where('tenant_id', $user->tenant_id)
            ->first();
            
        if (!$customization) {
            return response()->json([
                'success' => false,
                'message' => 'Customization not found'
            ], 404);
        }
        
        $result = $customization->resetToTemplate();
        
        if (!$result) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reset customization. Template may not exist.'
            ], 400);
        }
        
        return response()->json([
            'success' => true,
            'message' => 'Customization reset to original template successfully',
            'data' => $customization
        ]);
    }
    
    /**
     * Create a customization from a template.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createFromTemplate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'template_id' => 'required|exists:theme_templates,id',
            'name' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'is_active' => 'nullable|boolean',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
        
        $user = Auth::user();
        
        // Only tenants can create customizations
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can create customizations'
            ], 403);
        }
        
        $template = ThemeTemplate::find($request->input('template_id'));
        
        if (!$template) {
            return response()->json([
                'success' => false,
                'message' => 'Template not found'
            ], 404);
        }
        
        $theme = Theme::find($template->theme_id);
        
        // Check if user has access to this theme
        if ($theme->tenant_id !== null && $theme->tenant_id !== $user->tenant_id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }
        
        // Create customization from template
        $overrides = [
            'name' => $request->input('name', $template->name),
            'description' => $request->input('description'),
            'is_active' => $request->input('is_active', true),
        ];
        
        $customization = $template->createCustomization($user->tenant_id, $overrides);
        
        return response()->json([
            'success' => true,
            'message' => 'Customization created from template successfully',
            'data' => $customization
        ], 201);
    }
}
