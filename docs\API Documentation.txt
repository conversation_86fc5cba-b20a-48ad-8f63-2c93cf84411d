📚 API Documentation

This document provides an overview of the API endpoints available in our LMS platform. The API follows RESTful principles and uses JSON for data exchange.

## 🔐 Authentication

All API requests (except public endpoints) require authentication using JWT tokens.

### Obtaining a Token

```
POST /api/auth/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password"
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "Bearer",
  "expires_in": 3600
}
```

### Using the Token

Include the token in the Authorization header:

```
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

## 👤 User Endpoints

### Get Current User

```
GET /api/user
```

**Response:**
```json
{
  "id": 1,
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "role": "student",
  "created_at": "2023-01-01T00:00:00.000000Z",
  "updated_at": "2023-01-01T00:00:00.000000Z"
}
```

### Update User Profile

```
PUT /api/user/profile
```

**Request Body:**
```json
{
  "name": "John Smith",
  "bio": "Learning enthusiast",
  "avatar": "base64_encoded_image"
}
```

## 📚 Course Endpoints

### List Courses

```
GET /api/courses
```

**Query Parameters:**
- `page`: Page number (default: 1)
- `per_page`: Items per page (default: 10)
- `category_id`: Filter by category
- `search`: Search term

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "title": "Introduction to Programming",
      "slug": "introduction-to-programming",
      "description": "Learn the basics of programming",
      "price": 29.99,
      "instructor": {
        "id": 2,
        "name": "Jane Smith"
      },
      "thumbnail": "https://example.com/thumbnails/course1.jpg",
      "created_at": "2023-01-01T00:00:00.000000Z"
    },
    // More courses...
  ],
  "meta": {
    "current_page": 1,
    "last_page": 5,
    "per_page": 10,
    "total": 48
  }
}
```

### Get Course Details

```
GET /api/courses/{id}
```

**Response:**
```json
{
  "id": 1,
  "title": "Introduction to Programming",
  "slug": "introduction-to-programming",
  "description": "Learn the basics of programming",
  "long_description": "Detailed description with HTML formatting",
  "price": 29.99,
  "instructor": {
    "id": 2,
    "name": "Jane Smith",
    "bio": "Experienced programmer and educator"
  },
  "thumbnail": "https://example.com/thumbnails/course1.jpg",
  "sections": [
    {
      "id": 1,
      "title": "Getting Started",
      "lessons": [
        {
          "id": 1,
          "title": "Introduction",
          "duration": 600,
          "type": "video"
        }
        // More lessons...
      ]
    }
    // More sections...
  ],
  "created_at": "2023-01-01T00:00:00.000000Z",
  "updated_at": "2023-01-01T00:00:00.000000Z"
}
```

### Create Course (Instructor/Admin)

```
POST /api/courses
```

**Request Body:**
```json
{
  "title": "New Course",
  "description": "Course description",
  "long_description": "Detailed description",
  "price": 49.99,
  "category_id": 2,
  "thumbnail": "base64_encoded_image"
}
```

### Update Course (Instructor/Admin)

```
PUT /api/courses/{id}
```

**Request Body:**
```json
{
  "title": "Updated Course Title",
  "description": "Updated description",
  "price": 39.99
}
```

### Delete Course (Instructor/Admin)

```
DELETE /api/courses/{id}
```

## 📝 Lesson Endpoints

### Get Lesson Details

```
GET /api/lessons/{id}
```

**Response:**
```json
{
  "id": 1,
  "title": "Introduction",
  "description": "Lesson description",
  "content_type": "video",
  "content_url": "https://example.com/videos/lesson1.mp4",
  "duration": 600,
  "course_id": 1,
  "section_id": 1,
  "created_at": "2023-01-01T00:00:00.000000Z",
  "updated_at": "2023-01-01T00:00:00.000000Z"
}
```

### Create Lesson (Instructor/Admin)

```
POST /api/courses/{course_id}/sections/{section_id}/lessons
```

**Request Body:**
```json
{
  "title": "New Lesson",
  "description": "Lesson description",
  "content_type": "video",
  "content_url": "https://example.com/videos/new-lesson.mp4",
  "duration": 450
}
```

## 📊 Enrollment Endpoints

### Enroll in Course

```
POST /api/courses/{course_id}/enroll
```

**Response:**
```json
{
  "message": "Successfully enrolled in course",
  "enrollment_id": 123
}
```

### Get User Enrollments

```
GET /api/enrollments
```

**Response:**
```json
{
  "data": [
    {
      "id": 123,
      "course": {
        "id": 1,
        "title": "Introduction to Programming",
        "thumbnail": "https://example.com/thumbnails/course1.jpg"
      },
      "progress": 25,
      "enrolled_at": "2023-01-15T00:00:00.000000Z"
    }
    // More enrollments...
  ]
}
```

## 🏆 Progress Tracking Endpoints

### Update Lesson Progress

```
POST /api/lessons/{lesson_id}/progress
```

**Request Body:**
```json
{
  "status": "completed",
  "progress_percentage": 100,
  "time_spent": 580
}
```

### Get Course Progress

```
GET /api/courses/{course_id}/progress
```

**Response:**
```json
{
  "course_id": 1,
  "overall_progress": 68,
  "completed_lessons": 15,
  "total_lessons": 22,
  "sections": [
    {
      "id": 1,
      "title": "Getting Started",
      "progress": 100,
      "lessons": [
        {
          "id": 1,
          "title": "Introduction",
          "status": "completed",
          "progress_percentage": 100
        }
        // More lessons...
      ]
    }
    // More sections...
  ]
}
```

## 📝 Quiz and Assessment Endpoints

### Get Quiz

```
GET /api/quizzes/{id}
```

**Response:**
```json
{
  "id": 1,
  "title": "Chapter 1 Quiz",
  "description": "Test your knowledge of Chapter 1",
  "time_limit": 1800,
  "pass_percentage": 70,
  "questions": [
    {
      "id": 1,
      "question": "What is programming?",
      "type": "multiple_choice",
      "options": [
        "Writing code",
        "Creating websites",
        "Giving instructions to a computer",
        "All of the above"
      ]
    }
    // More questions...
  ]
}
```

### Submit Quiz Answers

```
POST /api/quizzes/{id}/submit
```

**Request Body:**
```json
{
  "answers": [
    {
      "question_id": 1,
      "answer": 3
    },
    // More answers...
  ]
}
```

**Response:**
```json
{
  "score": 85,
  "passed": true,
  "correct_answers": 17,
  "total_questions": 20,
  "time_taken": 1200
}
```

## 🎨 Theme Endpoints (Tenant)

### List Available Themes

```
GET /api/themes
```

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "name": "Modern",
      "description": "A clean, modern theme",
      "thumbnail": "https://example.com/themes/modern.jpg",
      "is_premium": false,
      "is_installed": true,
      "is_active": true
    },
    // More themes...
  ]
}
```

### Activate Theme

```
POST /api/themes/{id}/activate
```

**Response:**
```json
{
  "message": "Theme activated successfully",
  "theme": {
    "id": 2,
    "name": "Classic",
    "is_active": true
  }
}
```

## 🧩 Module Endpoints (Tenant)

### List Available Modules

```
GET /api/modules
```

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "name": "Certificate Generator",
      "description": "Generate certificates for course completion",
      "thumbnail": "https://example.com/modules/certificates.jpg",
      "is_premium": true,
      "price": 19.99,
      "is_installed": false
    },
    // More modules...
  ]
}
```

### Install Module

```
POST /api/modules/{id}/install
```

**Response:**
```json
{
  "message": "Module installed successfully",
  "module": {
    "id": 1,
    "name": "Certificate Generator",
    "is_installed": true
  }
}
```

## 📱 Mobile App Configuration Endpoints (Tenant)

### Get App Configuration

```
GET /api/app-builder/configuration
```

**Response:**
```json
{
  "app_name": "My LMS App",
  "primary_color": "#4285F4",
  "secondary_color": "#34A853",
  "logo": "https://example.com/logo.png",
  "splash_screen": "https://example.com/splash.png",
  "features": {
    "offline_courses": true,
    "push_notifications": true,
    "social_sharing": true,
    "biometric_login": false
  }
}
```

### Update App Configuration

```
PUT /api/app-builder/configuration
```

**Request Body:**
```json
{
  "app_name": "Updated App Name",
  "primary_color": "#FF5722",
  "features": {
    "biometric_login": true
  }
}
```

## 📊 Analytics Endpoints (Tenant/Admin)

### Get Enrollment Statistics

```
GET /api/analytics/enrollments
```

**Query Parameters:**
- `period`: daily, weekly, monthly, yearly (default: monthly)
- `start_date`: Start date (format: YYYY-MM-DD)
- `end_date`: End date (format: YYYY-MM-DD)

**Response:**
```json
{
  "total_enrollments": 1250,
  "period": "monthly",
  "data": [
    {
      "date": "2023-01",
      "count": 120
    },
    {
      "date": "2023-02",
      "count": 145
    }
    // More data points...
  ]
}
```

### Get Revenue Statistics

```
GET /api/analytics/revenue
```

**Response:**
```json
{
  "total_revenue": 12500.75,
  "period": "monthly",
  "data": [
    {
      "date": "2023-01",
      "amount": 1200.50
    },
    {
      "date": "2023-02",
      "amount": 1450.25
    }
    // More data points...
  ]
}
```

## Error Handling

All API endpoints return appropriate HTTP status codes:

- 200: Success
- 201: Created
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 422: Validation Error
- 500: Server Error

Error responses include a message and, when applicable, validation errors:

```json
{
  "message": "The given data was invalid.",
  "errors": {
    "email": [
      "The email field is required."
    ],
    "password": [
      "The password must be at least 8 characters."
    ]
  }
}
```

## Rate Limiting

API requests are subject to rate limiting to prevent abuse. The current limits are:

- 60 requests per minute for authenticated users
- 30 requests per minute for unauthenticated users

Rate limit information is included in the response headers:

```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 58
X-RateLimit-Reset: 1609459200
```

## Versioning

The API is versioned to ensure backward compatibility. The current version is v1.

```
/api/v1/courses
```

Future versions will be available at `/api/v2/`, etc.
