<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('developer_analytics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('developer_account_id')->constrained()->onDelete('cascade');
            $table->foreignId('submission_id')->nullable()->constrained('developer_submissions')->nullOnDelete();
            $table->enum('type', ['theme', 'module'])->default('theme');
            $table->enum('event_type', ['view', 'install', 'purchase', 'uninstall', 'review'])->default('view');
            $table->decimal('revenue', 10, 2)->default(0.00);
            $table->json('metadata')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('developer_analytics');
    }
};
