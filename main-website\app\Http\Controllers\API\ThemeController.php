<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Theme;
use App\Models\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ThemeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $query = Theme::query();

        // Filter by tenant if specified
        if ($request->has('tenant_id')) {
            $query->where('tenant_id', $request->tenant_id);
        }

        // If user is a tenant, only show their themes and global themes
        if ($user->role === 'tenant') {
            $query->where(function($q) use ($user) {
                $q->where('tenant_id', $user->tenant_id)
                  ->orWhereNull('tenant_id');
            });
        }

        // Filter by active status if specified
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // Filter by premium status if specified
        if ($request->has('is_premium')) {
            $query->where('is_premium', $request->boolean('is_premium'));
        }

        // Filter by featured status if specified
        if ($request->has('is_featured')) {
            $query->where('is_featured', $request->boolean('is_featured'));
        }

        // Pagination
        $perPage = $request->input('per_page', 10);
        $themes = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $themes
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'version' => 'required|string|max:50',
            'author' => 'nullable|string|max:255',
            'thumbnail' => 'nullable|image|max:2048',
            'preview_url' => 'nullable|url',
            'price' => 'nullable|numeric|min:0',
            'settings' => 'nullable|json',
            'is_premium' => 'nullable|boolean',
            'is_featured' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();

        // Only admins and tenants can create themes
        if (!in_array($user->role, ['admin', 'tenant'])) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Create slug from name
        $slug = Str::slug($request->name);
        $count = 1;

        // Ensure slug is unique
        while (Theme::where('slug', $slug)->exists()) {
            $slug = Str::slug($request->name) . '-' . $count;
            $count++;
        }

        // Create the theme
        $theme = new Theme();
        $theme->name = $request->name;
        $theme->slug = $slug;
        $theme->description = $request->description;
        $theme->version = $request->version;
        $theme->author = $request->author;
        $theme->preview_url = $request->preview_url;
        $theme->price = $request->price ?? 0;
        $theme->is_premium = $request->boolean('is_premium', false);
        $theme->is_featured = $request->boolean('is_featured', false);
        $theme->is_active = false;

        // Handle thumbnail upload
        if ($request->hasFile('thumbnail')) {
            $path = $request->file('thumbnail')->store('themes/thumbnails', 'public');
            $theme->thumbnail = $path;
        }

        // Handle settings
        if ($request->has('settings')) {
            $theme->settings = json_decode($request->settings);
        }

        // Set tenant ID for tenant users
        if ($user->role === 'tenant') {
            $theme->tenant_id = $user->tenant_id;
        }

        $theme->save();

        return response()->json([
            'success' => true,
            'message' => 'Theme created successfully',
            'data' => $theme
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(string $id)
    {
        $theme = Theme::find($id);

        if (!$theme) {
            return response()->json([
                'success' => false,
                'message' => 'Theme not found'
            ], 404);
        }

        $user = Auth::user();

        // Check if user has access to this theme
        if ($user->role === 'tenant' && $theme->tenant_id !== null && $theme->tenant_id !== $user->tenant_id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $theme
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, string $id)
    {
        $theme = Theme::find($id);

        if (!$theme) {
            return response()->json([
                'success' => false,
                'message' => 'Theme not found'
            ], 404);
        }

        $user = Auth::user();

        // Check if user has permission to update this theme
        if ($user->role === 'tenant' && $theme->tenant_id !== $user->tenant_id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'version' => 'sometimes|required|string|max:50',
            'author' => 'nullable|string|max:255',
            'thumbnail' => 'nullable|image|max:2048',
            'preview_url' => 'nullable|url',
            'price' => 'nullable|numeric|min:0',
            'settings' => 'nullable|json',
            'is_premium' => 'nullable|boolean',
            'is_featured' => 'nullable|boolean',
            'is_active' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Update the theme
        if ($request->has('name')) {
            $theme->name = $request->name;

            // Update slug if name changes
            $slug = Str::slug($request->name);
            $count = 1;

            // Ensure slug is unique
            while (Theme::where('slug', $slug)->where('id', '!=', $id)->exists()) {
                $slug = Str::slug($request->name) . '-' . $count;
                $count++;
            }

            $theme->slug = $slug;
        }

        if ($request->has('description')) $theme->description = $request->description;
        if ($request->has('version')) $theme->version = $request->version;
        if ($request->has('author')) $theme->author = $request->author;
        if ($request->has('preview_url')) $theme->preview_url = $request->preview_url;
        if ($request->has('price')) $theme->price = $request->price;
        if ($request->has('is_premium')) $theme->is_premium = $request->boolean('is_premium');
        if ($request->has('is_featured')) $theme->is_featured = $request->boolean('is_featured');
        if ($request->has('is_active')) $theme->is_active = $request->boolean('is_active');

        // Handle thumbnail upload
        if ($request->hasFile('thumbnail')) {
            // Delete old thumbnail if exists
            if ($theme->thumbnail) {
                Storage::disk('public')->delete($theme->thumbnail);
            }

            $path = $request->file('thumbnail')->store('themes/thumbnails', 'public');
            $theme->thumbnail = $path;
        }

        // Handle settings
        if ($request->has('settings')) {
            $theme->settings = json_decode($request->settings);
        }

        $theme->save();

        return response()->json([
            'success' => true,
            'message' => 'Theme updated successfully',
            'data' => $theme
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(string $id)
    {
        $theme = Theme::find($id);

        if (!$theme) {
            return response()->json([
                'success' => false,
                'message' => 'Theme not found'
            ], 404);
        }

        $user = Auth::user();

        // Check if user has permission to delete this theme
        if ($user->role !== 'admin' && ($user->role !== 'tenant' || $theme->tenant_id !== $user->tenant_id)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Don't allow deleting active themes
        if ($theme->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete an active theme'
            ], 400);
        }

        // Delete thumbnail if exists
        if ($theme->thumbnail) {
            Storage::disk('public')->delete($theme->thumbnail);
        }

        $theme->delete();

        return response()->json([
            'success' => true,
            'message' => 'Theme deleted successfully'
        ]);
    }

    /**
     * Activate the specified theme for a tenant.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function activate(Request $request, string $id)
    {
        $theme = Theme::find($id);

        if (!$theme) {
            return response()->json([
                'success' => false,
                'message' => 'Theme not found'
            ], 404);
        }

        $user = Auth::user();

        // Check if user has permission to activate this theme
        if ($user->role === 'tenant' && $theme->tenant_id !== null && $theme->tenant_id !== $user->tenant_id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Get the tenant
        $tenant = null;
        if ($user->role === 'tenant') {
            $tenant = Tenant::find($user->tenant_id);
        } else if ($request->has('tenant_id')) {
            $tenant = Tenant::find($request->tenant_id);
        }

        if (!$tenant) {
            return response()->json([
                'success' => false,
                'message' => 'Tenant not found'
            ], 404);
        }

        // Deactivate all other themes for this tenant
        Theme::where('tenant_id', $tenant->id)
            ->where('id', '!=', $theme->id)
            ->update(['is_active' => false]);

        // Activate this theme
        $theme->is_active = true;
        $theme->save();

        // Update tenant's theme_id
        $tenant->theme_id = $theme->id;
        $tenant->save();

        return response()->json([
            'success' => true,
            'message' => 'Theme activated successfully',
            'data' => $theme
        ]);
    }

    /**
     * Install a theme for a tenant.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function install(Request $request, string $id)
    {
        $theme = Theme::find($id);

        if (!$theme) {
            return response()->json([
                'success' => false,
                'message' => 'Theme not found'
            ], 404);
        }

        $user = Auth::user();

        // Only tenants can install themes
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can install themes'
            ], 403);
        }

        // Check if theme is premium and requires payment
        if ($theme->is_premium && $theme->price > 0) {
            // In a real implementation, you would check if the user has purchased this theme
            // For now, we'll just allow the installation
        }

        // Create a copy of the theme for the tenant
        $tenantTheme = new Theme();
        $tenantTheme->name = $theme->name;
        $tenantTheme->slug = $theme->slug . '-' . $user->tenant_id;
        $tenantTheme->description = $theme->description;
        $tenantTheme->version = $theme->version;
        $tenantTheme->author = $theme->author;
        $tenantTheme->thumbnail = $theme->thumbnail;
        $tenantTheme->preview_url = $theme->preview_url;
        $tenantTheme->settings = $theme->settings;
        $tenantTheme->is_premium = false; // Once installed, it's not premium anymore
        $tenantTheme->is_featured = false;
        $tenantTheme->is_active = false; // Don't activate by default
        $tenantTheme->tenant_id = $user->tenant_id;
        $tenantTheme->save();

        return response()->json([
            'success' => true,
            'message' => 'Theme installed successfully',
            'data' => $tenantTheme
        ]);
    }
}
