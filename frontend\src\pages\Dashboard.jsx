import { useState, useEffect, useContext } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import { 
  Container, 
  Typography, 
  Box, 
  Grid, 
  Card, 
  CardContent, 
  CardMedia, 
  Button, 
  Tabs, 
  Tab, 
  CircularProgress, 
  LinearProgress, 
  Divider, 
  Paper 
} from '@mui/material';
import { AuthContext } from '../context/AuthContext';
import { courseService, paymentService } from '../services/api';

const Dashboard = () => {
  const [tabValue, setTabValue] = useState(0);
  const [enrolledCourses, setEnrolledCourses] = useState([]);
  const [payments, setPayments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  const { user } = useContext(AuthContext);

  useEffect(() => {
    fetchEnrolledCourses();
    fetchPaymentHistory();
  }, []);

  const fetchEnrolledCourses = async () => {
    try {
      setLoading(true);
      // In a real app, you would have an API endpoint to get enrolled courses
      // For now, we'll simulate it by getting all courses and filtering
      const response = await courseService.getAllCourses();
      // Filter courses that the user is enrolled in (this would be handled by the backend in a real app)
      const enrolled = response.data.data.filter(course => 
        course.enrollments && course.enrollments.some(enrollment => 
          enrollment.user_id === user.id
        )
      );
      setEnrolledCourses(enrolled);
      setError(null);
    } catch (error) {
      console.error('Error fetching enrolled courses:', error);
      setError('Failed to load your courses. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const fetchPaymentHistory = async () => {
    try {
      const response = await paymentService.getPaymentHistory();
      setPayments(response.data);
    } catch (error) {
      console.error('Error fetching payment history:', error);
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Welcome, {user?.name}!
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage your courses and track your learning progress.
        </Typography>
      </Paper>

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="dashboard tabs">
          <Tab label="My Courses" id="tab-0" />
          <Tab label="Payment History" id="tab-1" />
          <Tab label="Account Settings" id="tab-2" />
        </Tabs>
      </Box>

      {/* My Courses Tab */}
      <div role="tabpanel" hidden={tabValue !== 0}>
        {tabValue === 0 && (
          <Box>
            <Typography variant="h5" component="h2" gutterBottom>
              My Enrolled Courses
            </Typography>
            
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                <CircularProgress />
              </Box>
            ) : error ? (
              <Typography color="error" sx={{ my: 4 }}>
                {error}
              </Typography>
            ) : enrolledCourses.length === 0 ? (
              <Box sx={{ textAlign: 'center', my: 4 }}>
                <Typography variant="body1" paragraph>
                  You haven't enrolled in any courses yet.
                </Typography>
                <Button component={RouterLink} to="/" variant="contained">
                  Browse Courses
                </Button>
              </Box>
            ) : (
              <Grid container spacing={4}>
                {enrolledCourses.map((course) => {
                  const enrollment = course.enrollments.find(e => e.user_id === user.id);
                  const progress = enrollment ? enrollment.progress : 0;
                  
                  return (
                    <Grid item key={course.id} xs={12} md={6}>
                      <Card sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
                        <CardMedia
                          component="img"
                          height="140"
                          image={course.thumbnail || 'https://source.unsplash.com/random?education'}
                          alt={course.title}
                        />
                        <CardContent sx={{ flexGrow: 1 }}>
                          <Typography gutterBottom variant="h5" component="h2">
                            {course.title}
                          </Typography>
                          <Box sx={{ mt: 2, mb: 1 }}>
                            <Typography variant="body2" color="text.secondary">
                              Progress: {progress}%
                            </Typography>
                            <LinearProgress variant="determinate" value={progress} sx={{ mt: 1 }} />
                          </Box>
                          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between' }}>
                            <Button
                              component={RouterLink}
                              to={`/course/${course.id}`}
                              size="small"
                              color="primary"
                            >
                              View Details
                            </Button>
                            <Button
                              component={RouterLink}
                              to={`/course/${course.id}/learn`}
                              variant="contained"
                              size="small"
                            >
                              Continue Learning
                            </Button>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  );
                })}
              </Grid>
            )}
          </Box>
        )}
      </div>

      {/* Payment History Tab */}
      <div role="tabpanel" hidden={tabValue !== 1}>
        {tabValue === 1 && (
          <Box>
            <Typography variant="h5" component="h2" gutterBottom>
              Payment History
            </Typography>
            
            {payments.length === 0 ? (
              <Typography variant="body1" sx={{ my: 4, textAlign: 'center' }}>
                No payment records found.
              </Typography>
            ) : (
              <Paper elevation={2} sx={{ overflow: 'hidden' }}>
                <Box sx={{ p: 2, bgcolor: 'primary.main', color: 'white' }}>
                  <Grid container>
                    <Grid item xs={3}>
                      <Typography variant="subtitle1">Date</Typography>
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="subtitle1">Course</Typography>
                    </Grid>
                    <Grid item xs={2}>
                      <Typography variant="subtitle1">Amount</Typography>
                    </Grid>
                    <Grid item xs={3}>
                      <Typography variant="subtitle1">Status</Typography>
                    </Grid>
                  </Grid>
                </Box>
                <Divider />
                {payments.map((payment) => (
                  <Box key={payment.id} sx={{ p: 2 }}>
                    <Grid container alignItems="center">
                      <Grid item xs={3}>
                        <Typography variant="body2">
                          {new Date(payment.created_at).toLocaleDateString()}
                        </Typography>
                      </Grid>
                      <Grid item xs={4}>
                        <Typography variant="body2">
                          {payment.course.title}
                        </Typography>
                      </Grid>
                      <Grid item xs={2}>
                        <Typography variant="body2">
                          ${payment.amount}
                        </Typography>
                      </Grid>
                      <Grid item xs={3}>
                        <Typography 
                          variant="body2"
                          sx={{ 
                            color: payment.status === 'completed' ? 'success.main' : 
                                  payment.status === 'pending' ? 'warning.main' : 'error.main'
                          }}
                        >
                          {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                        </Typography>
                      </Grid>
                    </Grid>
                    <Divider sx={{ mt: 2 }} />
                  </Box>
                ))}
              </Paper>
            )}
          </Box>
        )}
      </div>

      {/* Account Settings Tab */}
      <div role="tabpanel" hidden={tabValue !== 2}>
        {tabValue === 2 && (
          <Box>
            <Typography variant="h5" component="h2" gutterBottom>
              Account Settings
            </Typography>
            <Paper elevation={2} sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Profile Information
              </Typography>
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={12} sm={3}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Name
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={9}>
                  <Typography variant="body1">
                    {user?.name}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Email
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={9}>
                  <Typography variant="body1">
                    {user?.email}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Role
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={9}>
                  <Typography variant="body1" sx={{ textTransform: 'capitalize' }}>
                    {user?.role}
                  </Typography>
                </Grid>
              </Grid>
              <Box sx={{ mt: 3 }}>
                <Button variant="contained" color="primary">
                  Edit Profile
                </Button>
                <Button variant="outlined" color="secondary" sx={{ ml: 2 }}>
                  Change Password
                </Button>
              </Box>
            </Paper>
          </Box>
        )}
      </div>
    </Container>
  );
};

export default Dashboard;
