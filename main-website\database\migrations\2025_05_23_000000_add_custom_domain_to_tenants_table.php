<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tenants', function (Blueprint $table) {
            if (!Schema::hasColumn('tenants', 'custom_domain')) {
                $table->string('custom_domain')->nullable()->after('domain');
            }
            
            if (!Schema::hasColumn('tenants', 'custom_domain_verified')) {
                $table->boolean('custom_domain_verified')->default(false)->after('custom_domain');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tenants', function (Blueprint $table) {
            if (Schema::hasColumn('tenants', 'custom_domain')) {
                $table->dropColumn('custom_domain');
            }
            
            if (Schema::hasColumn('tenants', 'custom_domain_verified')) {
                $table->dropColumn('custom_domain_verified');
            }
        });
    }
};
