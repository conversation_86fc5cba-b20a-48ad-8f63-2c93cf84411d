<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Socialite\Facades\Socialite;
use Mockery;
use Tests\TestCase;

class GoogleAuthTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test Google OAuth redirect.
     */
    public function test_google_redirect(): void
    {
        $response = $this->get('/api/auth/google');

        $response->assertStatus(302);
        $response->assertRedirect();
    }

    /**
     * Test Google OAuth callback with existing user.
     */
    public function test_google_callback_with_existing_user(): void
    {
        // Create a user that matches the Google user
        $user = User::factory()->create([
            'name' => 'Existing Google User',
            'email' => '<EMAIL>',
        ]);

        // Mock the Socialite facade
        $abstractUser = Mockery::mock('Laravel\Socialite\Two\User');
        $abstractUser->shouldReceive('getId')
            ->andReturn('123456')
            ->shouldReceive('getName')
            ->andReturn('Existing Google User')
            ->shouldR<PERSON>eive('getEmail')
            ->andReturn('<EMAIL>')
            ->shouldReceive('getAvatar')
            ->andReturn('https://example.com/avatar.jpg');

        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturn(Mockery::mock('Laravel\Socialite\Contracts\Provider'))
            ->getMock()
            ->shouldReceive('user')
            ->andReturn($abstractUser);

        $response = $this->get('/api/auth/google/callback');

        $response->assertStatus(302);
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
        ]);
    }

    /**
     * Test Google OAuth callback with new user.
     */
    public function test_google_callback_with_new_user(): void
    {
        // Mock the Socialite facade
        $abstractUser = Mockery::mock('Laravel\Socialite\Two\User');
        $abstractUser->shouldReceive('getId')
            ->andReturn('789012')
            ->shouldReceive('getName')
            ->andReturn('New Google User')
            ->shouldReceive('getEmail')
            ->andReturn('<EMAIL>')
            ->shouldReceive('getAvatar')
            ->andReturn('https://example.com/avatar.jpg');

        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturn(Mockery::mock('Laravel\Socialite\Contracts\Provider'))
            ->getMock()
            ->shouldReceive('user')
            ->andReturn($abstractUser);

        $response = $this->get('/api/auth/google/callback');

        $response->assertStatus(302);
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'role' => 'student',
        ]);
    }

    /**
     * Test Google OAuth callback with error.
     */
    public function test_google_callback_with_error(): void
    {
        // Mock the Socialite facade to throw an exception
        Socialite::shouldReceive('driver')
            ->with('google')
            ->andReturn(Mockery::mock('Laravel\Socialite\Contracts\Provider'))
            ->getMock()
            ->shouldReceive('user')
            ->andThrow(new \Exception('Google authentication failed'));

        $response = $this->get('/api/auth/google/callback');

        $response->assertStatus(302);
        $response->assertRedirect('/login');
        $response->assertSessionHas('error');
    }

    /**
     * Clean up the testing environment.
     */
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
