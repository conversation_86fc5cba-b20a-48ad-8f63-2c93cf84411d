<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DeveloperSubmission extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'developer_account_id',
        'type',
        'name',
        'slug',
        'version',
        'description',
        'features',
        'installation_instructions',
        'usage_instructions',
        'thumbnail',
        'screenshots',
        'demo_url',
        'source_code_url',
        'price',
        'category_id',
        'status',
        'rejection_reason',
        'test_results',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'screenshots' => 'array',
        'test_results' => 'array',
        'price' => 'decimal:2',
    ];

    /**
     * Get the developer account that owns the submission.
     */
    public function developerAccount(): BelongsTo
    {
        return $this->belongsTo(DeveloperAccount::class);
    }

    /**
     * Get the category that the submission belongs to.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(MarketplaceCategory::class, 'category_id');
    }

    /**
     * Get the analytics for the submission.
     */
    public function analytics(): HasMany
    {
        return $this->hasMany(DeveloperAnalytic::class, 'submission_id');
    }

    /**
     * Scope a query to only include submissions of a specific type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query to only include submissions with a specific status.
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include published submissions.
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    /**
     * Publish the submission to the marketplace.
     */
    public function publish(): bool
    {
        if ($this->status !== 'approved') {
            return false;
        }

        // Create a theme or module based on the submission type
        if ($this->type === 'theme') {
            $item = new Theme();
        } else {
            $item = new Module();
        }

        // Set the item properties
        $item->name = $this->name;
        $item->slug = $this->slug;
        $item->version = $this->version;
        $item->description = $this->description;
        $item->author = $this->developerAccount->company_name ?? $this->developerAccount->user->name;
        $item->author_url = $this->developerAccount->website;
        $item->thumbnail = $this->thumbnail;
        $item->category_id = $this->category_id;
        $item->price = $this->price;
        $item->screenshots = $this->screenshots;
        $item->demo_url = $this->demo_url;
        $item->status = 'approved';
        $item->is_system = false;
        $item->is_active = false;

        // Save the item
        $item->save();

        // Update the submission status
        $this->status = 'published';
        $this->save();

        return true;
    }
}
