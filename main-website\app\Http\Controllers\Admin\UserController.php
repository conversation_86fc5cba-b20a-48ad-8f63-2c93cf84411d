<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class UserController extends Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        // Check if user is admin for all methods
        $this->middleware(function ($request, $next) {
            if (!Auth::check() || !Auth::user()->isAdmin()) {
                return redirect()->route('login')->with('error', 'You do not have permission to access this area.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of the users.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        // Get filter parameters
        $role = $request->input('role');
        $status = $request->input('status');
        $search = $request->input('search');

        // Get users from database or use mock data if table doesn't exist yet
        try {
            $query = User::query();
            
            // Apply filters
            if ($role) {
                $query->where('role', $role);
            }
            
            if ($status) {
                $query->where('status', $status);
            }
            
            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
                });
            }
            
            $users = $query->with('tenant')->paginate(10);
            
            if ($users->isEmpty() && !$role && !$status && !$search) {
                $users = $this->getMockUsers();
            }
        } catch (\Exception $e) {
            $users = $this->getMockUsers();
        }

        // Stats for user counts
        $userStats = [
            'total' => User::count() ?: 100,
            'admin' => User::where('role', 'admin')->count() ?: 5,
            'tenant' => User::where('role', 'tenant')->count() ?: 45,
            'student' => User::where('role', 'student')->count() ?: 50,
        ];

        return view('admin.users.index', compact('users', 'userStats', 'role', 'status', 'search'));
    }

    /**
     * Show the form for creating a new user.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        // Get tenants for dropdown
        try {
            $tenants = Tenant::where('status', 'active')->get(['id', 'name']);
        } catch (\Exception $e) {
            $tenants = collect();
        }

        return view('admin.users.create', compact('tenants'));
    }

    /**
     * Store a newly created user in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:admin,tenant,student',
            'status' => 'required|in:active,inactive,pending',
            'tenant_id' => 'nullable|exists:tenants,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'role' => $request->role,
                'status' => $request->status,
                'tenant_id' => $request->tenant_id,
            ]);

            return redirect()->route('admin.users.index')
                ->with('success', 'User created successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to create user: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified user.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        try {
            $user = User::with('tenant')->findOrFail($id);
        } catch (\Exception $e) {
            // Use mock data if user not found
            $user = collect($this->getMockUsers())->firstWhere('id', $id);
            
            if (!$user) {
                return redirect()->route('admin.users.index')
                    ->with('error', 'User not found.');
            }
        }

        return view('admin.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified user.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        try {
            $user = User::findOrFail($id);
        } catch (\Exception $e) {
            // Use mock data if user not found
            $user = collect($this->getMockUsers())->firstWhere('id', $id);
            
            if (!$user) {
                return redirect()->route('admin.users.index')
                    ->with('error', 'User not found.');
            }
        }

        // Get tenants for dropdown
        try {
            $tenants = Tenant::where('status', 'active')->get(['id', 'name']);
        } catch (\Exception $e) {
            $tenants = collect();
        }

        return view('admin.users.edit', compact('user', 'tenants'));
    }

    /**
     * Update the specified user in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $id,
            'password' => 'nullable|string|min:8|confirmed',
            'role' => 'required|in:admin,tenant,student',
            'status' => 'required|in:active,inactive,pending',
            'tenant_id' => 'nullable|exists:tenants,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $user = User::findOrFail($id);
            
            $userData = [
                'name' => $request->name,
                'email' => $request->email,
                'role' => $request->role,
                'status' => $request->status,
                'tenant_id' => $request->tenant_id,
            ];
            
            // Only update password if provided
            if ($request->filled('password')) {
                $userData['password'] = Hash::make($request->password);
            }
            
            $user->update($userData);

            return redirect()->route('admin.users.show', $user->id)
                ->with('success', 'User updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update user: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified user from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        try {
            $user = User::findOrFail($id);
            
            // Prevent deleting self
            if ($user->id === Auth::id()) {
                return redirect()->back()
                    ->with('error', 'You cannot delete your own account.');
            }
            
            // Check if user is a tenant owner
            if ($user->role === 'tenant' && Tenant::where('owner_id', $user->id)->exists()) {
                return redirect()->back()
                    ->with('error', 'Cannot delete user who owns a tenant. Please transfer ownership or delete the tenant first.');
            }
            
            $user->delete();
            
            return redirect()->route('admin.users.index')
                ->with('success', 'User deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to delete user: ' . $e->getMessage());
        }
    }

    /**
     * Activate the specified user.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function activate($id)
    {
        try {
            $user = User::findOrFail($id);
            
            $user->update([
                'status' => 'active',
            ]);
            
            return redirect()->back()
                ->with('success', 'User activated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to activate user: ' . $e->getMessage());
        }
    }

    /**
     * Deactivate the specified user.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function deactivate($id)
    {
        try {
            $user = User::findOrFail($id);
            
            // Prevent deactivating self
            if ($user->id === Auth::id()) {
                return redirect()->back()
                    ->with('error', 'You cannot deactivate your own account.');
            }
            
            $user->update([
                'status' => 'inactive',
            ]);
            
            return redirect()->back()
                ->with('success', 'User deactivated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to deactivate user: ' . $e->getMessage());
        }
    }

    /**
     * Get mock users data.
     *
     * @return \Illuminate\Support\Collection
     */
    private function getMockUsers()
    {
        return collect([
            (object) [
                'id' => 1,
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'role' => 'admin',
                'status' => 'active',
                'tenant_id' => null,
                'tenant' => null,
                'created_at' => now()->subMonths(6),
                'updated_at' => now()->subMonths(6),
            ],
            (object) [
                'id' => 2,
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'role' => 'tenant',
                'status' => 'active',
                'tenant_id' => 1,
                'tenant' => (object) [
                    'id' => 1,
                    'name' => 'John\'s Academy',
                ],
                'created_at' => now()->subMonths(5),
                'updated_at' => now()->subMonths(5),
            ],
            (object) [
                'id' => 3,
                'name' => 'Jane Smith',
                'email' => '<EMAIL>',
                'role' => 'tenant',
                'status' => 'active',
                'tenant_id' => 2,
                'tenant' => (object) [
                    'id' => 2,
                    'name' => 'Smith Learning Center',
                ],
                'created_at' => now()->subMonths(4),
                'updated_at' => now()->subMonths(4),
            ],
            (object) [
                'id' => 4,
                'name' => 'Bob Johnson',
                'email' => '<EMAIL>',
                'role' => 'student',
                'status' => 'active',
                'tenant_id' => 1,
                'tenant' => (object) [
                    'id' => 1,
                    'name' => 'John\'s Academy',
                ],
                'created_at' => now()->subMonths(3),
                'updated_at' => now()->subMonths(3),
            ],
            (object) [
                'id' => 5,
                'name' => 'Alice Williams',
                'email' => '<EMAIL>',
                'role' => 'student',
                'status' => 'active',
                'tenant_id' => 2,
                'tenant' => (object) [
                    'id' => 2,
                    'name' => 'Smith Learning Center',
                ],
                'created_at' => now()->subMonths(2),
                'updated_at' => now()->subMonths(2),
            ],
            (object) [
                'id' => 6,
                'name' => 'Charlie Brown',
                'email' => '<EMAIL>',
                'role' => 'tenant',
                'status' => 'pending',
                'tenant_id' => 3,
                'tenant' => (object) [
                    'id' => 3,
                    'name' => 'Brown Education',
                ],
                'created_at' => now()->subWeeks(2),
                'updated_at' => now()->subWeeks(2),
            ],
            (object) [
                'id' => 7,
                'name' => 'David Miller',
                'email' => '<EMAIL>',
                'role' => 'student',
                'status' => 'inactive',
                'tenant_id' => 1,
                'tenant' => (object) [
                    'id' => 1,
                    'name' => 'John\'s Academy',
                ],
                'created_at' => now()->subMonths(1),
                'updated_at' => now()->subDays(5),
            ],
        ]);
    }
}
