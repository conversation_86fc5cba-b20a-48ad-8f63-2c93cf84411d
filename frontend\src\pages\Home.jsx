import { useState } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Button,
  TextField,
  InputAdornment,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import CourseList from '../components/CourseList';

const Home = () => {
  const [searchTerm, setSearchTerm] = useState('');

  return (
    <Box>
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #ff7700 0%, #cc5c00 100%)',
          color: 'white',
          py: { xs: 8, md: 12 },
          textAlign: 'center',
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            background: 'url(https://source.unsplash.com/random?education) center/cover no-repeat',
            opacity: 0.1,
            zIndex: 0,
          }
        }}
      >
        <Container maxWidth={false} sx={{ position: 'relative', zIndex: 1, px: { xs: 2, sm: 4, md: 6 }, maxWidth: '1600px' }}>
          <Typography variant="h2" component="h1" gutterBottom>
            Learn Anything, Anytime, Anywhere
          </Typography>
          <Typography variant="h5" component="p" paragraph>
            Discover thousands of courses from top instructors around the world.
          </Typography>
          <Box
            component="form"
            noValidate
            sx={{
              mt: 6,
              display: 'flex',
              justifyContent: 'center',
              width: '100%',
              maxWidth: '800px',
              mx: 'auto',
            }}
            onSubmit={(e) => {
              e.preventDefault();
              handleSearch();
            }}
          >
            <TextField
              variant="outlined"
              placeholder="Search for courses..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              sx={{
                width: '100%',
                bgcolor: 'white',
                borderRadius: 2,
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  '& fieldset': {
                    borderColor: 'transparent',
                  },
                  '&:hover fieldset': {
                    borderColor: 'transparent',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: 'transparent',
                    boxShadow: '0 0 0 3px rgba(37, 99, 235, 0.2)',
                  },
                },
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon color="primary" />
                  </InputAdornment>
                ),
              }}
            />
            <Button
              type="submit"
              variant="contained"
              color="secondary"
              sx={{
                ml: 1,
                px: 3,
                height: '56px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
              }}
            >
              Search
            </Button>
          </Box>
        </Container>
      </Box>

      {/* Courses Section */}
      <Container maxWidth={false} sx={{ py: { xs: 6, md: 10 }, px: { xs: 2, sm: 4, md: 6 }, maxWidth: '1600px' }}>
        <Typography variant="h4" component="h2" gutterBottom sx={{ fontWeight: 700, position: 'relative', display: 'inline-block', mb: 4 }}>
          Featured Courses
          <Box sx={{ position: 'absolute', bottom: -8, left: 0, width: '60%', height: 4, bgcolor: 'secondary.main', borderRadius: 2 }} />
        </Typography>

        <CourseList />
      </Container>

      {/* Call to Action Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #0369a1 0%, #075985 100%)',
          color: 'white',
          py: { xs: 8, md: 10 },
          textAlign: 'center',
          position: 'relative',
          mt: 8,
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            background: 'url(https://source.unsplash.com/random?teaching) center/cover no-repeat',
            opacity: 0.1,
            zIndex: 0,
          }
        }}
      >
        <Container maxWidth={false} sx={{ position: 'relative', zIndex: 1, px: { xs: 2, sm: 4, md: 6 }, maxWidth: '1600px' }}>
          <Typography variant="h4" component="h2" gutterBottom>
            Become an Instructor
          </Typography>
          <Typography variant="body1" paragraph>
            Share your knowledge with the world. Create online courses and earn money.
          </Typography>
          <Button
            component={RouterLink}
            to="/tenant/register"
            variant="contained"
            color="primary"
            size="large"
            sx={{
              mt: 3,
              py: 1.5,
              px: 4,
              fontSize: '1.1rem',
              fontWeight: 600,
              borderRadius: 3,
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
              },
            }}
          >
            Start Teaching Today
          </Button>
        </Container>
      </Box>
    </Box>
  );
};

export default Home;
