<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AppBuild extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'app_configuration_id',
        'tenant_id',
        'version',
        'build_number',
        'platform',
        'environment',
        'status',
        'build_log',
        'ios_build_url',
        'android_build_url',
        'build_metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'build_metadata' => 'array',
    ];

    /**
     * Get the app configuration that owns the build.
     */
    public function appConfiguration(): BelongsTo
    {
        return $this->belongsTo(AppConfiguration::class);
    }

    /**
     * Get the tenant that owns the build.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Scope a query to only include builds with a specific status.
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include builds for a specific platform.
     */
    public function scopeForPlatform($query, $platform)
    {
        if ($platform === 'both') {
            return $query;
        }
        
        return $query->where(function($q) use ($platform) {
            $q->where('platform', $platform)
              ->orWhere('platform', 'both');
        });
    }

    /**
     * Scope a query to only include builds for a specific environment.
     */
    public function scopeForEnvironment($query, $environment)
    {
        return $query->where('environment', $environment);
    }

    /**
     * Update the build status and log.
     */
    public function updateStatus($status, $log = null): void
    {
        $this->status = $status;
        
        if ($log) {
            $this->build_log = $this->build_log 
                ? $this->build_log . "\n\n" . $log 
                : $log;
        }
        
        $this->save();
        
        // Update the app configuration's last build status
        $this->appConfiguration->last_build_status = $status;
        $this->appConfiguration->last_build_at = now();
        $this->appConfiguration->last_build_log = $this->build_log;
        $this->appConfiguration->save();
    }
}
