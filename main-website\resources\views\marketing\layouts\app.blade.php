<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'LMS Platform') }} - @yield('title', 'Online Learning Platform')</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    <link rel="stylesheet" href="{{ asset('build/assets/app.css') }}">

    <style>
        :root {
            --primary-color: #ff7700;
            --primary-color-light: #ff9a40;
            --primary-color-dark: #cc5c00;
            --secondary-color: #0369a1;
            --secondary-color-light: #38bdf8;
            --secondary-color-dark: #075985;
        }

        /* Primary color utility classes */
        .bg-primary-50 { background-color: #fff7ed; }
        .bg-primary-100 { background-color: #ffedd5; }
        .bg-primary-200 { background-color: #fed7aa; }
        .bg-primary-300 { background-color: #fdba74; }
        .bg-primary-400 { background-color: #fb923c; }
        .bg-primary-500 { background-color: #ff7700; }
        .bg-primary-600 { background-color: #ea580c; }
        .bg-primary-700 { background-color: #cc5c00; }
        .bg-primary-800 { background-color: #9a3412; }
        .bg-primary-900 { background-color: #7c2d12; }

        .text-primary-50 { color: #fff7ed; }
        .text-primary-100 { color: #ffedd5; }
        .text-primary-200 { color: #fed7aa; }
        .text-primary-300 { color: #fdba74; }
        .text-primary-400 { color: #fb923c; }
        .text-primary-500 { color: #ff7700; }
        .text-primary-600 { color: #ea580c; }
        .text-primary-700 { color: #cc5c00; }
        .text-primary-800 { color: #9a3412; }
        .text-primary-900 { color: #7c2d12; }

        .border-primary-50 { border-color: #fff7ed; }
        .border-primary-100 { border-color: #ffedd5; }
        .border-primary-200 { border-color: #fed7aa; }
        .border-primary-300 { border-color: #fdba74; }
        .border-primary-400 { border-color: #fb923c; }
        .border-primary-500 { border-color: #ff7700; }
        .border-primary-600 { border-color: #ea580c; }
        .border-primary-700 { border-color: #cc5c00; }
        .border-primary-800 { border-color: #9a3412; }
        .border-primary-900 { border-color: #7c2d12; }

        .ring-primary-50 { --tw-ring-color: #fff7ed; }
        .ring-primary-100 { --tw-ring-color: #ffedd5; }
        .ring-primary-200 { --tw-ring-color: #fed7aa; }
        .ring-primary-300 { --tw-ring-color: #fdba74; }
        .ring-primary-400 { --tw-ring-color: #fb923c; }
        .ring-primary-500 { --tw-ring-color: #ff7700; }
        .ring-primary-600 { --tw-ring-color: #ea580c; }
        .ring-primary-700 { --tw-ring-color: #cc5c00; }
        .ring-primary-800 { --tw-ring-color: #9a3412; }
        .ring-primary-900 { --tw-ring-color: #7c2d12; }

        /* Secondary color utility classes */
        .bg-secondary-50 { background-color: #f0f9ff; }
        .bg-secondary-100 { background-color: #e0f2fe; }
        .bg-secondary-200 { background-color: #bae6fd; }
        .bg-secondary-300 { background-color: #7dd3fc; }
        .bg-secondary-400 { background-color: #38bdf8; }
        .bg-secondary-500 { background-color: #0ea5e9; }
        .bg-secondary-600 { background-color: #0369a1; }
        .bg-secondary-700 { background-color: #075985; }
        .bg-secondary-800 { background-color: #0c4a6e; }
        .bg-secondary-900 { background-color: #0c3d5e; }

        .text-secondary-50 { color: #f0f9ff; }
        .text-secondary-100 { color: #e0f2fe; }
        .text-secondary-200 { color: #bae6fd; }
        .text-secondary-300 { color: #7dd3fc; }
        .text-secondary-400 { color: #38bdf8; }
        .text-secondary-500 { color: #0ea5e9; }
        .text-secondary-600 { color: #0369a1; }
        .text-secondary-700 { color: #075985; }
        .text-secondary-800 { color: #0c4a6e; }
        .text-secondary-900 { color: #0c3d5e; }

        .border-secondary-50 { border-color: #f0f9ff; }
        .border-secondary-100 { border-color: #e0f2fe; }
        .border-secondary-200 { border-color: #bae6fd; }
        .border-secondary-300 { border-color: #7dd3fc; }
        .border-secondary-400 { border-color: #38bdf8; }
        .border-secondary-500 { border-color: #0ea5e9; }
        .border-secondary-600 { border-color: #0369a1; }
        .border-secondary-700 { border-color: #075985; }
        .border-secondary-800 { border-color: #0c4a6e; }
        .border-secondary-900 { border-color: #0c3d5e; }

        .ring-secondary-50 { --tw-ring-color: #f0f9ff; }
        .ring-secondary-100 { --tw-ring-color: #e0f2fe; }
        .ring-secondary-200 { --tw-ring-color: #bae6fd; }
        .ring-secondary-300 { --tw-ring-color: #7dd3fc; }
        .ring-secondary-400 { --tw-ring-color: #38bdf8; }
        .ring-secondary-500 { --tw-ring-color: #0ea5e9; }
        .ring-secondary-600 { --tw-ring-color: #0369a1; }
        .ring-secondary-700 { --tw-ring-color: #075985; }
        .ring-secondary-800 { --tw-ring-color: #0c4a6e; }
        .ring-secondary-900 { --tw-ring-color: #0c3d5e; }
    </style>

    <!-- Scripts -->
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</head>
<body class="font-sans antialiased">
    <div class="min-h-screen bg-white">
        @include('marketing.partials.header')

        <main>
            @yield('content')
        </main>

        @include('marketing.partials.footer')
    </div>


</body>
</html>
