<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Cookie;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\CourseController;
use App\Http\Controllers\API\LessonController;
use App\Http\Controllers\API\MarketplaceController;
use App\Http\Controllers\API\MarketplaceReviewController;
use App\Http\Controllers\API\PaymentController;
use App\Http\Controllers\API\TenantController;
use App\Http\Controllers\API\ThemeController;
use App\Http\Controllers\API\ModuleController;
use App\Http\Controllers\Auth\GoogleController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// CSRF Token route
Route::get('/csrf-token', function (Request $request) {
    $token = csrf_token();
    return response()->json(['token' => $token]);
});

/*
|--------------------------------------------------------------------------
| Authentication Routes
|--------------------------------------------------------------------------
*/

Route::prefix('auth')->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/register-tenant', [AuthController::class, 'registerTenant']);
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::post('/refresh', [AuthController::class, 'refresh']);
    Route::get('/profile', [AuthController::class, 'userProfile']);

    // Google OAuth routes
    Route::get('/google', [GoogleController::class, 'redirectToGoogle']);
    Route::get('/google/callback', [GoogleController::class, 'handleGoogleCallback']);
});

/*
|--------------------------------------------------------------------------
| Public API Routes
|--------------------------------------------------------------------------
*/

// Course routes (public)
Route::get('/courses', [CourseController::class, 'index']);
Route::get('/courses/{id}', [CourseController::class, 'show']);

// Tenant routes (public)
Route::get('/tenants', [TenantController::class, 'index']);
Route::get('/tenants/{id}', [TenantController::class, 'show']);

/*
|--------------------------------------------------------------------------
| Protected API Routes
|--------------------------------------------------------------------------
*/

Route::middleware('auth:api')->group(function () {
    // User profile
    Route::get('/user', function (Request $request) {
        return $request->user();
    });

    // Course management
    Route::post('/courses', [CourseController::class, 'store']);
    Route::put('/courses/{id}', [CourseController::class, 'update']);
    Route::delete('/courses/{id}', [CourseController::class, 'destroy']);
    Route::post('/courses/{id}/enroll', [CourseController::class, 'enroll']);
    Route::post('/courses/{id}/progress', [CourseController::class, 'updateProgress']);

    // Lesson management
    Route::apiResource('lessons', LessonController::class);

    // Tenant management
    Route::post('/tenants', [TenantController::class, 'store']);
    Route::put('/tenants/{id}', [TenantController::class, 'update']);
    Route::delete('/tenants/{id}', [TenantController::class, 'destroy']);
    Route::post('/tenants/{id}/approve', [TenantController::class, 'approve']);

    // Payment routes
    Route::post('/payments/create-order', [PaymentController::class, 'createOrder']);
    Route::post('/payments/verify', [PaymentController::class, 'verifyPayment']);
    Route::get('/payments/history', [PaymentController::class, 'history']);

    // Theme management
    Route::apiResource('themes', ThemeController::class);
    Route::post('/themes/{id}/activate', [ThemeController::class, 'activate']);
    Route::post('/themes/{id}/install', [ThemeController::class, 'install']);

    // Module management
    Route::apiResource('modules', ModuleController::class);
    Route::post('/modules/{id}/install', [ModuleController::class, 'install']);
    Route::post('/modules/{id}/uninstall', [ModuleController::class, 'uninstall']);
    Route::post('/modules/{id}/enable', [ModuleController::class, 'enable']);
    Route::post('/modules/{id}/disable', [ModuleController::class, 'disable']);

    // Marketplace
    Route::get('/marketplace/featured', [MarketplaceController::class, 'featured']);
    Route::get('/marketplace/themes', [MarketplaceController::class, 'themes']);
    Route::get('/marketplace/modules', [MarketplaceController::class, 'modules']);
    Route::get('/marketplace/themes/{id}', [MarketplaceController::class, 'showTheme']);
    Route::get('/marketplace/modules/{id}', [MarketplaceController::class, 'showModule']);
    Route::get('/marketplace/installed/themes', [MarketplaceController::class, 'installedThemes']);
    Route::get('/marketplace/installed/modules', [MarketplaceController::class, 'installedModules']);

    // Marketplace Reviews
    Route::get('/marketplace/themes/{id}/reviews', [MarketplaceReviewController::class, 'themeReviews']);
    Route::get('/marketplace/modules/{id}/reviews', [MarketplaceReviewController::class, 'moduleReviews']);
    Route::post('/marketplace/themes/{id}/reviews', [MarketplaceReviewController::class, 'createThemeReview']);
    Route::post('/marketplace/modules/{id}/reviews', [MarketplaceReviewController::class, 'createModuleReview']);
    Route::put('/marketplace/reviews/{id}', [MarketplaceReviewController::class, 'updateReview']);
    Route::delete('/marketplace/reviews/{id}', [MarketplaceReviewController::class, 'deleteReview']);
});
