import React, { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  Divider,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Rating,
  TextField,
  CircularProgress,
  Alert,
  Paper,
  ImageList,
  ImageListItem,
  Avatar,
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  ShoppingCart as ShoppingCartIcon,
  Download as DownloadIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  Person as PersonIcon,
  CalendarToday as CalendarTodayIcon,
  Code as CodeIcon,
  Info as InfoIcon,
  Description as DescriptionIcon,
  Comment as CommentIcon,
} from '@mui/icons-material';
import { AuthContext } from '../context/AuthContext';
import { marketplaceService, themeService, moduleService } from '../services/api';

const MarketplaceItemDetail = () => {
  const { type, id } = useParams();
  const navigate = useNavigate();
  const { isAuthenticated, user } = useContext(AuthContext);

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [item, setItem] = useState(null);
  const [reviews, setReviews] = useState([]);
  const [tabValue, setTabValue] = useState(0);
  const [userReview, setUserReview] = useState({
    rating: 0,
    comment: '',
  });
  const [reviewSubmitting, setReviewSubmitting] = useState(false);
  const [reviewError, setReviewError] = useState(null);

  useEffect(() => {
    const fetchItemDetails = async () => {
      try {
        setLoading(true);

        // Determine which service to use based on the type
        const service = type === 'theme' ? themeService : moduleService;
        const response = await service.getThemeById(id);

        setItem(response.data);
        setError(null);
      } catch (error) {
        console.error(`Error fetching ${type} details:`, error);
        setError(`Failed to load ${type} details. Please try again later.`);
      } finally {
        setLoading(false);
      }
    };

    fetchItemDetails();
  }, [type, id]);

  useEffect(() => {
    const fetchReviews = async () => {
      if (!item) return;

      try {
        const response = await marketplaceService.getReviews({
          reviewable_type: type,
          reviewable_id: id,
        });

        setReviews(response.data.data.data || []);
      } catch (error) {
        console.error('Error fetching reviews:', error);
      }
    };

    fetchReviews();
  }, [item, type, id]);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleReviewChange = (event) => {
    const { name, value } = event.target;
    setUserReview(prev => ({ ...prev, [name]: value }));
  };

  const handleRatingChange = (event, newValue) => {
    setUserReview(prev => ({ ...prev, rating: newValue }));
  };

  const handleReviewSubmit = async (event) => {
    event.preventDefault();

    if (!isAuthenticated()) {
      navigate('/login', { state: { from: `/marketplace/${type}/${id}` } });
      return;
    }

    if (userReview.rating === 0) {
      setReviewError('Please select a rating');
      return;
    }

    try {
      setReviewSubmitting(true);
      setReviewError(null);

      await marketplaceService.submitReview({
        reviewable_type: type,
        reviewable_id: id,
        rating: userReview.rating,
        comment: userReview.comment,
      });

      // Reset form
      setUserReview({ rating: 0, comment: '' });

      // Show success message
      alert('Review submitted successfully and pending approval');
    } catch (error) {
      console.error('Error submitting review:', error);
      setReviewError('Failed to submit review. Please try again later.');
    } finally {
      setReviewSubmitting(false);
    }
  };

  const handleInstall = async () => {
    if (!isAuthenticated()) {
      navigate('/login', { state: { from: `/marketplace/${type}/${id}` } });
      return;
    }

    try {
      setLoading(true);

      if (type === 'theme') {
        await marketplaceService.installTheme(id);
      } else {
        await marketplaceService.installModule(id);
      }

      alert(`${type.charAt(0).toUpperCase() + type.slice(1)} installed successfully!`);

      // Redirect to appropriate management page
      navigate(type === 'theme' ? '/themes' : '/modules');
    } catch (error) {
      console.error(`Error installing ${type}:`, error);
      setError(`Failed to install ${type}. Please try again later.`);
    } finally {
      setLoading(false);
    }
  };

  const handlePurchase = async () => {
    if (!isAuthenticated()) {
      navigate('/login', { state: { from: `/marketplace/${type}/${id}` } });
      return;
    }

    try {
      setLoading(true);

      if (type === 'theme') {
        await marketplaceService.purchaseTheme(id);
      } else {
        await marketplaceService.purchaseModule(id);
      }

      alert(`${type.charAt(0).toUpperCase() + type.slice(1)} purchased successfully!`);

      // Redirect to appropriate management page
      navigate(type === 'theme' ? '/themes' : '/modules');
    } catch (error) {
      console.error(`Error purchasing ${type}:`, error);
      setError(`Failed to purchase ${type}. Please try again later.`);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error">{error}</Alert>
      </Container>
    );
  }

  if (!item) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="warning">Item not found</Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Grid container spacing={4}>
        {/* Item Header */}
        <Grid item xs={12}>
          <Box sx={{ mb: 4 }}>
            <Typography variant="h3" component="h1" gutterBottom>
              {item.name}
            </Typography>
            <Box display="flex" alignItems="center" sx={{ mb: 2 }}>
              <Rating value={item.average_rating || 0} precision={0.5} readOnly />
              <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                ({item.reviews_count || 0} reviews)
              </Typography>
              {item.is_featured && (
                <Chip
                  label="Featured"
                  color="primary"
                  size="small"
                  sx={{ ml: 2 }}
                />
              )}
              <Chip
                label={type.charAt(0).toUpperCase() + type.slice(1)}
                color={type === 'theme' ? 'secondary' : 'info'}
                size="small"
                sx={{ ml: 2 }}
              />
            </Box>
          </Box>
        </Grid>

        {/* Item Image */}
        <Grid item xs={12} md={6}>
          <Box sx={{ position: 'relative', mb: 4 }}>
            <img
              src={item.thumbnail || `https://source.unsplash.com/random?${type}`}
              alt={item.name}
              style={{ width: '100%', borderRadius: 8, boxShadow: '0 4px 12px rgba(0,0,0,0.1)' }}
            />
          </Box>

          {/* Screenshots */}
          {item.screenshots && item.screenshots.length > 0 && (
            <Paper sx={{ p: 2, mb: 4 }}>
              <Typography variant="h6" gutterBottom>
                Screenshots
              </Typography>
              <ImageList cols={3} gap={8}>
                {item.screenshots.map((screenshot, index) => (
                  <ImageListItem key={index}>
                    <img
                      src={screenshot}
                      alt={`Screenshot ${index + 1}`}
                      loading="lazy"
                      style={{ borderRadius: 4 }}
                    />
                  </ImageListItem>
                ))}
              </ImageList>
            </Paper>
          )}
        </Grid>

        {/* Item Details */}
        <Grid item xs={12} md={6}>
          <Card sx={{ mb: 4 }}>
            <CardContent>
              <Box sx={{ mb: 3 }}>
                <Typography variant="h4" color="primary" gutterBottom>
                  {item.price > 0 ? `$${item.price.toFixed(2)}` : 'Free'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {item.downloads} downloads
                </Typography>
              </Box>

              <Box sx={{ mb: 3 }}>
                {item.price > 0 ? (
                  <Button
                    fullWidth
                    variant="contained"
                    size="large"
                    startIcon={<ShoppingCartIcon />}
                    onClick={handlePurchase}
                  >
                    Purchase Now
                  </Button>
                ) : (
                  <Button
                    fullWidth
                    variant="contained"
                    size="large"
                    startIcon={<DownloadIcon />}
                    onClick={handleInstall}
                  >
                    Install Now
                  </Button>
                )}
              </Box>

              <Divider sx={{ my: 3 }} />

              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <PersonIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Author"
                    secondary={item.author || 'Unknown'}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <CalendarTodayIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Last Updated"
                    secondary={new Date(item.updated_at).toLocaleDateString()}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <CodeIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Version"
                    secondary={item.version || '1.0.0'}
                  />
                </ListItem>
                {item.category && (
                  <ListItem>
                    <ListItemIcon>
                      <InfoIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Category"
                      secondary={item.category.name}
                    />
                  </ListItem>
                )}
              </List>

              {item.demo_url && (
                <Box sx={{ mt: 3 }}>
                  <Button
                    fullWidth
                    variant="outlined"
                    href={item.demo_url}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    View Demo
                  </Button>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Tabs Section */}
        <Grid item xs={12}>
          <Box sx={{ width: '100%', mb: 4 }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs value={tabValue} onChange={handleTabChange} aria-label="item tabs">
                <Tab icon={<DescriptionIcon />} iconPosition="start" label="Description" id="tab-0" />
                <Tab
                  icon={<CommentIcon />}
                  iconPosition="start"
                  label={`Reviews (${item.reviews_count || 0})`}
                  id="tab-1"
                />
              </Tabs>
            </Box>

            {/* Description Tab */}
            <Box role="tabpanel" hidden={tabValue !== 0} id="tabpanel-0" sx={{ py: 3 }}>
              {tabValue === 0 && (
                <Typography variant="body1" component="div">
                  {item.description || 'No description available.'}
                </Typography>
              )}
            </Box>

            {/* Reviews Tab */}
            <Box role="tabpanel" hidden={tabValue !== 1} id="tabpanel-1" sx={{ py: 3 }}>
              {tabValue === 1 && (
                <>
                  {/* Review Form */}
                  {isAuthenticated() && (
                    <Paper sx={{ p: 3, mb: 4 }}>
                      <Typography variant="h6" gutterBottom>
                        Write a Review
                      </Typography>
                      <Box component="form" onSubmit={handleReviewSubmit}>
                        <Box sx={{ mb: 2 }}>
                          <Typography component="legend">Your Rating</Typography>
                          <Rating
                            name="rating"
                            value={userReview.rating}
                            onChange={handleRatingChange}
                            precision={1}
                          />
                        </Box>
                        <TextField
                          fullWidth
                          multiline
                          rows={4}
                          name="comment"
                          label="Your Review"
                          value={userReview.comment}
                          onChange={handleReviewChange}
                          sx={{ mb: 2 }}
                        />
                        {reviewError && (
                          <Alert severity="error" sx={{ mb: 2 }}>
                            {reviewError}
                          </Alert>
                        )}
                        <Button
                          type="submit"
                          variant="contained"
                          disabled={reviewSubmitting}
                        >
                          {reviewSubmitting ? <CircularProgress size={24} /> : 'Submit Review'}
                        </Button>
                      </Box>
                    </Paper>
                  )}

                  {/* Reviews List */}
                  {reviews.length > 0 ? (
                    reviews.map((review) => (
                      <Paper key={review.id} sx={{ p: 3, mb: 2 }}>
                        <Box display="flex" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
                          <Box display="flex" alignItems="center">
                            <Avatar alt={review.user.name} src="/static/images/avatar/1.jpg" sx={{ mr: 2 }} />
                            <Typography variant="subtitle1">
                              {review.user.name}
                            </Typography>
                          </Box>
                          <Rating value={review.rating} readOnly />
                        </Box>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                          {new Date(review.created_at).toLocaleDateString()}
                        </Typography>
                        <Typography variant="body1">
                          {review.comment}
                        </Typography>
                      </Paper>
                    ))
                  ) : (
                    <Alert severity="info">
                      No reviews yet. Be the first to review this {type}!
                    </Alert>
                  )}
                </>
              )}
            </Box>
          </Box>
        </Grid>
      </Grid>
    </Container>
  );
};

export default MarketplaceItemDetail;
