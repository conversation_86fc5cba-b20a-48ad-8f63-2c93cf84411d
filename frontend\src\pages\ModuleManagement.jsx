import { useState, useEffect, useContext } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Grid, 
  Card, 
  CardContent, 
  CardMedia, 
  CardActions, 
  Button, 
  Chip, 
  Dialog, 
  DialogActions, 
  DialogContent, 
  DialogContentText, 
  DialogTitle, 
  TextField, 
  CircularProgress, 
  Alert, 
  Divider,
  Paper,
  Tabs,
  Tab,
  IconButton,
  Tooltip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import { styled } from '@mui/material/styles';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import ExtensionIcon from '@mui/icons-material/Extension';
import DependencyIcon from '@mui/icons-material/Link';
import { moduleService } from '../services/api';
import { AuthContext } from '../context/AuthContext';

const StyledCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: '0 12px 20px rgba(0, 0, 0, 0.1)',
  },
  position: 'relative',
  overflow: 'hidden',
  borderRadius: theme.shape.borderRadius * 2,
}));

const ModuleManagement = () => {
  const { user } = useContext(AuthContext);
  const [modules, setModules] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [moduleFile, setModuleFile] = useState(null);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [uploadError, setUploadError] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedModule, setSelectedModule] = useState(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);

  useEffect(() => {
    fetchModules();
  }, []);

  const fetchModules = async () => {
    try {
      setLoading(true);
      const params = {};
      
      // If user is a tenant, only fetch their modules and global modules
      if (user.role === 'tenant') {
        params.tenant_id = user.tenant_id;
      }
      
      const response = await moduleService.getAllModules(params);
      setModules(response.data.data);
      setError(null);
    } catch (error) {
      console.error('Error fetching modules:', error);
      setError('Failed to load modules. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleUploadDialogOpen = () => {
    setUploadDialogOpen(true);
    setModuleFile(null);
    setUploadError(null);
  };

  const handleUploadDialogClose = () => {
    setUploadDialogOpen(false);
  };

  const handleFileChange = (event) => {
    setModuleFile(event.target.files[0]);
  };

  const handleUploadModule = async () => {
    if (!moduleFile) {
      setUploadError('Please select a module file to upload.');
      return;
    }

    try {
      setUploadLoading(true);
      setUploadError(null);

      const formData = new FormData();
      formData.append('module_file', moduleFile);
      
      // If user is a tenant, add tenant_id
      if (user.role === 'tenant') {
        formData.append('tenant_id', user.tenant_id);
      }

      // In a real implementation, you would have an endpoint to upload and install the module
      // For now, we'll simulate it with a timeout
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Refresh the modules list
      await fetchModules();
      
      setUploadDialogOpen(false);
    } catch (error) {
      console.error('Error uploading module:', error);
      setUploadError('Failed to upload module. Please try again later.');
    } finally {
      setUploadLoading(false);
    }
  };

  const handleDeleteDialogOpen = (module) => {
    setSelectedModule(module);
    setDeleteDialogOpen(true);
  };

  const handleDeleteDialogClose = () => {
    setDeleteDialogOpen(false);
    setSelectedModule(null);
  };

  const handleDeleteModule = async () => {
    if (!selectedModule) return;

    try {
      await moduleService.deleteModule(selectedModule.id);
      
      // Update the modules list
      setModules(modules.filter(module => module.id !== selectedModule.id));
      
      setDeleteDialogOpen(false);
      setSelectedModule(null);
    } catch (error) {
      console.error('Error deleting module:', error);
      setError('Failed to delete module. Please try again later.');
    }
  };

  const handleActivateModule = async (module) => {
    try {
      await moduleService.activateModule(module.id);
      
      // Update the modules list
      setModules(modules.map(m => 
        m.id === module.id ? { ...m, is_active: true } : m
      ));
    } catch (error) {
      console.error('Error activating module:', error);
      setError('Failed to activate module. Please try again later.');
    }
  };

  const handleDeactivateModule = async (module) => {
    try {
      await moduleService.deactivateModule(module.id);
      
      // Update the modules list
      setModules(modules.map(m => 
        m.id === module.id ? { ...m, is_active: false } : m
      ));
    } catch (error) {
      console.error('Error deactivating module:', error);
      setError('Failed to deactivate module. Please try again later.');
    }
  };

  const handleDetailsDialogOpen = (module) => {
    setSelectedModule(module);
    setDetailsDialogOpen(true);
  };

  const handleDetailsDialogClose = () => {
    setDetailsDialogOpen(false);
    setSelectedModule(null);
  };

  const filteredModules = modules.filter(module => {
    if (tabValue === 0) return true; // All modules
    if (tabValue === 1) return module.tenant_id === null; // Global modules
    if (tabValue === 2) return module.tenant_id !== null; // Tenant modules
    if (tabValue === 3) return module.is_active; // Active modules
    return true;
  });

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Module Management
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<UploadFileIcon />}
          onClick={handleUploadDialogOpen}
        >
          Upload Module
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ mb: 4 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          centered
        >
          <Tab label="All Modules" />
          <Tab label="Global Modules" />
          <Tab label="Tenant Modules" />
          <Tab label="Active Modules" />
        </Tabs>
      </Paper>

      {filteredModules.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            No modules found
          </Typography>
          <Typography variant="body1">
            {tabValue === 0 ? 'There are no modules installed.' : 
             tabValue === 1 ? 'There are no global modules installed.' : 
             tabValue === 2 ? 'There are no tenant-specific modules installed.' :
             'There are no active modules.'}
          </Typography>
        </Paper>
      ) : (
        <Grid container spacing={4}>
          {filteredModules.map((module) => (
            <Grid item key={module.id} xs={12} sm={6} md={4} lg={3}>
              <StyledCard>
                {module.is_active && (
                  <Chip
                    label="Active"
                    color="primary"
                    size="small"
                    sx={{
                      position: 'absolute',
                      top: 16,
                      right: 16,
                      zIndex: 1,
                    }}
                  />
                )}
                <CardMedia
                  component="img"
                  height="180"
                  image={module.thumbnail || `https://source.unsplash.com/random?tech&sig=${module.id}`}
                  alt={module.name}
                />
                <CardContent sx={{ flexGrow: 1 }}>
                  <Typography variant="h6" component="div" gutterBottom>
                    {module.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Version: {module.version}
                  </Typography>
                  {module.author && (
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      By: {module.author}
                    </Typography>
                  )}
                  <Divider sx={{ my: 1.5 }} />
                  <Typography variant="body2" color="text.secondary">
                    {module.description || 'No description available.'}
                  </Typography>
                </CardContent>
                <CardActions sx={{ p: 2, pt: 0 }}>
                  {module.is_active ? (
                    <Button
                      size="small"
                      variant="outlined"
                      color="warning"
                      onClick={() => handleDeactivateModule(module)}
                      startIcon={<CancelIcon />}
                    >
                      Deactivate
                    </Button>
                  ) : (
                    <Button
                      size="small"
                      variant="contained"
                      color="primary"
                      onClick={() => handleActivateModule(module)}
                      startIcon={<CheckCircleIcon />}
                    >
                      Activate
                    </Button>
                  )}
                  <Box sx={{ ml: 'auto', display: 'flex', gap: 1 }}>
                    <Tooltip title="Details">
                      <IconButton
                        size="small"
                        color="primary"
                        onClick={() => handleDetailsDialogOpen(module)}
                      >
                        <ExtensionIcon />
                      </IconButton>
                    </Tooltip>
                    {!module.is_active && !module.is_system && (
                      <Tooltip title="Delete">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteDialogOpen(module)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                  </Box>
                </CardActions>
              </StyledCard>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Upload Module Dialog */}
      <Dialog open={uploadDialogOpen} onClose={handleUploadDialogClose}>
        <DialogTitle>Upload Module</DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ mb: 2 }}>
            Upload a module package (.zip) to install it. The module package should contain a module.json file with metadata and all necessary assets.
          </DialogContentText>
          {uploadError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {uploadError}
            </Alert>
          )}
          <Button
            variant="outlined"
            component="label"
            startIcon={<UploadFileIcon />}
            sx={{ mb: 2 }}
          >
            Select Module File
            <input
              type="file"
              accept=".zip"
              hidden
              onChange={handleFileChange}
            />
          </Button>
          {moduleFile && (
            <Typography variant="body2" sx={{ mt: 1 }}>
              Selected file: {moduleFile.name}
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleUploadDialogClose} disabled={uploadLoading}>
            Cancel
          </Button>
          <Button
            onClick={handleUploadModule}
            variant="contained"
            color="primary"
            disabled={uploadLoading || !moduleFile}
            startIcon={uploadLoading ? <CircularProgress size={20} /> : null}
          >
            {uploadLoading ? 'Uploading...' : 'Upload'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Module Dialog */}
      <Dialog open={deleteDialogOpen} onClose={handleDeleteDialogClose}>
        <DialogTitle>Delete Module</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the module "{selectedModule?.name}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteDialogClose}>
            Cancel
          </Button>
          <Button
            onClick={handleDeleteModule}
            variant="contained"
            color="error"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Module Details Dialog */}
      <Dialog
        open={detailsDialogOpen}
        onClose={handleDetailsDialogClose}
        maxWidth="sm"
        fullWidth
      >
        {selectedModule && (
          <>
            <DialogTitle>{selectedModule.name} Details</DialogTitle>
            <DialogContent>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Version
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {selectedModule.version}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Status
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {selectedModule.is_active ? (
                      <Box sx={{ display: 'flex', alignItems: 'center', color: 'success.main' }}>
                        <CheckCircleIcon fontSize="small" sx={{ mr: 0.5 }} />
                        Active
                      </Box>
                    ) : (
                      <Box sx={{ display: 'flex', alignItems: 'center', color: 'text.secondary' }}>
                        <CancelIcon fontSize="small" sx={{ mr: 0.5 }} />
                        Inactive
                      </Box>
                    )}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Description
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {selectedModule.description || 'No description available.'}
                  </Typography>
                </Grid>
                {selectedModule.author && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Author
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                      {selectedModule.author}
                    </Typography>
                  </Grid>
                )}
                {selectedModule.author_url && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Author URL
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                      <a href={selectedModule.author_url} target="_blank" rel="noopener noreferrer">
                        {selectedModule.author_url}
                      </a>
                    </Typography>
                  </Grid>
                )}
                {selectedModule.dependencies && Object.keys(selectedModule.dependencies).length > 0 && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Dependencies
                    </Typography>
                    <List dense>
                      {Object.entries(selectedModule.dependencies).map(([name, version]) => (
                        <ListItem key={name}>
                          <ListItemIcon>
                            <DependencyIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary={name}
                            secondary={`Version: ${version}`}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Grid>
                )}
                {selectedModule.hooks && Object.keys(selectedModule.hooks).length > 0 && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Hooks
                    </Typography>
                    <List dense>
                      {Object.keys(selectedModule.hooks).map((hook) => (
                        <ListItem key={hook}>
                          <ListItemIcon>
                            <ExtensionIcon />
                          </ListItemIcon>
                          <ListItemText primary={hook} />
                        </ListItem>
                      ))}
                    </List>
                  </Grid>
                )}
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleDetailsDialogClose}>
                Close
              </Button>
              {selectedModule.is_active ? (
                <Button
                  onClick={() => {
                    handleDeactivateModule(selectedModule);
                    handleDetailsDialogClose();
                  }}
                  variant="outlined"
                  color="warning"
                >
                  Deactivate
                </Button>
              ) : (
                <Button
                  onClick={() => {
                    handleActivateModule(selectedModule);
                    handleDetailsDialogClose();
                  }}
                  variant="contained"
                  color="primary"
                >
                  Activate
                </Button>
              )}
            </DialogActions>
          </>
        )}
      </Dialog>
    </Container>
  );
};

export default ModuleManagement;
