<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ThemesController extends Controller
{
    /**
     * Display the themes page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $themes = [
            [
                'name' => 'Modern',
                'description' => 'A clean, modern theme with a focus on readability and user experience.',
                'price' => 0,
                'is_free' => true,
                'thumbnail' => 'https://source.unsplash.com/random/800x600/?website,modern',
                'features' => [
                    'Responsive design',
                    'Light and dark mode',
                    'Customizable colors',
                    'Modern typography',
                ],
                'popular' => true,
            ],
            [
                'name' => 'Classic',
                'description' => 'A traditional academic theme that focuses on content and structure.',
                'price' => 0,
                'is_free' => true,
                'thumbnail' => 'https://source.unsplash.com/random/800x600/?website,classic',
                'features' => [
                    'Responsive design',
                    'Traditional layout',
                    'Customizable colors',
                    'Serif typography',
                ],
                'popular' => false,
            ],
            [
                'name' => 'Minimal',
                'description' => 'A minimalist theme that puts the focus on your content.',
                'price' => 0,
                'is_free' => true,
                'thumbnail' => 'https://source.unsplash.com/random/800x600/?website,minimal',
                'features' => [
                    'Responsive design',
                    'Minimalist layout',
                    'Limited color palette',
                    'Clean typography',
                ],
                'popular' => false,
            ],
            [
                'name' => 'Corporate',
                'description' => 'A professional theme designed for corporate training and enterprise learning.',
                'price' => 49,
                'is_free' => false,
                'thumbnail' => 'https://source.unsplash.com/random/800x600/?website,corporate',
                'features' => [
                    'Responsive design',
                    'Professional layout',
                    'Corporate branding options',
                    'Advanced customization',
                    'Priority support',
                ],
                'popular' => false,
            ],
            [
                'name' => 'Creative',
                'description' => 'A vibrant theme for creative courses and artistic content.',
                'price' => 49,
                'is_free' => false,
                'thumbnail' => 'https://source.unsplash.com/random/800x600/?website,creative',
                'features' => [
                    'Responsive design',
                    'Creative layout',
                    'Vibrant color options',
                    'Advanced customization',
                    'Priority support',
                ],
                'popular' => false,
            ],
            [
                'name' => 'Tech',
                'description' => 'A modern theme designed for technology courses and coding bootcamps.',
                'price' => 49,
                'is_free' => false,
                'thumbnail' => 'https://source.unsplash.com/random/800x600/?website,tech',
                'features' => [
                    'Responsive design',
                    'Tech-focused layout',
                    'Dark mode by default',
                    'Code syntax highlighting',
                    'Advanced customization',
                    'Priority support',
                ],
                'popular' => true,
            ],
        ];

        return view('marketing.themes', compact('themes'));
    }
}
