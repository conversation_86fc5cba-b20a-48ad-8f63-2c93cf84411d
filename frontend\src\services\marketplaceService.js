import axios from 'axios';

const API_URL = 'http://localhost:8000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
  },
  withCredentials: true,
});

const marketplaceService = {
  // Get featured themes and modules
  getFeatured: () => {
    return api.get('/marketplace/featured');
  },

  // Get marketplace categories
  getCategories: (params) => {
    return api.get('/marketplace/categories', { params });
  },

  // Search themes and modules
  search: (params) => {
    return api.get('/marketplace/search', { params });
  },

  // Get themes
  getThemes: (params) => {
    return api.get('/marketplace/themes', { params });
  },

  // Get modules
  getModules: (params) => {
    return api.get('/marketplace/modules', { params });
  },

  // Get reviews for a theme or module
  getReviews: (params) => {
    return api.get('/marketplace/reviews', { params });
  },

  // Submit a review
  submitReview: (data) => {
    return api.post('/marketplace/reviews', data);
  },

  // Update a review
  updateReview: (id, data) => {
    return api.put(`/marketplace/reviews/${id}`, data);
  },

  // Delete a review
  deleteReview: (id) => {
    return api.delete(`/marketplace/reviews/${id}`);
  },

  // Install a theme
  installTheme: (id) => {
    return api.post(`/themes/${id}/install`);
  },

  // Install a module
  installModule: (id) => {
    return api.post(`/modules/${id}/install-marketplace`);
  },

  // Purchase a theme
  purchaseTheme: (id) => {
    return api.post(`/themes/${id}/purchase`);
  },

  // Purchase a module
  purchaseModule: (id) => {
    return api.post(`/modules/${id}/purchase`);
  }
};

export default marketplaceService;
