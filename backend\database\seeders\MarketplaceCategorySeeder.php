<?php

namespace Database\Seeders;

use App\Models\MarketplaceCategory;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class MarketplaceCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            // Theme categories
            [
                'name' => 'Education',
                'description' => 'Themes designed for educational institutions and online learning.',
                'icon' => 'school',
                'type' => 'theme',
                'order' => 1,
            ],
            [
                'name' => 'Business',
                'description' => 'Professional themes for business and corporate training.',
                'icon' => 'business',
                'type' => 'theme',
                'order' => 2,
            ],
            [
                'name' => 'Creative',
                'description' => 'Visually striking themes for creative courses and portfolios.',
                'icon' => 'palette',
                'type' => 'theme',
                'order' => 3,
            ],
            [
                'name' => 'Minimal',
                'description' => 'Clean, simple themes with a focus on content.',
                'icon' => 'remove',
                'type' => 'theme',
                'order' => 4,
            ],
            
            // Module categories
            [
                'name' => 'Communication',
                'description' => 'Modules for enhancing communication between students and instructors.',
                'icon' => 'chat',
                'type' => 'module',
                'order' => 1,
            ],
            [
                'name' => 'Assessment',
                'description' => 'Tools for creating and managing assessments and quizzes.',
                'icon' => 'assignment',
                'type' => 'module',
                'order' => 2,
            ],
            [
                'name' => 'Engagement',
                'description' => 'Modules to increase student engagement and participation.',
                'icon' => 'emoji_events',
                'type' => 'module',
                'order' => 3,
            ],
            [
                'name' => 'Analytics',
                'description' => 'Tools for tracking and analyzing student performance.',
                'icon' => 'analytics',
                'type' => 'module',
                'order' => 4,
            ],
            [
                'name' => 'Integration',
                'description' => 'Modules for integrating with third-party services and tools.',
                'icon' => 'integration_instructions',
                'type' => 'module',
                'order' => 5,
            ],
            
            // Both types
            [
                'name' => 'Featured',
                'description' => 'Our top picks for themes and modules.',
                'icon' => 'star',
                'type' => 'both',
                'order' => 0,
            ],
        ];

        foreach ($categories as $category) {
            MarketplaceCategory::firstOrCreate(
                ['slug' => Str::slug($category['name'])],
                [
                    'name' => $category['name'],
                    'slug' => Str::slug($category['name']),
                    'description' => $category['description'],
                    'icon' => $category['icon'],
                    'type' => $category['type'],
                    'order' => $category['order'],
                    'is_active' => true,
                ]
            );
        }
    }
}
