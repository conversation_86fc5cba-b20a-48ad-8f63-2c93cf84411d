<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\DeveloperAccount;
use App\Models\DeveloperSubmission;
use App\Models\MarketplaceCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class DeveloperSubmissionController extends Controller
{
    /**
     * Display a listing of the submissions for the authenticated developer.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $developerAccount = DeveloperAccount::where('user_id', $user->id)->first();

        if (!$developerAccount) {
            return response()->json([
                'success' => false,
                'message' => 'Developer account not found'
            ], 404);
        }

        $type = $request->input('type');
        $status = $request->input('status');
        $query = DeveloperSubmission::where('developer_account_id', $developerAccount->id);

        if ($type) {
            $query->where('type', $type);
        }

        if ($status) {
            $query->where('status', $status);
        }

        $submissions = $query->paginate(10);

        return response()->json([
            'success' => true,
            'data' => $submissions
        ]);
    }

    /**
     * Store a newly created submission in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|in:theme,module',
            'name' => 'required|string|max:255',
            'version' => 'required|string|max:50',
            'description' => 'required|string|max:5000',
            'features' => 'nullable|string|max:5000',
            'installation_instructions' => 'nullable|string|max:5000',
            'usage_instructions' => 'nullable|string|max:5000',
            'thumbnail' => 'nullable|string|max:255',
            'screenshots' => 'nullable|array',
            'screenshots.*' => 'string|max:255',
            'demo_url' => 'nullable|url|max:255',
            'source_code_url' => 'nullable|url|max:255',
            'price' => 'required|numeric|min:0',
            'category_id' => 'required|exists:marketplace_categories,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $developerAccount = DeveloperAccount::where('user_id', $user->id)->first();

        if (!$developerAccount) {
            return response()->json([
                'success' => false,
                'message' => 'Developer account not found'
            ], 404);
        }

        // Check if developer account is approved
        if ($developerAccount->status !== 'approved') {
            return response()->json([
                'success' => false,
                'message' => 'Your developer account must be approved to submit items'
            ], 400);
        }

        // Validate category type
        $category = MarketplaceCategory::find($request->input('category_id'));
        if ($category->type !== $request->input('type') && $category->type !== 'both') {
            return response()->json([
                'success' => false,
                'message' => 'The selected category is not valid for this item type'
            ], 400);
        }

        // Create submission
        $submission = new DeveloperSubmission();
        $submission->developer_account_id = $developerAccount->id;
        $submission->type = $request->input('type');
        $submission->name = $request->input('name');
        $submission->slug = Str::slug($request->input('name'));
        $submission->version = $request->input('version');
        $submission->description = $request->input('description');
        $submission->features = $request->input('features');
        $submission->installation_instructions = $request->input('installation_instructions');
        $submission->usage_instructions = $request->input('usage_instructions');
        $submission->thumbnail = $request->input('thumbnail');
        $submission->screenshots = $request->input('screenshots');
        $submission->demo_url = $request->input('demo_url');
        $submission->source_code_url = $request->input('source_code_url');
        $submission->price = $request->input('price');
        $submission->category_id = $request->input('category_id');
        $submission->status = 'draft';
        $submission->save();

        return response()->json([
            'success' => true,
            'message' => 'Submission created successfully',
            'data' => $submission
        ], 201);
    }

    /**
     * Display the specified submission.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $user = Auth::user();
        $developerAccount = DeveloperAccount::where('user_id', $user->id)->first();

        if (!$developerAccount) {
            return response()->json([
                'success' => false,
                'message' => 'Developer account not found'
            ], 404);
        }

        $submission = DeveloperSubmission::where('id', $id)
            ->where('developer_account_id', $developerAccount->id)
            ->first();

        if (!$submission) {
            return response()->json([
                'success' => false,
                'message' => 'Submission not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $submission
        ]);
    }

    /**
     * Update the specified submission in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'version' => 'required|string|max:50',
            'description' => 'required|string|max:5000',
            'features' => 'nullable|string|max:5000',
            'installation_instructions' => 'nullable|string|max:5000',
            'usage_instructions' => 'nullable|string|max:5000',
            'thumbnail' => 'nullable|string|max:255',
            'screenshots' => 'nullable|array',
            'screenshots.*' => 'string|max:255',
            'demo_url' => 'nullable|url|max:255',
            'source_code_url' => 'nullable|url|max:255',
            'price' => 'required|numeric|min:0',
            'category_id' => 'required|exists:marketplace_categories,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $developerAccount = DeveloperAccount::where('user_id', $user->id)->first();

        if (!$developerAccount) {
            return response()->json([
                'success' => false,
                'message' => 'Developer account not found'
            ], 404);
        }

        $submission = DeveloperSubmission::where('id', $id)
            ->where('developer_account_id', $developerAccount->id)
            ->first();

        if (!$submission) {
            return response()->json([
                'success' => false,
                'message' => 'Submission not found'
            ], 404);
        }

        // Check if submission can be updated
        if (!in_array($submission->status, ['draft', 'rejected'])) {
            return response()->json([
                'success' => false,
                'message' => 'This submission cannot be updated in its current status'
            ], 400);
        }

        // Validate category type
        $category = MarketplaceCategory::find($request->input('category_id'));
        if ($category->type !== $submission->type && $category->type !== 'both') {
            return response()->json([
                'success' => false,
                'message' => 'The selected category is not valid for this item type'
            ], 400);
        }

        // Update submission
        $submission->name = $request->input('name');
        $submission->slug = Str::slug($request->input('name'));
        $submission->version = $request->input('version');
        $submission->description = $request->input('description');
        $submission->features = $request->input('features');
        $submission->installation_instructions = $request->input('installation_instructions');
        $submission->usage_instructions = $request->input('usage_instructions');
        $submission->thumbnail = $request->input('thumbnail');
        $submission->screenshots = $request->input('screenshots');
        $submission->demo_url = $request->input('demo_url');
        $submission->source_code_url = $request->input('source_code_url');
        $submission->price = $request->input('price');
        $submission->category_id = $request->input('category_id');
        $submission->save();

        return response()->json([
            'success' => true,
            'message' => 'Submission updated successfully',
            'data' => $submission
        ]);
    }

    /**
     * Submit the specified submission for review.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function submit($id)
    {
        $user = Auth::user();
        $developerAccount = DeveloperAccount::where('user_id', $user->id)->first();

        if (!$developerAccount) {
            return response()->json([
                'success' => false,
                'message' => 'Developer account not found'
            ], 404);
        }

        $submission = DeveloperSubmission::where('id', $id)
            ->where('developer_account_id', $developerAccount->id)
            ->first();

        if (!$submission) {
            return response()->json([
                'success' => false,
                'message' => 'Submission not found'
            ], 404);
        }

        // Check if submission can be submitted
        if (!in_array($submission->status, ['draft', 'rejected'])) {
            return response()->json([
                'success' => false,
                'message' => 'This submission cannot be submitted in its current status'
            ], 400);
        }

        // Submit the submission
        $submission->status = 'submitted';
        $submission->save();

        return response()->json([
            'success' => true,
            'message' => 'Submission submitted for review successfully',
            'data' => $submission
        ]);
    }

    /**
     * Review a submission (admin only).
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function review(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:approved,rejected',
            'rejection_reason' => 'required_if:status,rejected|nullable|string|max:1000',
            'test_results' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();

        // Check if user is admin
        if ($user->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $submission = DeveloperSubmission::find($id);
        if (!$submission) {
            return response()->json([
                'success' => false,
                'message' => 'Submission not found'
            ], 404);
        }

        // Check if submission can be reviewed
        if ($submission->status !== 'submitted' && $submission->status !== 'in_review') {
            return response()->json([
                'success' => false,
                'message' => 'This submission cannot be reviewed in its current status'
            ], 400);
        }

        // Update submission status
        $submission->status = $request->input('status');
        $submission->rejection_reason = $request->input('rejection_reason');
        $submission->test_results = $request->input('test_results');
        $submission->save();

        // If approved, publish to marketplace
        if ($request->input('status') === 'approved') {
            $submission->publish();
        }

        return response()->json([
            'success' => true,
            'message' => 'Submission reviewed successfully',
            'data' => $submission
        ]);
    }

    /**
     * List all submissions (admin only).
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function adminIndex(Request $request)
    {
        $user = Auth::user();

        // Check if user is admin
        if ($user->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $type = $request->input('type');
        $status = $request->input('status');
        $query = DeveloperSubmission::with('developerAccount', 'category');

        if ($type) {
            $query->where('type', $type);
        }

        if ($status) {
            $query->where('status', $status);
        }

        $submissions = $query->paginate(10);

        return response()->json([
            'success' => true,
            'data' => $submissions
        ]);
    }
}
