<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class DocsController extends Controller
{
    /**
     * Display the documentation page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $categories = [
            [
                'name' => 'Getting Started',
                'docs' => [
                    [
                        'title' => 'Introduction',
                        'description' => 'Learn about the LMS platform and its features.',
                        'icon' => 'book-open',
                    ],
                    [
                        'title' => 'Installation',
                        'description' => 'How to install and set up your LMS platform.',
                        'icon' => 'download',
                    ],
                    [
                        'title' => 'Configuration',
                        'description' => 'Configure your LMS platform to suit your needs.',
                        'icon' => 'cog',
                    ],
                ],
            ],
            [
                'name' => 'Features',
                'docs' => [
                    [
                        'title' => 'Course Management',
                        'description' => 'Learn how to create and manage courses.',
                        'icon' => 'academic-cap',
                    ],
                    [
                        'title' => 'User Management',
                        'description' => 'Manage students, instructors, and administrators.',
                        'icon' => 'users',
                    ],
                    [
                        'title' => 'Theme Customization',
                        'description' => 'Customize the look and feel of your platform.',
                        'icon' => 'color-swatch',
                    ],
                ],
            ],
            [
                'name' => 'Advanced',
                'docs' => [
                    [
                        'title' => 'API Documentation',
                        'description' => 'Integrate with the LMS platform using our API.',
                        'icon' => 'code',
                    ],
                    [
                        'title' => 'Theme Development',
                        'description' => 'Create custom themes for the LMS platform.',
                        'icon' => 'template',
                    ],
                    [
                        'title' => 'Module Development',
                        'description' => 'Extend the platform with custom modules.',
                        'icon' => 'puzzle',
                    ],
                ],
            ],
        ];

        return view('marketing.docs', compact('categories'));
    }
}
