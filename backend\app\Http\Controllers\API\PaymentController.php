<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Enrollment;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Razorpay\Api\Api;

class PaymentController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    /**
     * Create a payment order.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createOrder(Request $request)
    {
        // Validate request
        $validator = Validator::make($request->all(), [
            'course_id' => 'required|exists:courses,id',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 400);
        }

        // Get course
        $course = Course::findOrFail($request->course_id);

        // Get authenticated user
        $user = Auth::user();

        // Check if user is already enrolled
        $enrollment = Enrollment::where('user_id', $user->id)
            ->where('course_id', $request->course_id)
            ->first();

        if ($enrollment && $enrollment->payment_status === 'completed') {
            return response()->json(['message' => 'User already enrolled in this course with completed payment']);
        }

        // Create enrollment if not exists
        if (!$enrollment) {
            $enrollment = Enrollment::create([
                'user_id' => $user->id,
                'course_id' => $request->course_id,
                'progress' => 0,
                'payment_status' => 'pending',
            ]);
        }

        // Create Razorpay order
        $api = new Api(env('RAZORPAY_KEY_ID', 'rzp_test_your_key_id'), env('RAZORPAY_KEY_SECRET', 'your_key_secret'));

        $orderData = [
            'receipt' => 'order_' . $user->id . '_' . $course->id,
            'amount' => $course->price * 100, // Amount in paise
            'currency' => 'INR',
            'notes' => [
                'user_id' => $user->id,
                'course_id' => $course->id,
            ],
        ];

        $razorpayOrder = $api->order->create($orderData);

        // Create payment record
        $payment = Payment::create([
            'user_id' => $user->id,
            'course_id' => $course->id,
            'amount' => $course->price,
            'razorpay_id' => $razorpayOrder->id,
            'status' => 'pending',
        ]);

        return response()->json([
            'order_id' => $razorpayOrder->id,
            'amount' => $course->price,
            'currency' => 'INR',
            'key_id' => env('RAZORPAY_KEY_ID'),
            'payment_id' => $payment->id,
        ]);
    }

    /**
     * Verify payment.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyPayment(Request $request)
    {
        // Validate request
        $validator = Validator::make($request->all(), [
            'razorpay_order_id' => 'required|string',
            'razorpay_payment_id' => 'required|string',
            'razorpay_signature' => 'required|string',
            'payment_id' => 'required|exists:payments,id',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 400);
        }

        // Get payment
        $payment = Payment::findOrFail($request->payment_id);

        // Verify signature
        $api = new Api(env('RAZORPAY_KEY_ID', 'rzp_test_your_key_id'), env('RAZORPAY_KEY_SECRET', 'your_key_secret'));

        try {
            $attributes = [
                'razorpay_order_id' => $request->razorpay_order_id,
                'razorpay_payment_id' => $request->razorpay_payment_id,
                'razorpay_signature' => $request->razorpay_signature,
            ];

            $api->utility->verifyPaymentSignature($attributes);

            // Update payment status
            $payment->status = 'completed';
            $payment->save();

            // Update enrollment status
            $enrollment = Enrollment::where('user_id', $payment->user_id)
                ->where('course_id', $payment->course_id)
                ->first();

            if ($enrollment) {
                $enrollment->payment_status = 'completed';
                $enrollment->save();
            }

            return response()->json(['message' => 'Payment verified successfully']);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Payment verification failed: ' . $e->getMessage()], 400);
        }
    }

    /**
     * Get payment history for the authenticated user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function history()
    {
        $user = Auth::user();
        $payments = Payment::with('course')->where('user_id', $user->id)->orderBy('created_at', 'desc')->get();

        return response()->json($payments);
    }
}
