<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\PageBuilderPage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class PageBuilderController extends Controller
{
    /**
     * Display a listing of the pages for the authenticated tenant.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        // Only tenants can have pages
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can have pages'
            ], 403);
        }
        
        $status = $request->input('status');
        $query = PageBuilderPage::where('tenant_id', $user->tenant_id);
        
        if ($status) {
            if ($status === 'published') {
                $query->published();
            } elseif ($status === 'draft') {
                $query->draft();
            }
        }
        
        $pages = $query->orderBy('created_at', 'desc')->get();
        
        return response()->json([
            'success' => true,
            'data' => $pages
        ]);
    }
    
    /**
     * Store a newly created page in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'meta' => 'nullable|array',
            'content' => 'nullable|array',
            'status' => 'nullable|in:draft,published',
            'is_homepage' => 'nullable|boolean',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
        
        $user = Auth::user();
        
        // Only tenants can create pages
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can create pages'
            ], 403);
        }
        
        // Create the page
        $page = new PageBuilderPage();
        $page->tenant_id = $user->tenant_id;
        $page->title = $request->input('title');
        $page->slug = Str::slug($request->input('title'));
        $page->description = $request->input('description');
        $page->meta = $request->input('meta');
        $page->content = $request->input('content', []);
        $page->status = $request->input('status', 'draft');
        $page->is_homepage = $request->input('is_homepage', false);
        
        // If published, set published_at
        if ($page->status === 'published') {
            $page->published_at = now();
        }
        
        // If set as homepage, unset any existing homepage
        if ($page->is_homepage) {
            PageBuilderPage::where('tenant_id', $user->tenant_id)
                ->where('is_homepage', true)
                ->update(['is_homepage' => false]);
        }
        
        $page->save();
        
        return response()->json([
            'success' => true,
            'message' => 'Page created successfully',
            'data' => $page
        ], 201);
    }
    
    /**
     * Display the specified page.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $user = Auth::user();
        
        // Only tenants can view their own pages
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can view their own pages'
            ], 403);
        }
        
        $page = PageBuilderPage::where('id', $id)
            ->where('tenant_id', $user->tenant_id)
            ->first();
            
        if (!$page) {
            return response()->json([
                'success' => false,
                'message' => 'Page not found'
            ], 404);
        }
        
        return response()->json([
            'success' => true,
            'data' => $page
        ]);
    }
    
    /**
     * Update the specified page in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'meta' => 'nullable|array',
            'content' => 'nullable|array',
            'status' => 'nullable|in:draft,published',
            'is_homepage' => 'nullable|boolean',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
        
        $user = Auth::user();
        
        // Only tenants can update their own pages
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can update their own pages'
            ], 403);
        }
        
        $page = PageBuilderPage::where('id', $id)
            ->where('tenant_id', $user->tenant_id)
            ->first();
            
        if (!$page) {
            return response()->json([
                'success' => false,
                'message' => 'Page not found'
            ], 404);
        }
        
        // Update the page
        if ($request->has('title')) {
            $page->title = $request->input('title');
            $page->slug = Str::slug($request->input('title'));
        }
        
        if ($request->has('description')) {
            $page->description = $request->input('description');
        }
        
        if ($request->has('meta')) {
            $page->meta = $request->input('meta');
        }
        
        if ($request->has('content')) {
            $page->content = $request->input('content');
        }
        
        if ($request->has('status')) {
            $newStatus = $request->input('status');
            
            // If changing to published, set published_at
            if ($newStatus === 'published' && $page->status !== 'published') {
                $page->published_at = now();
            }
            
            $page->status = $newStatus;
        }
        
        if ($request->has('is_homepage')) {
            $isHomepage = $request->input('is_homepage');
            
            // If setting as homepage, unset any existing homepage
            if ($isHomepage && !$page->is_homepage) {
                PageBuilderPage::where('tenant_id', $user->tenant_id)
                    ->where('is_homepage', true)
                    ->update(['is_homepage' => false]);
            }
            
            $page->is_homepage = $isHomepage;
        }
        
        $page->save();
        
        return response()->json([
            'success' => true,
            'message' => 'Page updated successfully',
            'data' => $page
        ]);
    }
    
    /**
     * Remove the specified page from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $user = Auth::user();
        
        // Only tenants can delete their own pages
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can delete their own pages'
            ], 403);
        }
        
        $page = PageBuilderPage::where('id', $id)
            ->where('tenant_id', $user->tenant_id)
            ->first();
            
        if (!$page) {
            return response()->json([
                'success' => false,
                'message' => 'Page not found'
            ], 404);
        }
        
        $page->delete();
        
        return response()->json([
            'success' => true,
            'message' => 'Page deleted successfully'
        ]);
    }
    
    /**
     * Publish the specified page.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function publish($id)
    {
        $user = Auth::user();
        
        // Only tenants can publish their own pages
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can publish their own pages'
            ], 403);
        }
        
        $page = PageBuilderPage::where('id', $id)
            ->where('tenant_id', $user->tenant_id)
            ->first();
            
        if (!$page) {
            return response()->json([
                'success' => false,
                'message' => 'Page not found'
            ], 404);
        }
        
        $page->publish();
        
        return response()->json([
            'success' => true,
            'message' => 'Page published successfully',
            'data' => $page
        ]);
    }
    
    /**
     * Unpublish the specified page.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function unpublish($id)
    {
        $user = Auth::user();
        
        // Only tenants can unpublish their own pages
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can unpublish their own pages'
            ], 403);
        }
        
        $page = PageBuilderPage::where('id', $id)
            ->where('tenant_id', $user->tenant_id)
            ->first();
            
        if (!$page) {
            return response()->json([
                'success' => false,
                'message' => 'Page not found'
            ], 404);
        }
        
        $page->unpublish();
        
        return response()->json([
            'success' => true,
            'message' => 'Page unpublished successfully',
            'data' => $page
        ]);
    }
    
    /**
     * Set the specified page as homepage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function setAsHomepage($id)
    {
        $user = Auth::user();
        
        // Only tenants can set their own pages as homepage
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can set their own pages as homepage'
            ], 403);
        }
        
        $page = PageBuilderPage::where('id', $id)
            ->where('tenant_id', $user->tenant_id)
            ->first();
            
        if (!$page) {
            return response()->json([
                'success' => false,
                'message' => 'Page not found'
            ], 404);
        }
        
        $page->setAsHomepage();
        
        return response()->json([
            'success' => true,
            'message' => 'Page set as homepage successfully',
            'data' => $page
        ]);
    }
    
    /**
     * Get the homepage for the authenticated tenant.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getHomepage()
    {
        $user = Auth::user();
        
        // Only tenants can get their homepage
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can get their homepage'
            ], 403);
        }
        
        $homepage = PageBuilderPage::where('tenant_id', $user->tenant_id)
            ->where('is_homepage', true)
            ->first();
            
        if (!$homepage) {
            return response()->json([
                'success' => false,
                'message' => 'Homepage not found'
            ], 404);
        }
        
        return response()->json([
            'success' => true,
            'data' => $homepage
        ]);
    }
}
