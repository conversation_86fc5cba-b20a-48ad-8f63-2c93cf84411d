<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Test</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <style>
        /* Inline styles for testing */
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f0f0f0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .test-heading {
            color: #3b82f6;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 16px;
        }
        .test-paragraph {
            color: #4b5563;
            margin-bottom: 12px;
        }
        .test-card {
            background-color: white;
            padding: 16px;
            border-radius: 8px;
            margin-top: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- Test with inline styles -->
    <div class="test-container">
        <h1 class="test-heading">CSS Test Page (Inline Styles)</h1>
        <p class="test-paragraph">This is a test page with inline styles.</p>
        <div class="test-card">
            <h2 style="font-size: 18px; font-weight: 600; color: #1f2937;">Test Card</h2>
            <p style="color: #6b7280; margin-top: 8px;">This card uses inline styles.</p>
        </div>
    </div>

    <hr style="margin: 40px 0;">

    <!-- Test with Tailwind classes -->
    <div class="container mx-auto px-4 py-8 max-w-3xl">
        <h1 class="text-3xl font-bold text-blue-600">CSS Test Page (Tailwind)</h1>
        <p class="mt-4 text-gray-700">This is a test page to see if Tailwind CSS is loading correctly.</p>
        <div class="mt-8 p-6 bg-white rounded-lg shadow-md">
            <h2 class="text-xl font-semibold text-gray-800">Test Card</h2>
            <p class="mt-2 text-gray-600">This card should have styling if Tailwind CSS is loading correctly.</p>
        </div>
    </div>
</body>
</html>
