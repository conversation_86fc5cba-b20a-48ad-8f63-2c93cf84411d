import React, { useState, useEffect } from 'react';
import { 
  StyleSheet, 
  View, 
  Text, 
  Image, 
  ScrollView, 
  ActivityIndicator, 
  TouchableOpacity,
  Alert
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { 
  Button, 
  Card, 
  Chip, 
  Divider, 
  TextInput, 
  Title, 
  Paragraph,
  List
} from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';

import { Colors } from '@/constants/Colors';
import { useTheme } from '@/components/ThemeProvider';
import { useAuth } from '@/context/AuthContext';
import api from '@/services/api';

// Mock data for marketplace items
const mockThemes = [
  {
    id: '1',
    name: 'Modern Light Theme',
    description: 'A clean, modern theme with light colors and minimalist design. Perfect for educational institutions and online learning platforms. Features customizable colors, fonts, and layouts to match your brand identity.',
    thumbnail: 'https://source.unsplash.com/random?light',
    price: 0,
    is_featured: true,
    average_rating: 4.5,
    reviews_count: 28,
    downloads: 1250,
    category: { name: 'Education' },
    author: 'Design Studio',
    version: '1.2.0',
    updated_at: '2023-10-15T14:30:00Z',
    screenshots: [
      'https://source.unsplash.com/random?ui',
      'https://source.unsplash.com/random?design',
      'https://source.unsplash.com/random?interface'
    ]
  },
  {
    id: '2',
    name: 'Dark Academia',
    description: 'A sophisticated dark theme inspired by academic aesthetics. Features rich, dark colors with gold accents, serif fonts, and elegant design elements. Perfect for literature, history, and arts courses.',
    thumbnail: 'https://source.unsplash.com/random?dark',
    price: 29.99,
    is_featured: false,
    average_rating: 4.2,
    reviews_count: 15,
    downloads: 850,
    category: { name: 'Business' },
    author: 'Academic Designs',
    version: '1.0.5',
    updated_at: '2023-09-22T10:15:00Z',
    screenshots: [
      'https://source.unsplash.com/random?dark',
      'https://source.unsplash.com/random?academia',
      'https://source.unsplash.com/random?library'
    ]
  },
  {
    id: '3',
    name: 'Vibrant Colors',
    description: 'A bold and colorful theme to make your content stand out. Features bright color schemes, playful typography, and engaging design elements. Ideal for creative courses, children's education, and art instruction.',
    thumbnail: 'https://source.unsplash.com/random?colorful',
    price: 19.99,
    is_featured: true,
    average_rating: 4.7,
    reviews_count: 32,
    downloads: 2100,
    category: { name: 'Creative' },
    author: 'Color Works',
    version: '2.1.0',
    updated_at: '2023-11-05T09:45:00Z',
    screenshots: [
      'https://source.unsplash.com/random?colorful',
      'https://source.unsplash.com/random?vibrant',
      'https://source.unsplash.com/random?bright'
    ]
  },
];

const mockModules = [
  {
    id: '1',
    name: 'Advanced Quiz Builder',
    description: 'Create interactive quizzes with various question types and scoring options. Features multiple choice, true/false, matching, fill-in-the-blank, and essay questions. Includes automatic grading, time limits, and detailed analytics.',
    thumbnail: 'https://source.unsplash.com/random?quiz',
    price: 0,
    is_featured: true,
    average_rating: 4.8,
    reviews_count: 45,
    downloads: 3200,
    category: { name: 'Assessment' },
    author: 'EduTech Solutions',
    version: '3.0.2',
    updated_at: '2023-10-28T16:20:00Z',
    screenshots: [
      'https://source.unsplash.com/random?quiz',
      'https://source.unsplash.com/random?test',
      'https://source.unsplash.com/random?exam'
    ]
  },
  {
    id: '2',
    name: 'Discussion Forum',
    description: 'Add a full-featured discussion forum to your courses. Includes threaded discussions, rich text formatting, file attachments, moderation tools, and notification options. Enhance student engagement and collaboration.',
    thumbnail: 'https://source.unsplash.com/random?forum',
    price: 24.99,
    is_featured: false,
    average_rating: 4.3,
    reviews_count: 19,
    downloads: 1500,
    category: { name: 'Communication' },
    author: 'Community Tools',
    version: '2.4.1',
    updated_at: '2023-09-10T11:30:00Z',
    screenshots: [
      'https://source.unsplash.com/random?forum',
      'https://source.unsplash.com/random?discussion',
      'https://source.unsplash.com/random?community'
    ]
  },
  {
    id: '3',
    name: 'Certificate Generator',
    description: 'Automatically generate and issue certificates upon course completion. Features customizable templates, dynamic fields, digital signatures, and PDF export. Includes certificate verification system and student achievement tracking.',
    thumbnail: 'https://source.unsplash.com/random?certificate',
    price: 14.99,
    is_featured: true,
    average_rating: 4.6,
    reviews_count: 37,
    downloads: 2800,
    category: { name: 'Assessment' },
    author: 'Certification Pro',
    version: '1.8.0',
    updated_at: '2023-11-12T08:45:00Z',
    screenshots: [
      'https://source.unsplash.com/random?certificate',
      'https://source.unsplash.com/random?diploma',
      'https://source.unsplash.com/random?award'
    ]
  },
];

// Mock reviews
const mockReviews = [
  {
    id: 1,
    user: { name: 'John Smith' },
    rating: 5,
    comment: 'Excellent theme! Very easy to customize and looks professional.',
    created_at: '2023-10-20T14:30:00Z'
  },
  {
    id: 2,
    user: { name: 'Sarah Johnson' },
    rating: 4,
    comment: 'Good design and functionality. Would be perfect with a few more color options.',
    created_at: '2023-09-15T09:45:00Z'
  },
  {
    id: 3,
    user: { name: 'Michael Brown' },
    rating: 5,
    comment: 'Exactly what I needed for my online courses. Students love it!',
    created_at: '2023-11-05T16:20:00Z'
  }
];

const MarketplaceItemDetail = () => {
  const { type, id } = useLocalSearchParams();
  const router = useRouter();
  const { theme } = useTheme();
  const { isAuthenticated, user } = useAuth();
  
  const [loading, setLoading] = useState(true);
  const [item, setItem] = useState(null);
  const [reviews, setReviews] = useState([]);
  const [activeTab, setActiveTab] = useState('description');
  const [userReview, setUserReview] = useState({ rating: 0, comment: '' });
  
  useEffect(() => {
    fetchItemDetails();
  }, [type, id]);
  
  const fetchItemDetails = async () => {
    try {
      setLoading(true);
      
      // In a real implementation, you would fetch data from your API
      // const response = await api.get(`/marketplace/${type}/${id}`);
      // const reviewsResponse = await api.get('/marketplace/reviews', {
      //   params: { reviewable_type: type, reviewable_id: id }
      // });
      
      // Using mock data for now
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Find the item in mock data
      const mockData = type === 'theme' ? mockThemes : mockModules;
      const foundItem = mockData.find(item => item.id === id);
      
      if (foundItem) {
        setItem(foundItem);
        setReviews(mockReviews);
      }
    } catch (error) {
      console.error('Error fetching item details:', error);
      Alert.alert('Error', 'Failed to load item details');
    } finally {
      setLoading(false);
    }
  };
  
  const handleInstall = () => {
    if (!isAuthenticated) {
      Alert.alert(
        'Authentication Required',
        'Please log in to install this item',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Log In', onPress: () => router.push('/login') }
        ]
      );
      return;
    }
    
    Alert.alert(
      'Success',
      `${item.name} has been installed successfully!`,
      [{ text: 'OK' }]
    );
  };
  
  const handlePurchase = () => {
    if (!isAuthenticated) {
      Alert.alert(
        'Authentication Required',
        'Please log in to purchase this item',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Log In', onPress: () => router.push('/login') }
        ]
      );
      return;
    }
    
    Alert.alert(
      'Confirm Purchase',
      `Are you sure you want to purchase ${item.name} for $${item.price.toFixed(2)}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Purchase', 
          onPress: () => {
            Alert.alert('Success', 'Purchase completed successfully!');
          }
        }
      ]
    );
  };
  
  const handleSubmitReview = () => {
    if (!isAuthenticated) {
      Alert.alert(
        'Authentication Required',
        'Please log in to submit a review',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Log In', onPress: () => router.push('/login') }
        ]
      );
      return;
    }
    
    if (userReview.rating === 0) {
      Alert.alert('Error', 'Please select a rating');
      return;
    }
    
    Alert.alert(
      'Success',
      'Your review has been submitted and is pending approval',
      [{ text: 'OK' }]
    );
    
    setUserReview({ rating: 0, comment: '' });
  };
  
  const renderRating = (rating) => {
    return (
      <View style={styles.ratingContainer}>
        {[1, 2, 3, 4, 5].map((star) => (
          <Ionicons
            key={star}
            name={star <= Math.floor(rating) ? 'star' : 'star-outline'}
            size={16}
            color={Colors[theme].primary}
          />
        ))}
        <Text style={[styles.ratingText, { color: Colors[theme].textSecondary }]}>
          ({rating.toFixed(1)})
        </Text>
      </View>
    );
  };
  
  const renderStarInput = () => {
    return (
      <View style={styles.starInputContainer}>
        {[1, 2, 3, 4, 5].map((star) => (
          <TouchableOpacity
            key={star}
            onPress={() => setUserReview({ ...userReview, rating: star })}
          >
            <Ionicons
              name={star <= userReview.rating ? 'star' : 'star-outline'}
              size={32}
              color={Colors[theme].primary}
              style={{ marginHorizontal: 4 }}
            />
          </TouchableOpacity>
        ))}
      </View>
    );
  };
  
  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: Colors[theme].background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors[theme].primary} />
          <Text style={{ color: Colors[theme].text, marginTop: 10 }}>
            Loading item details...
          </Text>
        </View>
      </SafeAreaView>
    );
  }
  
  if (!item) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: Colors[theme].background }]}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color={Colors[theme].error} />
          <Text style={[styles.errorText, { color: Colors[theme].text }]}>
            Item not found
          </Text>
          <Button mode="contained" onPress={() => router.back()}>
            Go Back
          </Button>
        </View>
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: Colors[theme].background }]}>
      <ScrollView>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton} 
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color={Colors[theme].text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: Colors[theme].text }]} numberOfLines={1}>
            {item.name}
          </Text>
        </View>
        
        {/* Main Image */}
        <View style={styles.imageContainer}>
          <Image 
            source={{ uri: item.thumbnail }} 
            style={styles.mainImage} 
            resizeMode="cover" 
          />
          {item.is_featured && (
            <Chip 
              style={styles.featuredChip} 
              textStyle={{ color: 'white' }}
            >
              Featured
            </Chip>
          )}
        </View>
        
        {/* Item Info */}
        <Card style={styles.infoCard}>
          <Card.Content>
            <Title>{item.name}</Title>
            <View style={styles.metaRow}>
              {renderRating(item.average_rating || 0)}
              <Text style={{ color: Colors[theme].textSecondary }}>
                ({item.reviews_count || 0} reviews)
              </Text>
            </View>
            
            <View style={styles.priceRow}>
              <Text style={[styles.price, { color: Colors[theme].primary }]}>
                {item.price > 0 ? `$${item.price.toFixed(2)}` : 'Free'}
              </Text>
              <Text style={{ color: Colors[theme].textSecondary }}>
                {item.downloads} downloads
              </Text>
            </View>
            
            <Button
              mode="contained"
              style={styles.actionButton}
              onPress={item.price > 0 ? handlePurchase : handleInstall}
            >
              {item.price > 0 ? 'Purchase Now' : 'Install Now'}
            </Button>
            
            <Divider style={styles.divider} />
            
            <List.Item
              title="Author"
              description={item.author}
              left={props => <List.Icon {...props} icon="account" />}
            />
            <List.Item
              title="Version"
              description={item.version}
              left={props => <List.Icon {...props} icon="code-tags" />}
            />
            <List.Item
              title="Category"
              description={item.category?.name}
              left={props => <List.Icon {...props} icon="tag" />}
            />
            <List.Item
              title="Last Updated"
              description={new Date(item.updated_at).toLocaleDateString()}
              left={props => <List.Icon {...props} icon="calendar" />}
            />
          </Card.Content>
        </Card>
        
        {/* Tabs */}
        <View style={styles.tabContainer}>
          <View style={styles.tabButtons}>
            <TouchableOpacity
              style={[
                styles.tabButton,
                activeTab === 'description' && { 
                  borderBottomWidth: 2,
                  borderBottomColor: Colors[theme].primary
                }
              ]}
              onPress={() => setActiveTab('description')}
            >
              <Text style={[
                styles.tabButtonText,
                { color: activeTab === 'description' ? Colors[theme].primary : Colors[theme].text }
              ]}>
                Description
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.tabButton,
                activeTab === 'screenshots' && { 
                  borderBottomWidth: 2,
                  borderBottomColor: Colors[theme].primary
                }
              ]}
              onPress={() => setActiveTab('screenshots')}
            >
              <Text style={[
                styles.tabButtonText,
                { color: activeTab === 'screenshots' ? Colors[theme].primary : Colors[theme].text }
              ]}>
                Screenshots
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.tabButton,
                activeTab === 'reviews' && { 
                  borderBottomWidth: 2,
                  borderBottomColor: Colors[theme].primary
                }
              ]}
              onPress={() => setActiveTab('reviews')}
            >
              <Text style={[
                styles.tabButtonText,
                { color: activeTab === 'reviews' ? Colors[theme].primary : Colors[theme].text }
              ]}>
                Reviews
              </Text>
            </TouchableOpacity>
          </View>
          
          {/* Tab Content */}
          <View style={styles.tabContent}>
            {activeTab === 'description' && (
              <Text style={{ color: Colors[theme].text }}>
                {item.description}
              </Text>
            )}
            
            {activeTab === 'screenshots' && (
              <View style={styles.screenshotsContainer}>
                {item.screenshots && item.screenshots.length > 0 ? (
                  item.screenshots.map((screenshot, index) => (
                    <Image
                      key={index}
                      source={{ uri: screenshot }}
                      style={styles.screenshot}
                      resizeMode="cover"
                    />
                  ))
                ) : (
                  <Text style={{ color: Colors[theme].textSecondary }}>
                    No screenshots available
                  </Text>
                )}
              </View>
            )}
            
            {activeTab === 'reviews' && (
              <View>
                {/* Review Form */}
                <Card style={styles.reviewFormCard}>
                  <Card.Content>
                    <Title>Write a Review</Title>
                    {renderStarInput()}
                    <TextInput
                      mode="outlined"
                      label="Your Review"
                      value={userReview.comment}
                      onChangeText={(text) => setUserReview({ ...userReview, comment: text })}
                      multiline
                      numberOfLines={4}
                      style={styles.reviewInput}
                    />
                    <Button 
                      mode="contained" 
                      onPress={handleSubmitReview}
                      style={styles.submitButton}
                    >
                      Submit Review
                    </Button>
                  </Card.Content>
                </Card>
                
                {/* Reviews List */}
                <Title style={styles.reviewsTitle}>Customer Reviews</Title>
                {reviews.length > 0 ? (
                  reviews.map((review) => (
                    <Card key={review.id} style={styles.reviewCard}>
                      <Card.Content>
                        <View style={styles.reviewHeader}>
                          <Text style={[styles.reviewAuthor, { color: Colors[theme].text }]}>
                            {review.user.name}
                          </Text>
                          <View style={styles.ratingContainer}>
                            {[1, 2, 3, 4, 5].map((star) => (
                              <Ionicons
                                key={star}
                                name={star <= review.rating ? 'star' : 'star-outline'}
                                size={14}
                                color={Colors[theme].primary}
                              />
                            ))}
                          </View>
                        </View>
                        <Text style={{ color: Colors[theme].textSecondary, fontSize: 12, marginBottom: 8 }}>
                          {new Date(review.created_at).toLocaleDateString()}
                        </Text>
                        <Text style={{ color: Colors[theme].text }}>
                          {review.comment}
                        </Text>
                      </Card.Content>
                    </Card>
                  ))
                ) : (
                  <Text style={{ color: Colors[theme].textSecondary, textAlign: 'center', marginTop: 20 }}>
                    No reviews yet. Be the first to review this {type}!
                  </Text>
                )}
              </View>
            )}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    marginVertical: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1,
  },
  imageContainer: {
    position: 'relative',
  },
  mainImage: {
    width: '100%',
    height: 200,
  },
  featuredChip: {
    position: 'absolute',
    top: 16,
    right: 16,
    backgroundColor: 'rgba(103, 58, 183, 0.8)',
  },
  infoCard: {
    margin: 16,
    elevation: 4,
  },
  metaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 8,
  },
  ratingText: {
    marginLeft: 4,
    fontSize: 14,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 8,
  },
  price: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  actionButton: {
    marginVertical: 16,
  },
  divider: {
    marginVertical: 16,
  },
  tabContainer: {
    margin: 16,
  },
  tabButtons: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  tabButtonText: {
    fontWeight: '500',
  },
  tabContent: {
    paddingVertical: 16,
  },
  screenshotsContainer: {
    flexDirection: 'column',
    alignItems: 'center',
  },
  screenshot: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 16,
  },
  reviewFormCard: {
    marginBottom: 24,
  },
  starInputContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginVertical: 16,
  },
  reviewInput: {
    marginVertical: 16,
  },
  submitButton: {
    marginTop: 8,
  },
  reviewsTitle: {
    marginBottom: 16,
  },
  reviewCard: {
    marginBottom: 16,
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  reviewAuthor: {
    fontWeight: 'bold',
  },
});

export default MarketplaceItemDetail;
