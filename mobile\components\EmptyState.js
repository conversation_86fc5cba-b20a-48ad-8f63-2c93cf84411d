import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from './ThemeProvider';
import Text from './ui/Text';

/**
 * Empty state component for displaying when there's no data
 *
 * @param {Object} props - Component props
 * @param {string} props.icon - Ionicons icon name
 * @param {string} props.title - Title text
 * @param {string} props.message - Message text
 * @param {string} props.actionLabel - Action button label
 * @param {Function} props.onAction - Action button handler
 */
const EmptyState = ({ 
  icon = 'alert-circle-outline', 
  title = 'No Data', 
  message = 'There is no data to display.', 
  actionLabel, 
  onAction 
}) => {
  const { theme } = useTheme();

  return (
    <View style={styles.container}>
      <Ionicons name={icon} size={80} color={theme.textSecondary} style={styles.icon} />
      <Text variant="h3" style={styles.title}>{title}</Text>
      <Text color="muted" style={styles.message}>{message}</Text>
      
      {actionLabel && onAction && (
        <TouchableOpacity 
          style={[styles.actionButton, { backgroundColor: theme.primary }]}
          onPress={onAction}
        >
          <Text color="light" style={styles.actionButtonText}>{actionLabel}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  icon: {
    marginBottom: 16,
    opacity: 0.7,
  },
  title: {
    marginBottom: 8,
    textAlign: 'center',
  },
  message: {
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  actionButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  actionButtonText: {
    fontWeight: '600',
  },
});

export default EmptyState;
