<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class DocsController extends Controller
{
    /**
     * Display the documentation home page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $categories = [
            [
                'name' => 'Getting Started',
                'icon' => 'rocket',
                'docs' => [
                    [
                        'title' => 'Introduction',
                        'slug' => 'introduction',
                        'description' => 'Learn about the LMS platform and how to get started.',
                    ],
                    [
                        'title' => 'Quick Start Guide',
                        'slug' => 'quick-start',
                        'description' => 'Get your LMS up and running in minutes.',
                    ],
                    [
                        'title' => 'Platform Overview',
                        'slug' => 'platform-overview',
                        'description' => 'Understand the key components of the platform.',
                    ],
                ],
            ],
            [
                'name' => 'Managing Your Courses',
                'icon' => 'academic-cap',
                'docs' => [
                    [
                        'title' => 'Creating Courses',
                        'slug' => 'creating-courses',
                        'description' => 'Learn how to create and structure your courses.',
                    ],
                    [
                        'title' => 'Adding Lessons',
                        'slug' => 'adding-lessons',
                        'description' => 'Add different types of content to your courses.',
                    ],
                    [
                        'title' => 'Creating Quizzes',
                        'slug' => 'creating-quizzes',
                        'description' => 'Create assessments and quizzes for your students.',
                    ],
                ],
            ],
            [
                'name' => 'Customizing Your LMS',
                'icon' => 'paint-brush',
                'docs' => [
                    [
                        'title' => 'Theme Customization',
                        'slug' => 'theme-customization',
                        'description' => 'Customize the look and feel of your platform.',
                    ],
                    [
                        'title' => 'Installing Extensions',
                        'slug' => 'installing-extensions',
                        'description' => 'Extend your platform with additional functionality.',
                    ],
                    [
                        'title' => 'Mobile App Configuration',
                        'slug' => 'mobile-app-configuration',
                        'description' => 'Configure your white-label mobile app.',
                    ],
                ],
            ],
        ];

        return view('marketing.docs.index', compact('categories'));
    }

    /**
     * Display a specific documentation page.
     *
     * @param  string  $slug
     * @return \Illuminate\View\View
     */
    public function show($slug)
    {
        // In a real application, you would fetch the documentation content from a database or file
        // For now, we'll just return a view with a placeholder
        
        return view('marketing.docs.show', compact('slug'));
    }
}
