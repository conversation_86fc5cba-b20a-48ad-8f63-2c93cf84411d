import React from 'react';
import { Text as RNText, StyleSheet } from 'react-native';
import { useTheme } from '../ThemeProvider';

/**
 * Custom Text component with consistent styling
 * 
 * @param {Object} props - Component props
 * @param {string} props.variant - Text variant (h1, h2, h3, h4, subtitle, body, caption)
 * @param {boolean} props.bold - Whether the text should be bold
 * @param {string} props.color - Text color (primary, secondary, error, etc.)
 * @param {Object} props.style - Additional style for the text
 */
const Text = ({ 
  children, 
  variant = 'body', 
  bold = false,
  color,
  style,
  ...props 
}) => {
  const { theme } = useTheme();
  
  // Determine text style based on variant
  const getVariantStyle = () => {
    switch (variant) {
      case 'h1':
        return styles.h1;
      case 'h2':
        return styles.h2;
      case 'h3':
        return styles.h3;
      case 'h4':
        return styles.h4;
      case 'subtitle':
        return styles.subtitle;
      case 'caption':
        return styles.caption;
      case 'body':
      default:
        return styles.body;
    }
  };
  
  // Determine text color
  const getColorStyle = () => {
    if (!color) {
      return { color: theme.text };
    }
    
    switch (color) {
      case 'primary':
        return { color: theme.primary };
      case 'secondary':
        return { color: theme.secondary };
      case 'error':
        return { color: theme.error };
      case 'accent':
        return { color: theme.accent };
      case 'light':
        return { color: theme.textLight };
      case 'muted':
        return { color: theme.textSecondary };
      default:
        return { color: color }; // Allow custom color values
    }
  };
  
  // Apply bold style if needed
  const boldStyle = bold ? { fontWeight: 'bold' } : {};
  
  return (
    <RNText
      style={[
        styles.text,
        getVariantStyle(),
        getColorStyle(),
        boldStyle,
        style,
      ]}
      {...props}
    >
      {children}
    </RNText>
  );
};

const styles = StyleSheet.create({
  text: {
    fontSize: 16,
    lineHeight: 24,
  },
  h1: {
    fontSize: 28,
    fontWeight: 'bold',
    lineHeight: 36,
    marginBottom: 16,
  },
  h2: {
    fontSize: 24,
    fontWeight: 'bold',
    lineHeight: 32,
    marginBottom: 12,
  },
  h3: {
    fontSize: 20,
    fontWeight: '600',
    lineHeight: 28,
    marginBottom: 8,
  },
  h4: {
    fontSize: 18,
    fontWeight: '600',
    lineHeight: 26,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontWeight: '600',
    lineHeight: 24,
    marginBottom: 4,
  },
  body: {
    fontSize: 16,
    lineHeight: 24,
  },
  caption: {
    fontSize: 14,
    lineHeight: 20,
    opacity: 0.8,
  },
});

export default Text;
