<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('app_configurations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained()->onDelete('cascade');
            $table->string('app_name');
            $table->string('bundle_id')->unique();
            $table->string('version')->default('1.0.0');
            $table->string('build_number')->default('1');
            $table->json('branding')->nullable();
            $table->json('features')->nullable();
            $table->json('app_store')->nullable();
            $table->json('build_settings')->nullable();
            $table->string('status')->default('draft');
            $table->timestamp('last_build_at')->nullable();
            $table->string('last_build_status')->nullable();
            $table->text('last_build_log')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('app_configurations');
    }
};
