import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Button,
  Chip,
  TextField,
  InputAdornment,
  IconButton,
  Grid,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Card,
  CardContent,
  Divider
} from '@mui/material';
import {
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  FileDownload as FileDownloadIcon,
  DateRange as DateRangeIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { paymentService } from '../../services/api';
import { Link as RouterLink } from 'react-router-dom';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';

// Sample data for charts
const monthlyRevenueData = [
  { name: 'Jan', revenue: 4000 },
  { name: 'Feb', revenue: 3000 },
  { name: 'Mar', revenue: 5000 },
  { name: 'Apr', revenue: 7000 },
  { name: 'May', revenue: 6000 },
  { name: 'Jun', revenue: 8000 },
  { name: 'Jul', revenue: 9000 },
  { name: 'Aug', revenue: 8500 },
  { name: 'Sep', revenue: 7500 },
  { name: 'Oct', revenue: 10000 },
  { name: 'Nov', revenue: 11000 },
  { name: 'Dec', revenue: 12000 },
];

const paymentMethodData = [
  { name: 'Credit Card', value: 65 },
  { name: 'PayPal', value: 25 },
  { name: 'Bank Transfer', value: 10 },
];

const COLORS = ['#0088FE', '#00C49F', '#FFBB28'];

const TransactionsReports = () => {
  const [tabValue, setTabValue] = useState(0);
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateRange, setDateRange] = useState({
    startDate: '',
    endDate: ''
  });
  const [reportPeriod, setReportPeriod] = useState('monthly');

  useEffect(() => {
    fetchTransactions();
  }, []);

  const fetchTransactions = async () => {
    try {
      setLoading(true);
      const response = await paymentService.getPaymentHistory();
      setTransactions(response.data);
      setError(null);
    } catch (error) {
      console.error('Error fetching transactions:', error);
      setError('Failed to load transactions. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value);
    setPage(0);
  };

  const handleStatusFilterChange = (event) => {
    setStatusFilter(event.target.value);
    setPage(0);
  };

  const handleDateChange = (event) => {
    const { name, value } = event.target;
    setDateRange(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleReportPeriodChange = (event) => {
    setReportPeriod(event.target.value);
  };

  const handleExportCSV = () => {
    // In a real implementation, this would call an API endpoint to generate and download a CSV
    alert('CSV export functionality would be implemented here');
  };

  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch = 
      transaction.course_name.toLowerCase().includes(searchQuery.toLowerCase()) || 
      transaction.user_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      transaction.transaction_id.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || transaction.status === statusFilter;
    
    let matchesDate = true;
    if (dateRange.startDate && dateRange.endDate) {
      const transactionDate = new Date(transaction.created_at);
      const startDate = new Date(dateRange.startDate);
      const endDate = new Date(dateRange.endDate);
      endDate.setHours(23, 59, 59); // Set to end of day
      
      matchesDate = transactionDate >= startDate && transactionDate <= endDate;
    }
    
    return matchesSearch && matchesStatus && matchesDate;
  });

  const paginatedTransactions = filteredTransactions.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  const getStatusChip = (status) => {
    switch (status) {
      case 'completed':
        return <Chip label="Completed" color="success" size="small" />;
      case 'pending':
        return <Chip label="Pending" color="warning" size="small" />;
      case 'failed':
        return <Chip label="Failed" color="error" size="small" />;
      case 'refunded':
        return <Chip label="Refunded" color="info" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  if (loading && transactions.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Transactions & Reports
      </Typography>
      
      <Tabs
        value={tabValue}
        onChange={handleTabChange}
        sx={{ mb: 3 }}
      >
        <Tab label="Transactions" />
        <Tab label="Reports" />
      </Tabs>
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {/* Transactions Tab */}
      {tabValue === 0 && (
        <Paper sx={{ p: 3, mb: 4 }}>
          <Grid container spacing={2} alignItems="center" sx={{ mb: 3 }}>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                placeholder="Search transactions..."
                variant="outlined"
                value={searchQuery}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <FormControl fullWidth variant="outlined">
                <InputLabel id="status-filter-label">Status</InputLabel>
                <Select
                  labelId="status-filter-label"
                  id="status-filter"
                  value={statusFilter}
                  onChange={handleStatusFilterChange}
                  label="Status"
                >
                  <MenuItem value="all">All Statuses</MenuItem>
                  <MenuItem value="completed">Completed</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="failed">Failed</MenuItem>
                  <MenuItem value="refunded">Refunded</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                label="Start Date"
                type="date"
                name="startDate"
                value={dateRange.startDate}
                onChange={handleDateChange}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                label="End Date"
                type="date"
                name="endDate"
                value={dateRange.endDate}
                onChange={handleDateChange}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={3} sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                startIcon={<FileDownloadIcon />}
                onClick={handleExportCSV}
              >
                Export CSV
              </Button>
              <Button
                variant="contained"
                startIcon={<RefreshIcon />}
                onClick={fetchTransactions}
              >
                Refresh
              </Button>
            </Grid>
          </Grid>
          
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Transaction ID</TableCell>
                  <TableCell>Course</TableCell>
                  <TableCell>User</TableCell>
                  <TableCell>Amount</TableCell>
                  <TableCell>Payment Method</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedTransactions.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} align="center">
                      No transactions found
                    </TableCell>
                  </TableRow>
                ) : (
                  paginatedTransactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell>{transaction.transaction_id}</TableCell>
                      <TableCell>{transaction.course_name}</TableCell>
                      <TableCell>{transaction.user_name}</TableCell>
                      <TableCell>${transaction.amount.toFixed(2)}</TableCell>
                      <TableCell>{transaction.payment_method}</TableCell>
                      <TableCell>{getStatusChip(transaction.status)}</TableCell>
                      <TableCell>{new Date(transaction.created_at).toLocaleString()}</TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          color="primary"
                          component={RouterLink}
                          to={`/admin/transactions/${transaction.id}`}
                          title="View Details"
                        >
                          <VisibilityIcon fontSize="small" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
          
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={filteredTransactions.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </Paper>
      )}
      
      {/* Reports Tab */}
      {tabValue === 1 && (
        <>
          <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <FormControl variant="outlined" sx={{ minWidth: 200 }}>
              <InputLabel id="report-period-label">Report Period</InputLabel>
              <Select
                labelId="report-period-label"
                id="report-period"
                value={reportPeriod}
                onChange={handleReportPeriodChange}
                label="Report Period"
              >
                <MenuItem value="daily">Daily</MenuItem>
                <MenuItem value="weekly">Weekly</MenuItem>
                <MenuItem value="monthly">Monthly</MenuItem>
                <MenuItem value="yearly">Yearly</MenuItem>
              </Select>
            </FormControl>
            
            <Button
              variant="outlined"
              startIcon={<FileDownloadIcon />}
              onClick={handleExportCSV}
            >
              Export Report
            </Button>
          </Box>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Paper sx={{ p: 3, mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Revenue Overview
                </Typography>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart
                    data={monthlyRevenueData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip formatter={(value) => [`$${value}`, 'Revenue']} />
                    <Legend />
                    <Bar dataKey="revenue" fill="#8884d8" name="Revenue" />
                  </BarChart>
                </ResponsiveContainer>
              </Paper>
              
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Top Selling Courses
                </Typography>
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Course</TableCell>
                        <TableCell>Tenant</TableCell>
                        <TableCell>Sales</TableCell>
                        <TableCell>Revenue</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      <TableRow>
                        <TableCell>Advanced Web Development</TableCell>
                        <TableCell>Tech Academy</TableCell>
                        <TableCell>245</TableCell>
                        <TableCell>$24,255.55</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Data Science Masterclass</TableCell>
                        <TableCell>Data Institute</TableCell>
                        <TableCell>189</TableCell>
                        <TableCell>$28,161.11</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Digital Marketing Fundamentals</TableCell>
                        <TableCell>Marketing School</TableCell>
                        <TableCell>156</TableCell>
                        <TableCell>$7,644.44</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>UI/UX Design Principles</TableCell>
                        <TableCell>Design Academy</TableCell>
                        <TableCell>134</TableCell>
                        <TableCell>$13,265.66</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Mobile App Development</TableCell>
                        <TableCell>Tech Academy</TableCell>
                        <TableCell>122</TableCell>
                        <TableCell>$12,078.78</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            </Grid>
            
            <Grid item xs={12} md={4}>
              <Paper sx={{ p: 3, mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Payment Methods
                </Typography>
                <ResponsiveContainer width="100%" height={250}>
                  <PieChart>
                    <Pie
                      data={paymentMethodData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {paymentMethodData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value, name) => [`${value}%`, name]} />
                  </PieChart>
                </ResponsiveContainer>
              </Paper>
              
              <Paper sx={{ p: 3, mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Monthly Summary
                </Typography>
                <Box sx={{ mt: 2 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        Total Revenue
                      </Typography>
                      <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                        $85,420.55
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        Transactions
                      </Typography>
                      <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                        1,245
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        Avg. Order Value
                      </Typography>
                      <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                        $68.61
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        Refund Rate
                      </Typography>
                      <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                        2.4%
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
              </Paper>
              
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Top Tenants by Revenue
                </Typography>
                <List>
                  <ListItem>
                    <Typography variant="body2" sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
                      <span>Tech Academy</span>
                      <span>$36,334.23</span>
                    </Typography>
                  </ListItem>
                  <Divider />
                  <ListItem>
                    <Typography variant="body2" sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
                      <span>Data Institute</span>
                      <span>$28,161.11</span>
                    </Typography>
                  </ListItem>
                  <Divider />
                  <ListItem>
                    <Typography variant="body2" sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
                      <span>Design Academy</span>
                      <span>$13,265.66</span>
                    </Typography>
                  </ListItem>
                  <Divider />
                  <ListItem>
                    <Typography variant="body2" sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
                      <span>Marketing School</span>
                      <span>$7,644.44</span>
                    </Typography>
                  </ListItem>
                </List>
              </Paper>
            </Grid>
          </Grid>
        </>
      )}
    </Container>
  );
};

// Add missing List and ListItem components
const List = ({ children }) => (
  <Box component="ul" sx={{ p: 0, m: 0, listStyle: 'none' }}>
    {children}
  </Box>
);

const ListItem = ({ children }) => (
  <Box component="li" sx={{ py: 1 }}>
    {children}
  </Box>
);

export default TransactionsReports;
