<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use <PERSON>mon\JWTAuth\Facades\JWTAuth;

class AuthenticationTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test user registration.
     */
    public function test_user_can_register(): void
    {
        $response = $this->postJson('/api/auth/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password',
            'role' => 'student',
        ]);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'message',
                'user' => [
                    'id',
                    'name',
                    'email',
                    'role',
                    'created_at',
                    'updated_at',
                ],
            ]);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'role' => 'student',
        ]);
    }

    /**
     * Test tenant registration.
     */
    public function test_tenant_can_register(): void
    {
        $response = $this->postJson('/api/auth/register-tenant', [
            'name' => 'Tenant User',
            'email' => '<EMAIL>',
            'password' => 'password',
            'tenant_name' => 'Test School',
            'domain' => 'test-school',
        ]);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'message',
                'tenant' => [
                    'id',
                    'name',
                    'domain',
                    'status',
                    'created_at',
                    'updated_at',
                ],
                'user' => [
                    'id',
                    'name',
                    'email',
                    'role',
                    'tenant_id',
                    'created_at',
                    'updated_at',
                ],
            ]);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'role' => 'tenant',
        ]);

        $this->assertDatabaseHas('tenants', [
            'name' => 'Test School',
            'domain' => 'test-school',
        ]);
    }

    /**
     * Test user login.
     */
    public function test_user_can_login(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);

        $response = $this->postJson('/api/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'access_token',
                'token_type',
                'expires_in',
                'user' => [
                    'id',
                    'name',
                    'email',
                    'role',
                ],
            ]);
    }

    /**
     * Test user profile access.
     */
    public function test_user_can_access_profile(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);

        $token = JWTAuth::fromUser($user);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/auth/profile');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'name',
                'email',
                'role',
                'created_at',
                'updated_at',
            ]);
    }

    /**
     * Test token refresh.
     */
    public function test_user_can_refresh_token(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);

        $token = JWTAuth::fromUser($user);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/auth/refresh');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'access_token',
                'token_type',
                'expires_in',
                'user',
            ]);
    }

    /**
     * Test user logout.
     */
    public function test_user_can_logout(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);

        $token = JWTAuth::fromUser($user);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/auth/logout');

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'User successfully signed out',
            ]);
    }

    /**
     * Test role-based access control.
     */
    public function test_role_based_access_control(): void
    {
        // Create admin user
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin',
        ]);

        // Create tenant user
        $tenant = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'tenant',
        ]);

        // Create student user
        $student = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'student',
        ]);

        // Test admin access to admin-only route
        $adminToken = JWTAuth::fromUser($admin);
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $adminToken,
        ])->getJson('/api/admin/test');

        // This will fail because we haven't created the route yet, but we're just testing the concept
        $response->assertStatus(404);

        // Test tenant access to tenant-only route
        $tenantToken = JWTAuth::fromUser($tenant);
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $tenantToken,
        ])->getJson('/api/tenant/test');

        // This will fail because we haven't created the route yet, but we're just testing the concept
        $response->assertStatus(404);

        // Test student access to student-only route
        $studentToken = JWTAuth::fromUser($student);
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $studentToken,
        ])->getJson('/api/student/test');

        // This will fail because we haven't created the route yet, but we're just testing the concept
        $response->assertStatus(404);
    }
}
