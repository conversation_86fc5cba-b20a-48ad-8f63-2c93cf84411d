<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if tenants table exists
        if (!Schema::hasTable('tenants')) {
            Schema::create('tenants', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('domain')->unique();
                $table->text('description')->nullable();
                $table->string('logo')->nullable();
                $table->string('status')->default('active');
                $table->unsignedBigInteger('owner_id')->nullable();
                $table->json('settings')->nullable();
                $table->timestamp('expires_at')->nullable();
                $table->timestamps();
            });
        }

        // Add tenant_id to users table if it doesn't exist
        if (Schema::hasTable('users') && !Schema::hasColumn('users', 'tenant_id')) {
            Schema::table('users', function (Blueprint $table) {
                $table->unsignedBigInteger('tenant_id')->nullable()->after('email_verified_at');
            });
        }

        // Add role to users table if it doesn't exist
        if (Schema::hasTable('users') && !Schema::hasColumn('users', 'role')) {
            Schema::table('users', function (Blueprint $table) {
                $table->string('role')->default('user')->after('email_verified_at');
            });
        }

        // Add status to users table if it doesn't exist
        if (Schema::hasTable('users') && !Schema::hasColumn('users', 'status')) {
            Schema::table('users', function (Blueprint $table) {
                $table->string('status')->default('active')->after('role');
            });
        }

        // Create a test tenant if none exists
        $tenantCount = DB::table('tenants')->count();
        if ($tenantCount === 0) {
            DB::table('tenants')->insert([
                'name' => 'Test Academy',
                'domain' => 'test-academy',
                'description' => 'This is a test academy for demonstration purposes.',
                'status' => 'active',
                'settings' => json_encode([
                    'theme' => 'default',
                    'colors' => [
                        'primary' => '#ff7700',
                        'secondary' => '#0369a1',
                    ],
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // We don't want to drop these tables in the down method
        // as they might contain important data
    }
};
