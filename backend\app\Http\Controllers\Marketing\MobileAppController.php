<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class MobileAppController extends Controller
{
    /**
     * Display the mobile app page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $appFeatures = [
            [
                'title' => 'Custom Branding',
                'description' => 'Get your own branded mobile app with your logo, colors, and identity.',
                'icon' => 'color-swatch',
            ],
            [
                'title' => 'Course Access',
                'description' => 'Students can access all their courses, videos, and materials on the go.',
                'icon' => 'academic-cap',
            ],
            [
                'title' => 'Offline Learning',
                'description' => 'Download courses for offline access when internet connection is limited.',
                'icon' => 'download',
            ],
            [
                'title' => 'Push Notifications',
                'description' => 'Send important updates and reminders directly to students\' devices.',
                'icon' => 'bell',
            ],
            [
                'title' => 'Interactive Quizzes',
                'description' => 'Students can complete quizzes and assessments right from their mobile device.',
                'icon' => 'clipboard-check',
            ],
            [
                'title' => 'Progress Tracking',
                'description' => 'Track course progress and completion rates across all devices.',
                'icon' => 'chart-bar',
            ],
        ];

        return view('marketing.mobile-app', compact('appFeatures'));
    }
}
