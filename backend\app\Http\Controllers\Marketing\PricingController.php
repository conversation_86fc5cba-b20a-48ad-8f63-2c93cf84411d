<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class PricingController extends Controller
{
    /**
     * Display the pricing page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $plans = [
            [
                'name' => 'Basic',
                'price' => 29,
                'period' => 'month',
                'features' => [
                    'Up to 100 students',
                    '10 courses',
                    'Basic analytics',
                    'Email support',
                    'Mobile app access',
                ],
                'popular' => false,
                'cta' => 'Start Free Trial',
                'cta_url' => '/signup?plan=basic',
            ],
            [
                'name' => 'Pro',
                'price' => 79,
                'period' => 'month',
                'features' => [
                    'Up to 1,000 students',
                    'Unlimited courses',
                    'Advanced analytics',
                    'Priority support',
                    'Custom domain',
                    'White-label mobile app',
                    '5 themes included',
                ],
                'popular' => true,
                'cta' => 'Start Free Trial',
                'cta_url' => '/signup?plan=pro',
            ],
            [
                'name' => 'Enterprise',
                'price' => 199,
                'period' => 'month',
                'features' => [
                    'Unlimited students',
                    'Unlimited courses',
                    'Advanced analytics',
                    'Dedicated support',
                    'Custom domain',
                    'White-label mobile app',
                    'All themes included',
                    'API access',
                    'SSO integration',
                ],
                'popular' => false,
                'cta' => 'Contact Sales',
                'cta_url' => '/contact?inquiry=enterprise',
            ],
        ];

        return view('marketing.pricing', compact('plans'));
    }
}
