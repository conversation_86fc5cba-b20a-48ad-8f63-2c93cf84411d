<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Subscription extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'plan_id',
        'status',
        'starts_at',
        'ends_at',
        'trial_ends_at',
        'payment_method',
        'payment_id',
        'quantity',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'trial_ends_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Get the tenant that owns the subscription.
     */
    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the plan that the subscription is for.
     */
    public function plan()
    {
        return $this->belongsTo(Plan::class);
    }

    /**
     * Get the payments for the subscription.
     */
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Check if the subscription is active.
     */
    public function isActive()
    {
        return $this->status === 'active';
    }

    /**
     * Check if the subscription is canceled.
     */
    public function isCanceled()
    {
        return $this->status === 'canceled';
    }

    /**
     * Check if the subscription is on trial.
     */
    public function onTrial()
    {
        return $this->trial_ends_at && $this->trial_ends_at->isFuture();
    }

    /**
     * Check if the subscription has ended.
     */
    public function hasEnded()
    {
        return $this->ends_at && $this->ends_at->isPast();
    }

    /**
     * Cancel the subscription.
     */
    public function cancel()
    {
        $this->status = 'canceled';
        $this->save();

        return $this;
    }

    /**
     * Resume the subscription.
     */
    public function resume()
    {
        $this->status = 'active';
        $this->save();

        return $this;
    }
}
