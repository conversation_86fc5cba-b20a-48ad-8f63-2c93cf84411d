@extends('admin.layouts.app')

@section('title', 'Plans')

@section('content')
<div class="container mx-auto px-6 py-8">
    <div class="flex items-center justify-between">
        <h3 class="text-3xl font-medium text-gray-700">Plans</h3>
        <a href="{{ route('admin.plans.create') }}" class="bg-primary hover:bg-primary-dark text-white font-bold py-2 px-4 rounded">
            Add Plan
        </a>
    </div>

    <div class="mt-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            @foreach($plans as $plan)
            <div class="bg-white overflow-hidden shadow rounded-lg {{ $plan->is_featured ? 'border-2 border-primary' : '' }}">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">{{ $plan->name }}</h3>
                        @if($plan->is_featured)
                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-primary text-white">
                            Featured
                        </span>
                        @endif
                    </div>
                    <div class="mt-4 flex items-baseline">
                        <span class="text-3xl font-extrabold text-gray-900">${{ number_format($plan->price, 2) }}</span>
                        <span class="ml-1 text-xl font-semibold text-gray-500">/{{ $plan->interval }}</span>
                    </div>
                    <div class="mt-6">
                        <ul class="space-y-3">
                            @foreach($plan->features as $feature)
                            <li class="flex items-start">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <p class="ml-3 text-sm text-gray-700">{{ $feature }}</p>
                            </li>
                            @endforeach
                        </ul>
                    </div>
                    <div class="mt-6">
                        <div class="rounded-md bg-gray-50 px-6 py-5">
                            <div class="text-sm">
                                <span class="font-medium text-gray-900">Active Tenants:</span>
                                <span class="ml-1 text-gray-500">{{ $plan->tenants_count }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="mt-6">
                        <a href="{{ route('admin.plans.edit', $plan->id) }}" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                            Edit Plan
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</div>
@endsection
