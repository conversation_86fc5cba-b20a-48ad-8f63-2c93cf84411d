import { useState, useEffect, useContext } from 'react';
import { Routes, Route, Link as RouterLink, useNavigate, useLocation } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Grid,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Button,
  Card,
  CardContent,
  CardActions,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle
} from '@mui/material';
import DashboardIcon from '@mui/icons-material/Dashboard';
import SchoolIcon from '@mui/icons-material/School';
import PeopleIcon from '@mui/icons-material/People';
import BusinessIcon from '@mui/icons-material/Business';
import SettingsIcon from '@mui/icons-material/Settings';
import PaletteIcon from '@mui/icons-material/Palette';
import ExtensionIcon from '@mui/icons-material/Extension';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import { AuthContext } from '../context/AuthContext';
import { courseService, tenantService } from '../services/api';

// Import admin components
import UserManagement from './admin/UserManagement';
import Settings from './admin/Settings';

// Dashboard Overview Component
const Overview = () => {
  const [stats, setStats] = useState({
    totalCourses: 0,
    totalUsers: 0,
    totalTenants: 0,
    pendingTenants: 0,
    totalRevenue: 0,
    activeUsers: 0,
    recentTenants: [],
    recentTransactions: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Sample data for charts
  const monthlyData = [
    { name: 'Jan', users: 65, tenants: 4, revenue: 4000 },
    { name: 'Feb', users: 78, tenants: 5, revenue: 5200 },
    { name: 'Mar', users: 90, tenants: 6, revenue: 6100 },
    { name: 'Apr', users: 120, tenants: 8, revenue: 8500 },
    { name: 'May', users: 150, tenants: 10, revenue: 10200 },
    { name: 'Jun', users: 190, tenants: 12, revenue: 12500 },
  ];

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // In a real app, you would have specific API endpoints for these
      const [coursesResponse, tenantsResponse] = await Promise.all([
        courseService.getAllCourses(),
        tenantService.getAllTenants()
      ]);

      const pendingTenants = tenantsResponse.data.filter(tenant => !tenant.is_active);
      const recentTenants = [...tenantsResponse.data]
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
        .slice(0, 5);

      setStats({
        totalCourses: coursesResponse.data.data.length,
        totalUsers: 350, // Placeholder value
        activeUsers: 280, // Placeholder value
        totalTenants: tenantsResponse.data.length,
        pendingTenants: pendingTenants.length,
        totalRevenue: 45000, // Placeholder value
        recentTenants: recentTenants,
        recentTransactions: [
          { id: 1, user: 'John Doe', amount: 99, course: 'Web Development Bootcamp', date: '2023-06-15' },
          { id: 2, user: 'Jane Smith', amount: 49, course: 'UI/UX Design Fundamentals', date: '2023-06-14' },
          { id: 3, user: 'Robert Johnson', amount: 149, course: 'Data Science Masterclass', date: '2023-06-13' },
          { id: 4, user: 'Emily Davis', amount: 79, course: 'Mobile App Development', date: '2023-06-12' },
        ]
      });
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setError('Failed to load dashboard data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ my: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" gutterBottom sx={{ mb: 0 }}>
          Admin Dashboard
        </Typography>
        <Button
          variant="outlined"
          onClick={fetchDashboardData}
          startIcon={<DashboardIcon />}
        >
          Refresh Data
        </Button>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column', height: 140, bgcolor: 'rgba(255, 119, 0, 0.05)' }}>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Total Courses
            </Typography>
            <Typography component="p" variant="h4" sx={{ color: '#ff7700' }}>
              {stats.totalCourses}
            </Typography>
            <Typography color="text.secondary" sx={{ flex: 1 }}>
              Active courses
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column', height: 140, bgcolor: 'rgba(255, 119, 0, 0.05)' }}>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Total Users
            </Typography>
            <Typography component="p" variant="h4" sx={{ color: '#ff7700' }}>
              {stats.totalUsers}
            </Typography>
            <Typography color="text.secondary" sx={{ flex: 1 }}>
              {stats.activeUsers} active users
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column', height: 140, bgcolor: 'rgba(255, 119, 0, 0.05)' }}>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Total Tenants
            </Typography>
            <Typography component="p" variant="h4" sx={{ color: '#ff7700' }}>
              {stats.totalTenants}
            </Typography>
            <Typography color="text.secondary" sx={{ flex: 1 }}>
              {stats.pendingTenants} pending approvals
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column', height: 140, bgcolor: 'rgba(255, 119, 0, 0.05)' }}>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Total Revenue
            </Typography>
            <Typography component="p" variant="h4" sx={{ color: '#ff7700' }}>
              ${stats.totalRevenue.toLocaleString()}
            </Typography>
            <Typography color="text.secondary" sx={{ flex: 1 }}>
              Platform earnings
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* Quick Actions */}
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Quick Actions
        </Typography>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item>
            <Button
              variant="contained"
              component={RouterLink}
              to="/admin/dashboard/tenants"
              sx={{ bgcolor: '#ff7700', '&:hover': { bgcolor: '#cc5c00' } }}
            >
              Manage Tenants
            </Button>
          </Grid>
          <Grid item>
            <Button
              variant="contained"
              component={RouterLink}
              to="/admin/dashboard/courses"
              sx={{ bgcolor: '#ff7700', '&:hover': { bgcolor: '#cc5c00' } }}
            >
              Manage Courses
            </Button>
          </Grid>
          <Grid item>
            <Button
              variant="contained"
              component={RouterLink}
              to="/admin/dashboard/users"
              sx={{ bgcolor: '#ff7700', '&:hover': { bgcolor: '#cc5c00' } }}
            >
              Manage Users
            </Button>
          </Grid>
          <Grid item>
            <Button
              variant="contained"
              component={RouterLink}
              to="/admin/dashboard/settings"
              sx={{ bgcolor: '#ff7700', '&:hover': { bgcolor: '#cc5c00' } }}
            >
              Platform Settings
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Recent Activity */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recent Tenants
            </Typography>
            {stats.recentTenants.length === 0 ? (
              <Typography variant="body1" sx={{ textAlign: 'center', py: 2 }}>
                No recent tenants.
              </Typography>
            ) : (
              <List>
                {stats.recentTenants.map((tenant) => (
                  <React.Fragment key={tenant.id}>
                    <ListItem>
                      <ListItemIcon>
                        <BusinessIcon sx={{ color: tenant.is_active ? 'success.main' : 'warning.main' }} />
                      </ListItemIcon>
                      <ListItemText
                        primary={tenant.name}
                        secondary={`Domain: ${tenant.domain} • Status: ${tenant.is_active ? 'Active' : 'Pending'}`}
                      />
                      <Button
                        variant="outlined"
                        size="small"
                        component={RouterLink}
                        to={`/admin/dashboard/tenants`}
                      >
                        View
                      </Button>
                    </ListItem>
                    {tenant !== stats.recentTenants[stats.recentTenants.length - 1] && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            )}
          </Paper>
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recent Transactions
            </Typography>
            {stats.recentTransactions.length === 0 ? (
              <Typography variant="body1" sx={{ textAlign: 'center', py: 2 }}>
                No recent transactions.
              </Typography>
            ) : (
              <List>
                {stats.recentTransactions.map((transaction) => (
                  <React.Fragment key={transaction.id}>
                    <ListItem>
                      <ListItemText
                        primary={`$${transaction.amount} - ${transaction.course}`}
                        secondary={`${transaction.user} • ${new Date(transaction.date).toLocaleDateString()}`}
                      />
                    </ListItem>
                    {transaction !== stats.recentTransactions[stats.recentTransactions.length - 1] && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Pending Approvals */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Pending Tenant Approvals
        </Typography>

        {stats.pendingTenants === 0 ? (
          <Typography variant="body1" sx={{ textAlign: 'center', py: 2 }}>
            No pending tenant approvals.
          </Typography>
        ) : (
          <Button
            variant="contained"
            sx={{ bgcolor: '#ff7700', '&:hover': { bgcolor: '#cc5c00' } }}
            component={RouterLink}
            to="/admin/dashboard/tenants"
          >
            Review {stats.pendingTenants} Pending Approvals
          </Button>
        )}
      </Paper>
    </Box>
  );
};

// Tenants Management Component
const TenantsManagement = () => {
  const [tenants, setTenants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [approvalDialogOpen, setApprovalDialogOpen] = useState(false);
  const [selectedTenant, setSelectedTenant] = useState(null);
  const [approvalLoading, setApprovalLoading] = useState(false);

  useEffect(() => {
    fetchTenants();
  }, []);

  const fetchTenants = async () => {
    try {
      setLoading(true);
      const response = await tenantService.getAllTenants();
      setTenants(response.data);
      setError(null);
    } catch (error) {
      console.error('Error fetching tenants:', error);
      setError('Failed to load tenants. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleApproveClick = (tenant) => {
    setSelectedTenant(tenant);
    setApprovalDialogOpen(true);
  };

  const handleApproveConfirm = async () => {
    try {
      setApprovalLoading(true);
      await tenantService.approveTenant(selectedTenant.id);

      // Update the tenant in the list
      setTenants(tenants.map(tenant =>
        tenant.id === selectedTenant.id ? { ...tenant, is_active: true } : tenant
      ));

      setApprovalDialogOpen(false);
      setSelectedTenant(null);
    } catch (error) {
      console.error('Error approving tenant:', error);
      setError('Failed to approve tenant. Please try again later.');
    } finally {
      setApprovalLoading(false);
    }
  };

  const handleDeleteTenant = async (tenantId) => {
    try {
      await tenantService.deleteTenant(tenantId);
      setTenants(tenants.filter(tenant => tenant.id !== tenantId));
    } catch (error) {
      console.error('Error deleting tenant:', error);
      setError('Failed to delete tenant. Please try again later.');
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ my: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" gutterBottom sx={{ mb: 0 }}>
          Tenants Management
        </Typography>
        <Button
          variant="outlined"
          onClick={fetchTenants}
          startIcon={<BusinessIcon />}
        >
          Refresh Tenants
        </Button>
      </Box>

      {tenants.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            No tenants found
          </Typography>
          <Typography variant="body1">
            There are no registered tenants in the system.
          </Typography>
        </Paper>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow sx={{ bgcolor: 'rgba(255, 119, 0, 0.05)' }}>
                <TableCell>Name</TableCell>
                <TableCell>Domain</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Created At</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {tenants.map((tenant) => (
                <TableRow key={tenant.id}>
                  <TableCell>{tenant.name}</TableCell>
                  <TableCell>{tenant.domain}</TableCell>
                  <TableCell>
                    {tenant.is_active ? (
                      <Box sx={{ display: 'flex', alignItems: 'center', color: 'success.main' }}>
                        <CheckCircleIcon fontSize="small" sx={{ mr: 1 }} />
                        Active
                      </Box>
                    ) : (
                      <Box sx={{ display: 'flex', alignItems: 'center', color: 'warning.main' }}>
                        <CancelIcon fontSize="small" sx={{ mr: 1 }} />
                        Pending
                      </Box>
                    )}
                  </TableCell>
                  <TableCell>{new Date(tenant.created_at).toLocaleDateString()}</TableCell>
                  <TableCell>
                    {!tenant.is_active && (
                      <Button
                        variant="contained"
                        size="small"
                        sx={{ mr: 1, bgcolor: '#ff7700', '&:hover': { bgcolor: '#cc5c00' } }}
                        onClick={() => handleApproveClick(tenant)}
                      >
                        Approve
                      </Button>
                    )}
                    <Button
                      variant="outlined"
                      size="small"
                      sx={{ mr: 1 }}
                    >
                      View
                    </Button>
                    <Button
                      variant="outlined"
                      color="error"
                      size="small"
                      onClick={() => handleDeleteTenant(tenant.id)}
                    >
                      Delete
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Approval Dialog */}
      <Dialog
        open={approvalDialogOpen}
        onClose={() => setApprovalDialogOpen(false)}
      >
        <DialogTitle>Approve Tenant</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to approve {selectedTenant?.name}? This will allow them to create and manage courses on the platform.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setApprovalDialogOpen(false)} disabled={approvalLoading}>
            Cancel
          </Button>
          <Button
            onClick={handleApproveConfirm}
            variant="contained"
            disabled={approvalLoading}
            sx={{ bgcolor: '#ff7700', '&:hover': { bgcolor: '#cc5c00' } }}
          >
            {approvalLoading ? 'Approving...' : 'Approve'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

// Courses Management Component
const CoursesManagement = () => {
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchCourses();
  }, []);

  const fetchCourses = async () => {
    try {
      setLoading(true);
      const response = await courseService.getAllCourses();
      setCourses(response.data.data);
      setError(null);
    } catch (error) {
      console.error('Error fetching courses:', error);
      setError('Failed to load courses. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteCourse = async (courseId) => {
    try {
      await courseService.deleteCourse(courseId);
      setCourses(courses.filter(course => course.id !== courseId));
    } catch (error) {
      console.error('Error deleting course:', error);
      setError('Failed to delete course. Please try again later.');
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ my: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" gutterBottom sx={{ mb: 0 }}>
          Courses Management
        </Typography>
        <Button
          variant="outlined"
          onClick={fetchCourses}
          startIcon={<SchoolIcon />}
        >
          Refresh Courses
        </Button>
      </Box>

      {courses.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            No courses found
          </Typography>
          <Typography variant="body1">
            There are no courses in the system.
          </Typography>
        </Paper>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow sx={{ bgcolor: 'rgba(255, 119, 0, 0.05)' }}>
                <TableCell>Title</TableCell>
                <TableCell>Instructor</TableCell>
                <TableCell>Tenant</TableCell>
                <TableCell>Price</TableCell>
                <TableCell>Enrollments</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {courses.map((course) => (
                <TableRow key={course.id}>
                  <TableCell>{course.title}</TableCell>
                  <TableCell>{course.instructor?.name || 'Unknown'}</TableCell>
                  <TableCell>{course.tenant?.name || 'Unknown'}</TableCell>
                  <TableCell>${course.price}</TableCell>
                  <TableCell>{course.enrollments?.length || 0}</TableCell>
                  <TableCell>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{ mr: 1, bgcolor: '#ff7700', '&:hover': { bgcolor: '#cc5c00' } }}
                      component={RouterLink}
                      to={`/course/${course.id}`}
                    >
                      View
                    </Button>
                    <Button
                      variant="outlined"
                      color="error"
                      size="small"
                      onClick={() => handleDeleteCourse(course.id)}
                    >
                      Delete
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );
};



// Main AdminDashboard Component
const AdminDashboard = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useContext(AuthContext);
  const [openDialog, setOpenDialog] = useState(false);

  useEffect(() => {
    if (user && user.role !== 'admin') {
      setOpenDialog(true);
    }
  }, [user]);

  const handleDialogClose = () => {
    setOpenDialog(false);
    navigate('/dashboard');
  };

  const getActiveItem = () => {
    const path = location.pathname;
    if (path.includes('/tenants')) return 1;
    if (path.includes('/courses')) return 2;
    if (path.includes('/users')) return 3;
    if (path.includes('/settings')) return 4;
    return 0;
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Dialog
        open={openDialog}
        onClose={handleDialogClose}
      >
        <DialogTitle>Access Denied</DialogTitle>
        <DialogContent>
          <DialogContentText>
            You don't have permission to access the admin dashboard. Only users with the admin role can access this area.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDialogClose} autoFocus>
            Go to Dashboard
          </Button>
        </DialogActions>
      </Dialog>

      <Grid container spacing={3}>
        {/* Sidebar */}
        <Grid item xs={12} md={3} lg={2}>
          <Paper sx={{ height: '100%' }}>
            <Box sx={{ p: 2, bgcolor: '#ff7700', color: 'white', textAlign: 'center' }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                Naxofy Admin
              </Typography>
            </Box>
            <List component="nav">
              <ListItem
                button
                component={RouterLink}
                to="/admin/dashboard"
                selected={getActiveItem() === 0}
                sx={{
                  '&.Mui-selected': {
                    bgcolor: 'rgba(255, 119, 0, 0.1)',
                    '&:hover': { bgcolor: 'rgba(255, 119, 0, 0.2)' }
                  },
                  '&:hover': { bgcolor: 'rgba(255, 119, 0, 0.05)' }
                }}
              >
                <ListItemIcon>
                  <DashboardIcon sx={{ color: getActiveItem() === 0 ? '#ff7700' : 'inherit' }} />
                </ListItemIcon>
                <ListItemText primary="Dashboard" primaryTypographyProps={{
                  sx: { color: getActiveItem() === 0 ? '#ff7700' : 'inherit', fontWeight: getActiveItem() === 0 ? 'bold' : 'normal' }
                }} />
              </ListItem>
              <ListItem
                button
                component={RouterLink}
                to="/admin/dashboard/tenants"
                selected={getActiveItem() === 1}
                sx={{
                  '&.Mui-selected': {
                    bgcolor: 'rgba(255, 119, 0, 0.1)',
                    '&:hover': { bgcolor: 'rgba(255, 119, 0, 0.2)' }
                  },
                  '&:hover': { bgcolor: 'rgba(255, 119, 0, 0.05)' }
                }}
              >
                <ListItemIcon>
                  <BusinessIcon sx={{ color: getActiveItem() === 1 ? '#ff7700' : 'inherit' }} />
                </ListItemIcon>
                <ListItemText primary="Tenants" primaryTypographyProps={{
                  sx: { color: getActiveItem() === 1 ? '#ff7700' : 'inherit', fontWeight: getActiveItem() === 1 ? 'bold' : 'normal' }
                }} />
              </ListItem>
              <ListItem
                button
                component={RouterLink}
                to="/admin/dashboard/courses"
                selected={getActiveItem() === 2}
                sx={{
                  '&.Mui-selected': {
                    bgcolor: 'rgba(255, 119, 0, 0.1)',
                    '&:hover': { bgcolor: 'rgba(255, 119, 0, 0.2)' }
                  },
                  '&:hover': { bgcolor: 'rgba(255, 119, 0, 0.05)' }
                }}
              >
                <ListItemIcon>
                  <SchoolIcon sx={{ color: getActiveItem() === 2 ? '#ff7700' : 'inherit' }} />
                </ListItemIcon>
                <ListItemText primary="Courses" primaryTypographyProps={{
                  sx: { color: getActiveItem() === 2 ? '#ff7700' : 'inherit', fontWeight: getActiveItem() === 2 ? 'bold' : 'normal' }
                }} />
              </ListItem>
              <ListItem
                button
                component={RouterLink}
                to="/admin/dashboard/users"
                selected={getActiveItem() === 3}
                sx={{
                  '&.Mui-selected': {
                    bgcolor: 'rgba(255, 119, 0, 0.1)',
                    '&:hover': { bgcolor: 'rgba(255, 119, 0, 0.2)' }
                  },
                  '&:hover': { bgcolor: 'rgba(255, 119, 0, 0.05)' }
                }}
              >
                <ListItemIcon>
                  <PeopleIcon sx={{ color: getActiveItem() === 3 ? '#ff7700' : 'inherit' }} />
                </ListItemIcon>
                <ListItemText primary="Users" primaryTypographyProps={{
                  sx: { color: getActiveItem() === 3 ? '#ff7700' : 'inherit', fontWeight: getActiveItem() === 3 ? 'bold' : 'normal' }
                }} />
              </ListItem>
              <Divider sx={{ my: 1 }} />
              <ListItem
                button
                component={RouterLink}
                to="/admin/dashboard/settings"
                selected={getActiveItem() === 4}
                sx={{
                  '&.Mui-selected': {
                    bgcolor: 'rgba(255, 119, 0, 0.1)',
                    '&:hover': { bgcolor: 'rgba(255, 119, 0, 0.2)' }
                  },
                  '&:hover': { bgcolor: 'rgba(255, 119, 0, 0.05)' }
                }}
              >
                <ListItemIcon>
                  <SettingsIcon sx={{ color: getActiveItem() === 4 ? '#ff7700' : 'inherit' }} />
                </ListItemIcon>
                <ListItemText primary="Settings" primaryTypographyProps={{
                  sx: { color: getActiveItem() === 4 ? '#ff7700' : 'inherit', fontWeight: getActiveItem() === 4 ? 'bold' : 'normal' }
                }} />
              </ListItem>
              <Divider sx={{ my: 1 }} />
              <ListItem
                button
                component={RouterLink}
                to="/themes"
                selected={false}
                sx={{ '&:hover': { bgcolor: 'rgba(255, 119, 0, 0.05)' } }}
              >
                <ListItemIcon>
                  <PaletteIcon />
                </ListItemIcon>
                <ListItemText primary="Themes" />
              </ListItem>
              <ListItem
                button
                component={RouterLink}
                to="/modules"
                selected={false}
                sx={{ '&:hover': { bgcolor: 'rgba(255, 119, 0, 0.05)' } }}
              >
                <ListItemIcon>
                  <ExtensionIcon />
                </ListItemIcon>
                <ListItemText primary="Modules" />
              </ListItem>
            </List>
          </Paper>
        </Grid>

        {/* Main Content */}
        <Grid item xs={12} md={9} lg={10}>
          <Paper sx={{ p: 3 }}>
            <Routes>
              <Route path="/" element={<Overview />} />
              <Route path="/tenants" element={<TenantsManagement />} />
              <Route path="/courses" element={<CoursesManagement />} />
              <Route path="/users" element={<UserManagement />} />
              <Route path="/settings" element={<Settings />} />
            </Routes>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default AdminDashboard;
