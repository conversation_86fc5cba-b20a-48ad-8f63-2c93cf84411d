import React from 'react';
import { View, TextInput, StyleSheet, Text } from 'react-native';
import { useTheme } from '../ThemeProvider';

/**
 * Custom Input component with consistent styling
 * 
 * @param {Object} props - Component props
 * @param {string} props.label - Input label
 * @param {string} props.error - Error message
 * @param {boolean} props.fullWidth - Whether the input should take full width
 * @param {Object} props.style - Additional style for the input container
 * @param {Object} props.inputStyle - Additional style for the input field
 */
const Input = ({ 
  label,
  error,
  fullWidth = false,
  style,
  inputStyle,
  ...props 
}) => {
  const { theme } = useTheme();
  
  return (
    <View style={[
      styles.container,
      fullWidth && styles.fullWidth,
      style
    ]}>
      {label && (
        <Text style={[
          styles.label,
          { color: theme.textSecondary }
        ]}>
          {label}
        </Text>
      )}
      
      <TextInput
        style={[
          styles.input,
          { 
            borderColor: error ? theme.error : theme.border,
            color: theme.text,
            backgroundColor: theme.backgroundSecondary,
          },
          inputStyle
        ]}
        placeholderTextColor={theme.textSecondary}
        {...props}
      />
      
      {error && (
        <Text style={[
          styles.error,
          { color: theme.error }
        ]}>
          {error}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  fullWidth: {
    width: '100%',
  },
  label: {
    marginBottom: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
  },
  error: {
    marginTop: 4,
    fontSize: 12,
  },
});

export default Input;
