import React, { useState, useEffect, useContext } from 'react';
import { StyleSheet, Image, ScrollView, TouchableOpacity, ActivityIndicator, Alert, View } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

import Container from '@/components/ui/Container';
import Text from '@/components/ui/Text';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { AuthContext } from '@/context/AuthContext';
import { courseService } from '@/services/api';
import { useTheme } from '@/components/ThemeProvider';

// Mock data for when API is not available
const mockCourseDetails = {
  id: 1,
  title: "Advanced Web Development",
  description: "Learn modern web development techniques with React, Node.js, and MongoDB. This comprehensive course covers frontend and backend development, as well as deployment and best practices for building scalable web applications.",
  thumbnail: "https://source.unsplash.com/random?web",
  price: 99.99,
  instructor: {
    name: "<PERSON>",
    bio: "Senior Web Developer with 10+ years of experience"
  },
  lessons: [
    { id: 1, title: "Introduction to Modern Web Development", duration: "45 min" },
    { id: 2, title: "Setting Up Your Development Environment", duration: "30 min" },
    { id: 3, title: "React Fundamentals", duration: "60 min" },
    { id: 4, title: "State Management with Redux", duration: "55 min" },
    { id: 5, title: "Building APIs with Node.js", duration: "50 min" },
  ],
  rating: 4.8,
  review_count: 156,
  is_enrolled: false
};

export default function CourseDetailScreen() {
  const { id } = useLocalSearchParams();
  const [course, setCourse] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [enrolling, setEnrolling] = useState(false);
  const { isAuthenticated, user } = useContext(AuthContext);
  const { theme } = useTheme();

  // Debug authentication state
  useEffect(() => {
    console.log('Authentication state in course detail:', isAuthenticated ? 'Authenticated' : 'Not authenticated');
    console.log('User:', user?.name || 'No user');
  }, [isAuthenticated, user]);

  useEffect(() => {
    fetchCourseDetails();
  }, [id]);

  const fetchCourseDetails = async () => {
    try {
      setLoading(true);
      // Try to fetch course details from API
      const response = await courseService.getCourseById(id);
      setCourse(response.data.course);
    } catch (error) {
      console.error('Error fetching course details:', error);
      setError('Failed to load course details');
      // Use mock data as fallback
      setCourse(mockCourseDetails);
    } finally {
      setLoading(false);
    }
  };

  const handleEnroll = async () => {
    console.log('Enroll button pressed');
    console.log('Authentication state:', isAuthenticated ? 'Authenticated' : 'Not authenticated');

    if (!isAuthenticated) {
      console.log('User not authenticated, showing login prompt');
      Alert.alert(
        'Authentication Required',
        'Please log in to enroll in this course',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Login', onPress: () => {
            console.log('Navigating to login screen');
            router.push('/login');
          }}
        ]
      );
      return;
    }

    try {
      console.log('Starting enrollment process for course ID:', id);
      setEnrolling(true);

      const response = await courseService.enrollInCourse(id);
      console.log('Enrollment response:', response.data);

      // Update the course object to reflect enrollment
      setCourse(prevCourse => ({
        ...prevCourse,
        is_enrolled: true
      }));

      Alert.alert('Success', 'You have successfully enrolled in this course');
    } catch (error) {
      console.error('Error enrolling in course:', error);
      Alert.alert('Error', 'Failed to enroll in course. Please try again.');
    } finally {
      setEnrolling(false);
    }
  };

  if (loading) {
    return (
      <Container style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.primary} />
        <Text style={styles.loadingText}>Loading course details...</Text>
      </Container>
    );
  }

  if (error || !course) {
    return (
      <Container style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={48} color={theme.error} />
        <Text style={styles.errorText}>{error || 'Course not found'}</Text>
        <Button onPress={fetchCourseDetails}>
          Try Again
        </Button>
      </Container>
    );
  }

  return (
    <Container scroll>
      <Image
        source={{ uri: course.thumbnail || 'https://via.placeholder.com/400x200?text=Course+Image' }}
        style={styles.courseImage}
      />

      <View style={styles.content}>
        <Text variant="h2" style={styles.courseTitle}>{course.title}</Text>

        <View style={styles.infoRow}>
          <View style={styles.instructorInfo}>
            <Ionicons name="person-outline" size={16} color={theme.textSecondary} />
            <Text variant="caption" color="muted" style={styles.instructorText}>
              By {course.instructor?.name || 'Instructor'}
            </Text>
          </View>
          {course.rating && (
            <View style={styles.ratingInfo}>
              <Ionicons name="star" size={16} color="#fbbf24" />
              <Text variant="caption" style={styles.ratingText}>
                {course.rating} ({course.review_count} reviews)
              </Text>
            </View>
          )}
        </View>

        <Card style={styles.priceCard}>
          <View style={styles.priceContainer}>
            <View>
              <Text variant="h3" color="primary" style={styles.priceText}>
                ${course.price?.toFixed(2) || 'Free'}
              </Text>
              <Text variant="caption" color="muted">One-time payment</Text>
            </View>

            {course.is_enrolled ? (
              <Button
                onPress={() => router.push(`/course/${id}/learn`)}
                style={styles.enrollButton}
              >
                <Ionicons name="play-circle-outline" size={20} color="white" style={styles.buttonIcon} />
                Continue Learning
              </Button>
            ) : (
              <Button
                onPress={handleEnroll}
                loading={enrolling}
                style={styles.enrollButton}
              >
                <Ionicons name="school-outline" size={20} color="white" style={styles.buttonIcon} />
                Enroll Now
              </Button>
            )}
          </View>
        </Card>

        <Card style={styles.section}>
          <Text variant="h4" style={styles.sectionTitle}>About this course</Text>
          <Text style={styles.description}>{course.description}</Text>
        </Card>

        <Card style={styles.section}>
          <Text variant="h4" style={styles.sectionTitle}>Course Content</Text>
          <Text variant="caption" color="muted" style={styles.lessonCount}>
            {course.lessons?.length || 0} lessons
          </Text>
          {course.lessons && course.lessons.length > 0 ? (
            course.lessons.map((lesson, index) => (
              <View key={lesson.id} style={[styles.lessonItem, { borderBottomColor: theme.border }]}>
                <View style={styles.lessonInfo}>
                  <View style={styles.lessonNumber}>
                    <Text variant="caption" color="primary">{index + 1}</Text>
                  </View>
                  <View style={styles.lessonDetails}>
                    <Text variant="subtitle">{lesson.title}</Text>
                    {lesson.duration && (
                      <Text variant="caption" color="muted">{lesson.duration}</Text>
                    )}
                  </View>
                </View>
                <Ionicons name="play-circle-outline" size={24} color={theme.primary} />
              </View>
            ))
          ) : (
            <View style={styles.emptyLessons}>
              <Ionicons name="book-outline" size={32} color={theme.textSecondary} />
              <Text color="muted">No lessons available yet</Text>
            </View>
          )}
        </Card>

        <Card style={styles.section}>
          <Text variant="h4" style={styles.sectionTitle}>Instructor</Text>
          <View style={styles.instructorCard}>
            <View style={[styles.instructorAvatar, { backgroundColor: theme.primary }]}>
              <Text style={styles.instructorInitial}>
                {course.instructor?.name?.charAt(0) || 'I'}
              </Text>
            </View>
            <View style={styles.instructorDetails}>
              <Text variant="subtitle" style={styles.instructorName}>
                {course.instructor?.name || 'Instructor'}
              </Text>
              <Text variant="caption" color="muted">
                {course.instructor?.bio || 'Experienced instructor'}
              </Text>
            </View>
          </View>
        </Card>
      </View>
    </Container>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginVertical: 16,
    textAlign: 'center',
  },
  courseImage: {
    width: '100%',
    height: 250,
    resizeMode: 'cover',
  },
  content: {
    padding: 16,
  },
  courseTitle: {
    marginBottom: 12,
    fontWeight: 'bold',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  instructorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  instructorText: {
    marginLeft: 4,
  },
  ratingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    marginLeft: 4,
  },
  priceCard: {
    marginBottom: 16,
  },
  priceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  enrollButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonIcon: {
    marginRight: 8,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    marginBottom: 12,
    fontWeight: 'bold',
  },
  description: {
    lineHeight: 22,
  },
  lessonCount: {
    marginBottom: 12,
  },
  lessonItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  lessonInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  lessonNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 119, 0, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  lessonDetails: {
    flex: 1,
  },
  emptyLessons: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  instructorCard: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  instructorAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  instructorInitial: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  instructorDetails: {
    flex: 1,
  },
  instructorName: {
    marginBottom: 4,
  },
});
