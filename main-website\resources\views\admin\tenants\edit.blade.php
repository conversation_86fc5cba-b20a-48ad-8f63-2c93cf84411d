@extends('admin.layouts.app')

@section('title', 'Edit Tenant')

@section('content')
<div class="container mx-auto px-6 py-8">
    <div class="flex items-center justify-between">
        <h3 class="text-3xl font-medium text-gray-700">Edit Tenant</h3>
        <div>
            <a href="{{ route('admin.tenants.show', $tenant->id) }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded mr-2">
                View Tenant
            </a>
            <a href="{{ route('admin.tenants.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Tenants
            </a>
        </div>
    </div>

    <div class="mt-8">
        <div class="bg-white p-6 rounded-lg shadow-md">
            <form action="#" method="POST">
                @csrf
                @method('PUT')
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
                        <input type="text" name="name" id="name" value="{{ $tenant->name }}" class="mt-1 focus:ring-primary focus:border-primary block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>

                    <div>
                        <label for="domain" class="block text-sm font-medium text-gray-700">Domain</label>
                        <div class="mt-1 flex rounded-md shadow-sm">
                            <input type="text" name="domain" id="domain" value="{{ explode('.', $tenant->domain)[0] }}" class="focus:ring-primary focus:border-primary flex-1 block w-full rounded-none rounded-l-md sm:text-sm border-gray-300">
                            <span class="inline-flex items-center px-3 rounded-r-md border border-l-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                                .naxofy.com
                            </span>
                        </div>
                    </div>

                    <div>
                        <label for="owner" class="block text-sm font-medium text-gray-700">Owner Name</label>
                        <input type="text" name="owner" id="owner" value="{{ $tenant->owner }}" class="mt-1 focus:ring-primary focus:border-primary block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                        <input type="email" name="email" id="email" value="{{ $tenant->email }}" class="mt-1 focus:ring-primary focus:border-primary block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>

                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700">Phone</label>
                        <input type="text" name="phone" id="phone" value="{{ $tenant->phone }}" class="mt-1 focus:ring-primary focus:border-primary block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>

                    <div>
                        <label for="plan" class="block text-sm font-medium text-gray-700">Plan</label>
                        <select id="plan" name="plan" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm">
                            <option value="basic" {{ $tenant->plan === 'Basic' ? 'selected' : '' }}>Basic</option>
                            <option value="standard" {{ $tenant->plan === 'Standard' ? 'selected' : '' }}>Standard</option>
                            <option value="premium" {{ $tenant->plan === 'Premium' ? 'selected' : '' }}>Premium</option>
                        </select>
                    </div>

                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                        <select id="status" name="status" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm">
                            <option value="active" {{ $tenant->status === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="pending" {{ $tenant->status === 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="suspended" {{ $tenant->status === 'suspended' ? 'selected' : '' }}>Suspended</option>
                        </select>
                    </div>

                    <div class="md:col-span-2">
                        <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
                        <textarea id="address" name="address" rows="3" class="mt-1 focus:ring-primary focus:border-primary block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">{{ $tenant->address }}</textarea>
                    </div>
                </div>

                <div class="mt-6 flex justify-between">
                    <button type="submit" class="bg-primary hover:bg-primary-dark text-white font-bold py-2 px-4 rounded">
                        Update Tenant
                    </button>
                    <button type="button" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                        Delete Tenant
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
