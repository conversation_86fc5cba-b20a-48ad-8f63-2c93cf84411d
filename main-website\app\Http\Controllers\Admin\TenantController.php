<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Tenant;
use App\Models\Plan;
use App\Models\User;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class TenantController extends Controller
{
    /**
     * Display a listing of the tenants.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $query = Tenant::with('owner', 'plan');

        // Filter by status
        if ($request->has('status') && $request->status != 'all') {
            $query->where('status', $request->status);
        }

        // Filter by plan
        if ($request->has('plan_id') && $request->plan_id != 'all') {
            $query->where('plan_id', $request->plan_id);
        }

        // Search by name or domain
        if ($request->has('search') && $request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                    ->orWhere('domain', 'like', '%' . $request->search . '%');
            });
        }

        // Sort by
        $sortField = $request->sort_by ?? 'created_at';
        $sortDirection = $request->sort_direction ?? 'desc';
        $query->orderBy($sortField, $sortDirection);

        $tenants = $query->paginate(10);
        $plans = Plan::all();

        return view('admin.tenants.index', compact('tenants', 'plans'));
    }

    /**
     * Show the form for creating a new tenant.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $plans = Plan::where('is_active', true)->get();
        return view('admin.tenants.create', compact('plans'));
    }

    /**
     * Store a newly created tenant in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'domain' => 'required|string|max:255|unique:tenants',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8',
            'plan_id' => 'required|exists:plans,id',
            'description' => 'nullable|string',
            'logo' => 'nullable|image|max:2048',
        ]);

        DB::beginTransaction();

        try {
            // Create the owner user
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'role' => 'tenant',
                'status' => 'active',
            ]);

            // Upload logo if provided
            $logoPath = null;
            if ($request->hasFile('logo')) {
                $logoPath = $request->file('logo')->store('tenant-logos', 'public');
            }

            // Create the tenant
            $tenant = Tenant::create([
                'name' => $request->name,
                'domain' => $request->domain,
                'description' => $request->description,
                'logo' => $logoPath,
                'status' => 'active',
                'plan_id' => $request->plan_id,
                'owner_id' => $user->id,
                'settings' => [
                    'theme' => 'default',
                    'colors' => [
                        'primary' => '#ff7700',
                        'secondary' => '#0369a1',
                    ],
                ],
            ]);

            // Update the user with the tenant_id
            $user->tenant_id = $tenant->id;
            $user->save();

            // Create a subscription
            $plan = Plan::find($request->plan_id);
            $subscription = Subscription::create([
                'tenant_id' => $tenant->id,
                'plan_id' => $plan->id,
                'status' => 'active',
                'starts_at' => now(),
                'ends_at' => $plan->interval === 'monthly' ? now()->addMonth() : now()->addYear(),
            ]);

            DB::commit();

            return redirect()->route('admin.tenants.index')
                ->with('success', 'Tenant created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to create tenant: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified tenant.
     *
     * @param  \App\Models\Tenant  $tenant
     * @return \Illuminate\View\View
     */
    public function show(Tenant $tenant)
    {
        $tenant->load('owner', 'plan', 'subscriptions', 'payments', 'users');
        
        // Get usage statistics
        $stats = [
            'students' => $tenant->users()->where('role', 'student')->count(),
            'instructors' => $tenant->users()->where('role', 'instructor')->count(),
            'courses' => $tenant->courses()->count(),
            'revenue' => $tenant->payments()->where('status', 'succeeded')->sum('amount'),
        ];

        return view('admin.tenants.show', compact('tenant', 'stats'));
    }

    /**
     * Show the form for editing the specified tenant.
     *
     * @param  \App\Models\Tenant  $tenant
     * @return \Illuminate\View\View
     */
    public function edit(Tenant $tenant)
    {
        $plans = Plan::where('is_active', true)->get();
        return view('admin.tenants.edit', compact('tenant', 'plans'));
    }

    /**
     * Update the specified tenant in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Tenant  $tenant
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, Tenant $tenant)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'domain' => 'required|string|max:255|unique:tenants,domain,' . $tenant->id,
            'plan_id' => 'required|exists:plans,id',
            'status' => 'required|in:active,pending,suspended',
            'description' => 'nullable|string',
            'logo' => 'nullable|image|max:2048',
        ]);

        // Upload logo if provided
        if ($request->hasFile('logo')) {
            $logoPath = $request->file('logo')->store('tenant-logos', 'public');
            $tenant->logo = $logoPath;
        }

        $tenant->name = $request->name;
        $tenant->domain = $request->domain;
        $tenant->description = $request->description;
        $tenant->status = $request->status;
        $tenant->plan_id = $request->plan_id;
        $tenant->save();

        return redirect()->route('admin.tenants.index')
            ->with('success', 'Tenant updated successfully.');
    }

    /**
     * Remove the specified tenant from storage.
     *
     * @param  \App\Models\Tenant  $tenant
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Tenant $tenant)
    {
        // This is a dangerous operation, so we'll just suspend the tenant instead
        $tenant->status = 'suspended';
        $tenant->save();

        return redirect()->route('admin.tenants.index')
            ->with('success', 'Tenant suspended successfully.');
    }

    /**
     * Approve a pending tenant.
     *
     * @param  \App\Models\Tenant  $tenant
     * @return \Illuminate\Http\RedirectResponse
     */
    public function approve(Tenant $tenant)
    {
        $tenant->status = 'active';
        $tenant->save();

        return redirect()->route('admin.tenants.index')
            ->with('success', 'Tenant approved successfully.');
    }

    /**
     * Display the tenant's usage statistics.
     *
     * @param  \App\Models\Tenant  $tenant
     * @return \Illuminate\View\View
     */
    public function stats(Tenant $tenant)
    {
        $tenant->load('owner', 'plan', 'subscriptions', 'payments');
        
        // Get usage statistics
        $stats = [
            'students' => $tenant->users()->where('role', 'student')->count(),
            'instructors' => $tenant->users()->where('role', 'instructor')->count(),
            'courses' => $tenant->courses()->count(),
            'revenue' => $tenant->payments()->where('status', 'succeeded')->sum('amount'),
        ];

        // Get student signups by month
        $studentSignups = $tenant->users()
            ->where('role', 'student')
            ->select(
                DB::raw('MONTH(created_at) as month'),
                DB::raw('YEAR(created_at) as year'),
                DB::raw('COUNT(*) as count')
            )
            ->whereYear('created_at', date('Y'))
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get()
            ->map(function ($item) {
                $item->month_name = date('F', mktime(0, 0, 0, $item->month, 1));
                return $item;
            });

        // Get revenue by month
        $revenueByMonth = $tenant->payments()
            ->where('status', 'succeeded')
            ->select(
                DB::raw('MONTH(created_at) as month'),
                DB::raw('YEAR(created_at) as year'),
                DB::raw('SUM(amount) as total')
            )
            ->whereYear('created_at', date('Y'))
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get()
            ->map(function ($item) {
                $item->month_name = date('F', mktime(0, 0, 0, $item->month, 1));
                return $item;
            });

        return view('admin.tenants.stats', compact('tenant', 'stats', 'studentSignups', 'revenueByMonth'));
    }

    /**
     * Update the tenant's plan.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Tenant  $tenant
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updatePlan(Request $request, Tenant $tenant)
    {
        $request->validate([
            'plan_id' => 'required|exists:plans,id',
        ]);

        $tenant->plan_id = $request->plan_id;
        $tenant->save();

        // Update or create a subscription
        $plan = Plan::find($request->plan_id);
        $subscription = Subscription::where('tenant_id', $tenant->id)
            ->where('status', 'active')
            ->first();

        if ($subscription) {
            $subscription->plan_id = $plan->id;
            $subscription->ends_at = $plan->interval === 'monthly' ? now()->addMonth() : now()->addYear();
            $subscription->save();
        } else {
            Subscription::create([
                'tenant_id' => $tenant->id,
                'plan_id' => $plan->id,
                'status' => 'active',
                'starts_at' => now(),
                'ends_at' => $plan->interval === 'monthly' ? now()->addMonth() : now()->addYear(),
            ]);
        }

        return redirect()->route('admin.tenants.show', $tenant)
            ->with('success', 'Tenant plan updated successfully.');
    }
}
