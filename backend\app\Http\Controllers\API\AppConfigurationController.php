<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\AppConfiguration;
use App\Models\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class AppConfigurationController extends Controller
{
    /**
     * Display the app configuration for the authenticated tenant.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function show()
    {
        $user = Auth::user();
        
        // Only tenants can have app configurations
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can have app configurations'
            ], 403);
        }
        
        $appConfiguration = AppConfiguration::where('tenant_id', $user->tenant_id)->first();
        
        if (!$appConfiguration) {
            return response()->json([
                'success' => false,
                'message' => 'App configuration not found'
            ], 404);
        }
        
        return response()->json([
            'success' => true,
            'data' => $appConfiguration
        ]);
    }
    
    /**
     * Create or update the app configuration for the authenticated tenant.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'app_name' => 'required|string|max:255',
            'bundle_id' => 'required|string|max:255|regex:/^[a-z][a-z0-9_]*(\.[a-z0-9_]+)+[0-9a-z_]$/i',
            'version' => 'nullable|string|max:50',
            'branding' => 'nullable|array',
            'features' => 'nullable|array',
            'app_store' => 'nullable|array',
            'build_settings' => 'nullable|array',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
        
        $user = Auth::user();
        
        // Only tenants can have app configurations
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can have app configurations'
            ], 403);
        }
        
        // Check if bundle ID is unique
        $existingConfig = AppConfiguration::where('bundle_id', $request->input('bundle_id'))
            ->where('tenant_id', '!=', $user->tenant_id)
            ->first();
            
        if ($existingConfig) {
            return response()->json([
                'success' => false,
                'message' => 'Bundle ID is already in use'
            ], 400);
        }
        
        // Get or create app configuration
        $appConfiguration = AppConfiguration::firstOrNew(['tenant_id' => $user->tenant_id]);
        $isNew = !$appConfiguration->exists;
        
        // Set app configuration properties
        $appConfiguration->app_name = $request->input('app_name');
        $appConfiguration->bundle_id = $request->input('bundle_id');
        
        if ($request->has('version')) {
            $appConfiguration->version = $request->input('version');
        } elseif ($isNew) {
            $appConfiguration->version = '1.0.0';
        }
        
        if ($isNew) {
            $appConfiguration->build_number = '1';
            $appConfiguration->status = 'draft';
        }
        
        if ($request->has('branding')) {
            $appConfiguration->branding = $request->input('branding');
        }
        
        if ($request->has('features')) {
            $appConfiguration->features = $request->input('features');
        }
        
        if ($request->has('app_store')) {
            $appConfiguration->app_store = $request->input('app_store');
        }
        
        if ($request->has('build_settings')) {
            $appConfiguration->build_settings = $request->input('build_settings');
        }
        
        $appConfiguration->save();
        
        return response()->json([
            'success' => true,
            'message' => $isNew ? 'App configuration created successfully' : 'App configuration updated successfully',
            'data' => $appConfiguration
        ], $isNew ? 201 : 200);
    }
    
    /**
     * Generate the Expo configuration for the app.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function generateExpoConfig()
    {
        $user = Auth::user();
        
        // Only tenants can have app configurations
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can have app configurations'
            ], 403);
        }
        
        $appConfiguration = AppConfiguration::where('tenant_id', $user->tenant_id)->first();
        
        if (!$appConfiguration) {
            return response()->json([
                'success' => false,
                'message' => 'App configuration not found'
            ], 404);
        }
        
        $expoConfig = $appConfiguration->generateExpoConfig();
        
        return response()->json([
            'success' => true,
            'data' => $expoConfig
        ]);
    }
    
    /**
     * List all app configurations (admin only).
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $user = Auth::user();
        
        // Only admins can list all app configurations
        if ($user->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }
        
        $appConfigurations = AppConfiguration::with('tenant')->paginate(10);
        
        return response()->json([
            'success' => true,
            'data' => $appConfigurations
        ]);
    }
    
    /**
     * Get a specific app configuration (admin only).
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function adminShow($id)
    {
        $user = Auth::user();
        
        // Only admins can view specific app configurations
        if ($user->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }
        
        $appConfiguration = AppConfiguration::with('tenant')->find($id);
        
        if (!$appConfiguration) {
            return response()->json([
                'success' => false,
                'message' => 'App configuration not found'
            ], 404);
        }
        
        return response()->json([
            'success' => true,
            'data' => $appConfiguration
        ]);
    }
}
