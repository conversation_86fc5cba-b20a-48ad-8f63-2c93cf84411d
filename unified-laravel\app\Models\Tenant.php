<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Tenant extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'domain',
        'description',
        'logo',
        'status',
        'owner_id',
        'plan_id',
        'settings',
        'theme_id',
        'is_active',
        'expires_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'settings' => 'array',
        'is_active' => 'boolean',
        'expires_at' => 'datetime',
    ];

    /**
     * Get the owner of the tenant.
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    /**
     * Get the plan of the tenant.
     */
    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    /**
     * Get the users for the tenant.
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the courses for the tenant.
     */
    public function courses(): HasMany
    {
        return $this->hasMany(Course::class);
    }

    /**
     * Get the themes for the tenant.
     */
    public function themes(): HasMany
    {
        return $this->hasMany(Theme::class);
    }

    /**
     * Get the active theme for the tenant.
     */
    public function activeTheme(): BelongsTo
    {
        return $this->belongsTo(Theme::class, 'theme_id');
    }

    /**
     * Get the modules owned by the tenant.
     */
    public function ownedModules(): HasMany
    {
        return $this->hasMany(Module::class);
    }

    /**
     * Get the modules installed by the tenant.
     */
    public function installedModules(): BelongsToMany
    {
        return $this->belongsToMany(Module::class)
            ->withPivot('is_active', 'settings')
            ->withTimestamps();
    }

    /**
     * Get the active modules for the tenant.
     */
    public function activeModules()
    {
        return $this->installedModules()->wherePivot('is_active', true)->get();
    }

    /**
     * Get the subscriptions for the tenant.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get the active subscription for the tenant.
     */
    public function activeSubscription()
    {
        return $this->subscriptions()->where('status', 'active')->latest()->first();
    }

    /**
     * Get the full domain for the tenant.
     */
    public function getFullDomainAttribute(): string
    {
        return $this->domain . '.' . config('app.tenant_domain', 'localhost');
    }

    /**
     * Get the dashboard URL for the tenant.
     */
    public function getDashboardUrlAttribute(): string
    {
        return 'http://' . $this->full_domain . '/dashboard';
    }
}
