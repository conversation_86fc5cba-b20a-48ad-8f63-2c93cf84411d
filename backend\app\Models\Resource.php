<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Resource extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'course_id',
        'title',
        'file_url',
        'type',
    ];

    /**
     * Get the course that owns the resource.
     */
    public function course()
    {
        return $this->belongsTo(Course::class);
    }
}
