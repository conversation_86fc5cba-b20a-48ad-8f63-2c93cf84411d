<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Marketing\HomeController;
use App\Http\Controllers\Marketing\PricingController;
use App\Http\Controllers\Marketing\FeaturesController;
use App\Http\Controllers\Marketing\ThemesController;
use App\Http\Controllers\Marketing\ExtensionsController;
use App\Http\Controllers\Marketing\MobileAppController;
use App\Http\Controllers\Marketing\SignupController;
use App\Http\Controllers\Marketing\DocsController;
use App\Http\Controllers\Marketing\AboutController;
use App\Http\Controllers\Marketing\ContactController;
use App\Http\Controllers\Marketing\BlogController;

// Admin Controllers
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\TenantController as AdminTenantController;
use App\Http\Controllers\Admin\SubscriptionController;
use App\Http\Controllers\Admin\ThemeController;
use App\Http\Controllers\Admin\SettingController;
use App\Http\Controllers\Admin\AnalyticsController;
use App\Http\Controllers\Admin\ExtensionController;
use App\Http\Controllers\Admin\SupportController;
use App\Http\Controllers\Auth\LoginController;

// Tenant Controllers
use App\Http\Controllers\Tenant\DashboardController as TenantDashboardController;
use App\Http\Controllers\Tenant\CourseController as TenantCourseController;
use App\Http\Controllers\Tenant\StudentController;
use App\Http\Controllers\Tenant\SettingController as TenantSettingController;

/*
|--------------------------------------------------------------------------
| Marketing Website Routes
|--------------------------------------------------------------------------
|
| These routes are for the public-facing marketing website
|
*/

// Marketing Routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/pricing', [PricingController::class, 'index'])->name('pricing');
Route::get('/features', [FeaturesController::class, 'index'])->name('features');
Route::get('/themes', [ThemesController::class, 'index'])->name('themes');
Route::get('/extensions', [ExtensionsController::class, 'index'])->name('extensions');
Route::get('/mobile-app', [MobileAppController::class, 'index'])->name('mobile-app');
Route::get('/signup', [SignupController::class, 'index'])->name('signup');
Route::post('/signup', [SignupController::class, 'store'])->name('signup.store');
Route::get('/signup/success', [SignupController::class, 'success'])->name('signup.success');
Route::get('/docs', [DocsController::class, 'index'])->name('docs');
Route::get('/about', [AboutController::class, 'index'])->name('about');
Route::get('/contact', [ContactController::class, 'index'])->name('contact');
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');
Route::get('/blog', [BlogController::class, 'index'])->name('blog');
Route::get('/blog/{slug}', [BlogController::class, 'show'])->name('blog.show');

// Authentication Routes
Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [LoginController::class, 'login']);
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

/*
|--------------------------------------------------------------------------
| Admin Dashboard Routes
|--------------------------------------------------------------------------
|
| These routes are for the admin dashboard
|
*/

// Admin Routes
Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {
    // Dashboard
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

    // Tenant Management
    Route::resource('tenants', AdminTenantController::class);

    // User Management
    Route::get('/users', [DashboardController::class, 'users'])->name('users.index');
    Route::get('/users/create', [DashboardController::class, 'createUser'])->name('users.create');
    Route::get('/users/{id}', [DashboardController::class, 'showUser'])->name('users.show');
    Route::get('/users/{id}/edit', [DashboardController::class, 'editUser'])->name('users.edit');

    // Subscription & Plans
    Route::resource('plans', SubscriptionController::class);
    Route::resource('subscriptions', SubscriptionController::class);

    // Payments
    Route::get('/payments', [DashboardController::class, 'payments'])->name('payments.index');
    Route::get('/payments/{id}', [DashboardController::class, 'showPayment'])->name('payments.show');
    Route::get('/payment-settings', [DashboardController::class, 'paymentSettings'])->name('payments.settings');

    // Themes & Templates
    Route::resource('themes', ThemeController::class);
    Route::get('/templates', [DashboardController::class, 'templates'])->name('templates.index');
    Route::get('/templates/create', [DashboardController::class, 'createTemplate'])->name('templates.create');
    Route::get('/templates/{id}', [DashboardController::class, 'showTemplate'])->name('templates.show');
    Route::get('/templates/{id}/edit', [DashboardController::class, 'editTemplate'])->name('templates.edit');

    // Extensions
    Route::resource('extensions', ExtensionController::class);
    Route::post('/extensions/{id}/enable', [ExtensionController::class, 'enable'])->name('extensions.enable');
    Route::post('/extensions/{id}/disable', [ExtensionController::class, 'disable'])->name('extensions.disable');
    Route::post('/extensions/{id}/plans', [ExtensionController::class, 'updatePlans'])->name('extensions.plans.update');
    Route::get('/extension-marketplace', [ExtensionController::class, 'marketplace'])->name('extensions.marketplace');
    Route::get('/extension-requests', [ExtensionController::class, 'requests'])->name('extensions.requests');
    Route::post('/extension-requests/{id}/approve', [ExtensionController::class, 'approveRequest'])->name('extensions.requests.approve');
    Route::post('/extension-requests/{id}/reject', [ExtensionController::class, 'rejectRequest'])->name('extensions.requests.reject');

    // Settings
    Route::get('/settings', [SettingController::class, 'index'])->name('settings.index');
    Route::get('/settings/email', [SettingController::class, 'email'])->name('settings.email');
    Route::get('/settings/storage', [SettingController::class, 'storage'])->name('settings.storage');
    Route::get('/settings/security', [SettingController::class, 'security'])->name('settings.security');
    Route::get('/settings/api', [SettingController::class, 'api'])->name('settings.api');

    // Support & Tickets
    Route::resource('support/tickets', SupportController::class);
    Route::get('/support/email-campaigns', [SupportController::class, 'emailCampaigns'])->name('support.email-campaigns');
    Route::get('/support/email-campaigns/create', [SupportController::class, 'createEmailCampaign'])->name('support.email-campaigns.create');
    Route::get('/support/email-campaigns/{id}', [SupportController::class, 'showEmailCampaign'])->name('support.email-campaigns.show');

    // Notifications
    Route::get('/notifications', [DashboardController::class, 'notifications'])->name('notifications.index');
    Route::get('/notifications/{id}', [DashboardController::class, 'showNotification'])->name('notifications.show');
    Route::post('/notifications/{id}/mark-as-read', [DashboardController::class, 'markNotificationAsRead'])->name('notifications.mark-as-read');
    Route::post('/notifications/mark-all-as-read', [DashboardController::class, 'markAllNotificationsAsRead'])->name('notifications.mark-all-as-read');
    Route::delete('/notifications/{id}', [DashboardController::class, 'deleteNotification'])->name('notifications.delete');

    // Analytics
    Route::get('/analytics', [AnalyticsController::class, 'index'])->name('analytics.index');
    Route::get('/analytics/users', [AnalyticsController::class, 'users'])->name('analytics.users');
    Route::get('/analytics/tenants', [AnalyticsController::class, 'tenants'])->name('analytics.tenants');
    Route::get('/analytics/revenue', [AnalyticsController::class, 'revenue'])->name('analytics.revenue');
    Route::get('/analytics/usage', [AnalyticsController::class, 'usage'])->name('analytics.usage');
    Route::get('/analytics/export', [AnalyticsController::class, 'export'])->name('analytics.export');
});

/*
|--------------------------------------------------------------------------
| Tenant Dashboard Routes
|--------------------------------------------------------------------------
|
| These routes are for tenant-specific dashboards
|
*/

// Tenant Routes
Route::domain('{domain}.'.config('app.tenant_domain', 'localhost'))->middleware(['auth', 'role:tenant'])->group(function () {
    Route::get('/', [TenantDashboardController::class, 'index'])->name('tenant.dashboard');
    Route::get('/courses', [TenantCourseController::class, 'index'])->name('tenant.courses');
    Route::get('/students', [StudentController::class, 'index'])->name('tenant.students');
    Route::get('/settings', [TenantSettingController::class, 'index'])->name('tenant.settings');
});

// Fallback for tenant routes when not using subdomains
Route::prefix('tenant/{domain}')->name('tenant.')->middleware(['auth', 'role:tenant'])->group(function () {
    Route::get('/dashboard', [TenantDashboardController::class, 'index'])->name('dashboard');
    Route::get('/courses', [TenantCourseController::class, 'index'])->name('courses');
    Route::get('/students', [StudentController::class, 'index'])->name('students');
    Route::get('/settings', [TenantSettingController::class, 'index'])->name('settings');
});

// Test Route
Route::get('/test', function () {
    return view('test');
});

// Admin Login Info Route
Route::get('/admin-login-info', function () {
    return view('admin-login-info');
})->name('admin.login.info');
