<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SupportTicket extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'user_id',
        'subject',
        'message',
        'status',
        'priority',
        'category',
    ];

    /**
     * Get the tenant that owns the ticket.
     */
    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the user that created the ticket.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the replies for the ticket.
     */
    public function replies()
    {
        return $this->hasMany(SupportTicketReply::class, 'ticket_id');
    }

    /**
     * Check if the ticket is open.
     */
    public function isOpen()
    {
        return $this->status === 'open';
    }

    /**
     * Check if the ticket is closed.
     */
    public function isClosed()
    {
        return $this->status === 'closed';
    }

    /**
     * Check if the ticket is pending.
     */
    public function isPending()
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the ticket is high priority.
     */
    public function isHighPriority()
    {
        return $this->priority === 'high';
    }

    /**
     * Check if the ticket is medium priority.
     */
    public function isMediumPriority()
    {
        return $this->priority === 'medium';
    }

    /**
     * Check if the ticket is low priority.
     */
    public function isLowPriority()
    {
        return $this->priority === 'low';
    }

    /**
     * Close the ticket.
     */
    public function close()
    {
        $this->status = 'closed';
        $this->save();

        return $this;
    }

    /**
     * Reopen the ticket.
     */
    public function reopen()
    {
        $this->status = 'open';
        $this->save();

        return $this;
    }
}
