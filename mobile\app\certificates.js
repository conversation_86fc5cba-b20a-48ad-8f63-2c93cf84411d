import React, { useState, useContext } from 'react';
import { View, StyleSheet, FlatList, Image, TouchableOpacity, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

import Container from '@/components/ui/Container';
import Text from '@/components/ui/Text';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { AuthContext } from '@/context/AuthContext';
import { useTheme } from '@/components/ThemeProvider';

// Mock certificates data
const mockCertificates = [
  {
    id: 1,
    title: 'Advanced Web Development',
    issueDate: '2025-03-15',
    courseId: 1,
    image: 'https://source.unsplash.com/random?certificate&sig=1',
  },
  {
    id: 2,
    title: 'Data Science Fundamentals',
    issueDate: '2025-02-20',
    courseId: 2,
    image: 'https://source.unsplash.com/random?certificate&sig=2',
  },
];

export default function CertificatesScreen() {
  const [certificates, setCertificates] = useState(mockCertificates);
  const [loading, setLoading] = useState(false);
  const { isAuthenticated, user } = useContext(AuthContext);
  const { theme } = useTheme();

  const handleBack = () => {
    router.back();
  };

  const handleViewCertificate = (certificate) => {
    // In a real app, this would open the certificate in a PDF viewer or similar
    alert(`Viewing certificate for ${certificate.title}`);
  };

  const handleShareCertificate = (certificate) => {
    // In a real app, this would open a share dialog
    alert(`Sharing certificate for ${certificate.title}`);
  };

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const renderCertificateItem = ({ item }) => (
    <Card style={styles.certificateCard}>
      <Image 
        source={{ uri: item.image }} 
        style={styles.certificateImage}
        resizeMode="cover"
      />
      <View style={styles.certificateContent}>
        <Text variant="subtitle" style={styles.certificateTitle}>{item.title}</Text>
        <Text variant="caption" color="muted">Issued on {formatDate(item.issueDate)}</Text>
        
        <View style={styles.certificateActions}>
          <Button 
            variant="primary" 
            size="small" 
            onPress={() => handleViewCertificate(item)}
            style={styles.actionButton}
          >
            <View style={styles.buttonContent}>
              <Ionicons name="document-text-outline" size={16} color="white" />
              <Text color="light" style={styles.buttonText}>View</Text>
            </View>
          </Button>
          <Button 
            variant="outline" 
            size="small" 
            onPress={() => handleShareCertificate(item)}
            style={styles.actionButton}
          >
            <View style={styles.buttonContent}>
              <Ionicons name="share-outline" size={16} color={theme.primary} />
              <Text color="primary" style={styles.buttonText}>Share</Text>
            </View>
          </Button>
        </View>
      </View>
    </Card>
  );

  if (!isAuthenticated) {
    return (
      <Container style={styles.container}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Ionicons name="arrow-back" size={24} color={theme.text} />
        </TouchableOpacity>
        
        <View style={styles.authContainer}>
          <Text variant="h2" style={styles.title}>Certificates</Text>
          <Text style={styles.message}>Please log in to view your certificates.</Text>
          <Button 
            onPress={() => router.push('/login')}
            style={styles.loginButton}
          >
            Log In
          </Button>
        </View>
      </Container>
    );
  }

  return (
    <Container style={styles.container}>
      <TouchableOpacity style={styles.backButton} onPress={handleBack}>
        <Ionicons name="arrow-back" size={24} color={theme.text} />
      </TouchableOpacity>
      
      <Text variant="h2" style={styles.title}>Your Certificates</Text>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.primary} />
        </View>
      ) : certificates.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="ribbon-outline" size={64} color={theme.textSecondary} />
          <Text style={styles.emptyText}>
            You haven't earned any certificates yet. Complete courses to earn certificates.
          </Text>
          <Button 
            onPress={() => router.push('/explore')}
            style={styles.exploreButton}
          >
            Explore Courses
          </Button>
        </View>
      ) : (
        <FlatList
          data={certificates}
          renderItem={renderCertificateItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
        />
      )}
    </Container>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backButton: {
    position: 'absolute',
    top: 50,
    left: 16,
    zIndex: 10,
    padding: 8,
  },
  title: {
    marginTop: 60,
    marginBottom: 20,
  },
  certificateCard: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  certificateImage: {
    width: '100%',
    height: 160,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  certificateContent: {
    padding: 16,
  },
  certificateTitle: {
    fontWeight: '600',
    marginBottom: 8,
  },
  certificateActions: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginTop: 16,
  },
  actionButton: {
    marginRight: 12,
    minWidth: 100,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    marginLeft: 6,
  },
  listContent: {
    paddingTop: 16,
    paddingBottom: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    marginTop: 16,
    marginBottom: 24,
    textAlign: 'center',
  },
  exploreButton: {
    minWidth: 180,
  },
  authContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  message: {
    marginBottom: 24,
    textAlign: 'center',
  },
  loginButton: {
    minWidth: 150,
  },
});
