import React, { useState, useContext } from 'react';
import { View, StyleSheet, TouchableOpacity, Switch, Alert, ScrollView, Image } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

import Container from '@/components/ui/Container';
import Text from '@/components/ui/Text';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Card from '@/components/ui/Card';
import { AuthContext } from '@/context/AuthContext';
import { useTheme } from '@/components/ThemeProvider';

export default function AccountSettingsScreen() {
  const { isAuthenticated, user, logout } = useContext(AuthContext);
  const { theme } = useTheme();
  
  const [name, setName] = useState(user?.name || '');
  const [email, setEmail] = useState(user?.email || '');
  const [bio, setBio] = useState(user?.bio || '');
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [pushNotifications, setPushNotifications] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleBack = () => {
    router.back();
  };

  const handleSaveProfile = () => {
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      setIsEditing(false);
      Alert.alert('Success', 'Your profile has been updated successfully.');
    }, 1000);
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to log out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          onPress: () => {
            logout();
            router.replace('/');
          },
          style: 'destructive',
        },
      ]
    );
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'Delete Account',
      'Are you sure you want to delete your account? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          onPress: () => {
            Alert.alert('Account Deletion', 'In a real app, this would delete your account.');
          },
          style: 'destructive',
        },
      ]
    );
  };

  if (!isAuthenticated) {
    return (
      <Container style={styles.container}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Ionicons name="arrow-back" size={24} color={theme.text} />
        </TouchableOpacity>
        
        <View style={styles.authContainer}>
          <Text variant="h2" style={styles.title}>Account Settings</Text>
          <Text style={styles.message}>Please log in to manage your account settings.</Text>
          <Button 
            onPress={() => router.push('/login')}
            style={styles.loginButton}
          >
            Log In
          </Button>
        </View>
      </Container>
    );
  }

  return (
    <Container style={styles.container}>
      <TouchableOpacity style={styles.backButton} onPress={handleBack}>
        <Ionicons name="arrow-back" size={24} color={theme.text} />
      </TouchableOpacity>
      
      <Text variant="h2" style={styles.title}>Account Settings</Text>
      
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Card style={styles.profileCard}>
          <View style={styles.profileHeader}>
            <View style={styles.profileImageContainer}>
              {user?.avatar ? (
                <Image source={{ uri: user.avatar }} style={styles.profileImage} />
              ) : (
                <View style={[styles.profileImagePlaceholder, { backgroundColor: theme.primary }]}>
                  <Text style={styles.profileImageText}>{user?.name?.charAt(0) || 'U'}</Text>
                </View>
              )}
              {isEditing && (
                <TouchableOpacity 
                  style={[styles.editImageButton, { backgroundColor: theme.primary }]}
                  onPress={() => Alert.alert('Change Photo', 'This would open the image picker in a real app.')}
                >
                  <Ionicons name="camera" size={16} color="white" />
                </TouchableOpacity>
              )}
            </View>
            
            <View style={styles.profileInfo}>
              <Text variant="h3">{user?.name}</Text>
              <Text variant="body" color="muted">{user?.email}</Text>
            </View>
            
            {!isEditing ? (
              <TouchableOpacity 
                style={styles.editButton}
                onPress={() => setIsEditing(true)}
              >
                <Ionicons name="create-outline" size={20} color={theme.primary} />
                <Text color="primary" style={styles.editButtonText}>Edit</Text>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity 
                style={styles.cancelButton}
                onPress={() => setIsEditing(false)}
              >
                <Text color="error">Cancel</Text>
              </TouchableOpacity>
            )}
          </View>
          
          {isEditing ? (
            <View style={styles.editForm}>
              <Input
                label="Full Name"
                value={name}
                onChangeText={setName}
                placeholder="Enter your full name"
                fullWidth
              />
              
              <Input
                label="Email"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                placeholder="Enter your email"
                fullWidth
              />
              
              <Input
                label="Bio"
                value={bio}
                onChangeText={setBio}
                placeholder="Tell us about yourself"
                multiline
                numberOfLines={4}
                fullWidth
                style={styles.bioInput}
              />
              
              <Button 
                onPress={handleSaveProfile}
                loading={loading}
                style={styles.saveButton}
              >
                Save Changes
              </Button>
            </View>
          ) : (
            <View style={styles.profileDetails}>
              {user?.bio ? (
                <Text variant="body">{user.bio}</Text>
              ) : (
                <Text variant="body" color="muted">No bio provided</Text>
              )}
            </View>
          )}
        </Card>
        
        <Text variant="h4" style={styles.sectionTitle}>Notification Settings</Text>
        
        <Card style={styles.settingsCard}>
          <View style={styles.settingItem}>
            <View>
              <Text variant="subtitle">Email Notifications</Text>
              <Text variant="caption" color="muted">Receive course updates and announcements</Text>
            </View>
            <Switch
              value={emailNotifications}
              onValueChange={setEmailNotifications}
              trackColor={{ false: '#e5e7eb', true: theme.primary + '80' }}
              thumbColor={emailNotifications ? theme.primary : '#f4f3f4'}
            />
          </View>
          
          <View style={styles.divider} />
          
          <View style={styles.settingItem}>
            <View>
              <Text variant="subtitle">Push Notifications</Text>
              <Text variant="caption" color="muted">Receive alerts on your device</Text>
            </View>
            <Switch
              value={pushNotifications}
              onValueChange={setPushNotifications}
              trackColor={{ false: '#e5e7eb', true: theme.primary + '80' }}
              thumbColor={pushNotifications ? theme.primary : '#f4f3f4'}
            />
          </View>
        </Card>
        
        <Text variant="h4" style={styles.sectionTitle}>Account Management</Text>
        
        <Card style={styles.settingsCard}>
          <TouchableOpacity 
            style={styles.menuItem}
            onPress={() => router.push('/payment-methods')}
          >
            <View style={styles.menuItemContent}>
              <Ionicons name="card-outline" size={24} color={theme.text} style={styles.menuIcon} />
              <Text variant="subtitle">Payment Methods</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={theme.textSecondary} />
          </TouchableOpacity>
          
          <View style={styles.divider} />
          
          <TouchableOpacity 
            style={styles.menuItem}
            onPress={() => Alert.alert('Change Password', 'This would open the change password form in a real app.')}
          >
            <View style={styles.menuItemContent}>
              <Ionicons name="lock-closed-outline" size={24} color={theme.text} style={styles.menuIcon} />
              <Text variant="subtitle">Change Password</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={theme.textSecondary} />
          </TouchableOpacity>
          
          <View style={styles.divider} />
          
          <TouchableOpacity 
            style={styles.menuItem}
            onPress={() => router.push('/help-support')}
          >
            <View style={styles.menuItemContent}>
              <Ionicons name="help-circle-outline" size={24} color={theme.text} style={styles.menuIcon} />
              <Text variant="subtitle">Help & Support</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={theme.textSecondary} />
          </TouchableOpacity>
        </Card>
        
        <View style={styles.dangerZone}>
          <Text variant="h4" style={styles.dangerTitle}>Danger Zone</Text>
          
          <Button 
            variant="outline" 
            onPress={handleLogout}
            style={styles.logoutButton}
          >
            <View style={styles.buttonContent}>
              <Ionicons name="log-out-outline" size={20} color={theme.primary} />
              <Text color="primary" style={styles.buttonText}>Logout</Text>
            </View>
          </Button>
          
          <Button 
            variant="error" 
            onPress={handleDeleteAccount}
            style={styles.deleteButton}
          >
            <View style={styles.buttonContent}>
              <Ionicons name="trash-outline" size={20} color="white" />
              <Text color="light" style={styles.buttonText}>Delete Account</Text>
            </View>
          </Button>
        </View>
      </ScrollView>
    </Container>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backButton: {
    position: 'absolute',
    top: 50,
    left: 16,
    zIndex: 10,
    padding: 8,
  },
  title: {
    marginTop: 60,
    marginBottom: 20,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  profileCard: {
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 24,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  profileImageContainer: {
    position: 'relative',
    marginRight: 16,
  },
  profileImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  profileImagePlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileImageText: {
    color: 'white',
    fontSize: 32,
    fontWeight: 'bold',
  },
  editImageButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileInfo: {
    flex: 1,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  editButtonText: {
    marginLeft: 4,
  },
  cancelButton: {
    padding: 8,
  },
  profileDetails: {
    padding: 16,
    paddingTop: 0,
  },
  editForm: {
    padding: 16,
  },
  bioInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  saveButton: {
    marginTop: 16,
  },
  sectionTitle: {
    marginBottom: 12,
  },
  settingsCard: {
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 24,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  divider: {
    height: 1,
    backgroundColor: '#e5e7eb',
    marginHorizontal: 16,
  },
  menuItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuIcon: {
    marginRight: 12,
  },
  dangerZone: {
    marginTop: 8,
    marginBottom: 24,
  },
  dangerTitle: {
    color: '#ef4444',
    marginBottom: 16,
  },
  logoutButton: {
    marginBottom: 12,
  },
  deleteButton: {
    backgroundColor: '#ef4444',
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    marginLeft: 8,
  },
  authContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  message: {
    marginBottom: 24,
    textAlign: 'center',
  },
  loginButton: {
    minWidth: 150,
  },
});
