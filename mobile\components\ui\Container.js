import React from 'react';
import { View, StyleSheet, SafeAreaView, ScrollView, RefreshControl } from 'react-native';
import { useTheme } from '../ThemeProvider';

/**
 * Custom Container component for consistent page layout
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.scroll - Whether the container should be scrollable
 * @param {boolean} props.refreshing - Whether the container is refreshing (for ScrollView)
 * @param {function} props.onRefresh - Function to call when the user pulls to refresh
 * @param {Object} props.style - Additional style for the container
 * @param {Object} props.contentContainerStyle - Additional style for the scroll content container
 */
const Container = ({ 
  children, 
  scroll = false,
  refreshing = false,
  onRefresh,
  style,
  contentContainerStyle,
  ...props 
}) => {
  const { theme } = useTheme();
  
  const containerStyle = [
    styles.container, 
    { backgroundColor: theme.background },
    style
  ];
  
  // If scroll is true, wrap children in a ScrollView
  if (scroll) {
    return (
      <SafeAreaView style={containerStyle}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={[styles.content, contentContainerStyle]}
          showsVerticalScrollIndicator={false}
          refreshControl={
            onRefresh ? (
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[theme.primary]}
                tintColor={theme.primary}
              />
            ) : undefined
          }
          {...props}
        >
          {children}
        </ScrollView>
      </SafeAreaView>
    );
  }
  
  // Otherwise, render as a regular View
  return (
    <SafeAreaView style={containerStyle} {...props}>
      <View style={[styles.content, contentContainerStyle]}>
        {children}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
});

export default Container;
