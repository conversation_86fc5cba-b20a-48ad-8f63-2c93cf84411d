<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AnalyticsController extends Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        // Check if user is admin for all methods
        $this->middleware(function ($request, $next) {
            if (!Auth::check() || !Auth::user()->isAdmin()) {
                return redirect()->route('login')->with('error', 'You do not have permission to access this area.');
            }
            return $next($request);
        });
    }

    /**
     * Display the analytics dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get analytics data
        $data = $this->getAnalyticsData();

        return view('admin.analytics.index', compact('data'));
    }

    /**
     * Display the tenant analytics.
     *
     * @return \Illuminate\View\View
     */
    public function tenants()
    {
        // Get tenant analytics data
        $data = $this->getTenantAnalyticsData();

        return view('admin.analytics.tenants', compact('data'));
    }

    /**
     * Display the user analytics.
     *
     * @return \Illuminate\View\View
     */
    public function users()
    {
        // Get user analytics data
        $data = $this->getUserAnalyticsData();

        return view('admin.analytics.users', compact('data'));
    }

    /**
     * Display the revenue analytics.
     *
     * @return \Illuminate\View\View
     */
    public function revenue()
    {
        // Get revenue analytics data
        $data = $this->getRevenueAnalyticsData();

        return view('admin.analytics.revenue', compact('data'));
    }

    /**
     * Get analytics data.
     *
     * @return array
     */
    private function getAnalyticsData()
    {
        // Try to get real data, fall back to mock data
        try {
            $tenantCount = Tenant::count();
            $userCount = User::count();
            $activeTenantsCount = Tenant::where('status', 'active')->count();
            $pendingTenantsCount = Tenant::where('status', 'pending')->count();
            
            // Get new tenants by month
            $newTenantsByMonth = DB::table('tenants')
                ->select(DB::raw('MONTH(created_at) as month'), DB::raw('COUNT(*) as count'))
                ->whereYear('created_at', date('Y'))
                ->groupBy(DB::raw('MONTH(created_at)'))
                ->orderBy(DB::raw('MONTH(created_at)'))
                ->get()
                ->pluck('count', 'month')
                ->toArray();
            
            // Fill in missing months
            $newTenantsByMonthData = [];
            for ($i = 1; $i <= 12; $i++) {
                $newTenantsByMonthData[$i] = $newTenantsByMonth[$i] ?? 0;
            }
            
            // Get new users by month
            $newUsersByMonth = DB::table('users')
                ->select(DB::raw('MONTH(created_at) as month'), DB::raw('COUNT(*) as count'))
                ->whereYear('created_at', date('Y'))
                ->groupBy(DB::raw('MONTH(created_at)'))
                ->orderBy(DB::raw('MONTH(created_at)'))
                ->get()
                ->pluck('count', 'month')
                ->toArray();
            
            // Fill in missing months
            $newUsersByMonthData = [];
            for ($i = 1; $i <= 12; $i++) {
                $newUsersByMonthData[$i] = $newUsersByMonth[$i] ?? 0;
            }
            
            // Get tenant distribution by plan
            $tenantsByPlan = DB::table('tenants')
                ->join('plans', 'tenants.plan_id', '=', 'plans.id')
                ->select('plans.name', DB::raw('COUNT(*) as count'))
                ->groupBy('plans.name')
                ->get()
                ->pluck('count', 'name')
                ->toArray();
            
            // Get user distribution by role
            $usersByRole = DB::table('users')
                ->select('role', DB::raw('COUNT(*) as count'))
                ->groupBy('role')
                ->get()
                ->pluck('count', 'role')
                ->toArray();
            
            // If any of the above failed or returned empty, use mock data
            if (empty($tenantCount) || empty($userCount) || empty($newTenantsByMonthData) || empty($newUsersByMonthData) || empty($tenantsByPlan) || empty($usersByRole)) {
                return $this->getMockAnalyticsData();
            }
            
            return [
                'tenant_count' => $tenantCount,
                'user_count' => $userCount,
                'active_tenants_count' => $activeTenantsCount,
                'pending_tenants_count' => $pendingTenantsCount,
                'new_tenants_by_month' => $newTenantsByMonthData,
                'new_users_by_month' => $newUsersByMonthData,
                'tenants_by_plan' => $tenantsByPlan,
                'users_by_role' => $usersByRole,
            ];
        } catch (\Exception $e) {
            return $this->getMockAnalyticsData();
        }
    }

    /**
     * Get tenant analytics data.
     *
     * @return array
     */
    private function getTenantAnalyticsData()
    {
        // In a real implementation, this would query the database for tenant analytics
        return [
            'total_tenants' => 120,
            'active_tenants' => 95,
            'pending_tenants' => 15,
            'suspended_tenants' => 10,
            'tenants_by_plan' => [
                'Basic' => 45,
                'Standard' => 55,
                'Premium' => 20,
            ],
            'tenants_by_status' => [
                'active' => 95,
                'pending' => 15,
                'suspended' => 10,
            ],
            'tenants_by_month' => [
                1 => 5,
                2 => 8,
                3 => 12,
                4 => 15,
                5 => 10,
                6 => 18,
                7 => 14,
                8 => 9,
                9 => 11,
                10 => 7,
                11 => 6,
                12 => 5,
            ],
            'tenant_retention_rate' => 85,
            'average_tenant_lifetime' => 8.5, // in months
            'top_tenants' => [
                [
                    'id' => 1,
                    'name' => 'John\'s Academy',
                    'students' => 250,
                    'courses' => 15,
                    'revenue' => 1250.00,
                ],
                [
                    'id' => 2,
                    'name' => 'Smith Learning Center',
                    'students' => 180,
                    'courses' => 12,
                    'revenue' => 950.00,
                ],
                [
                    'id' => 3,
                    'name' => 'Brown Education',
                    'students' => 320,
                    'courses' => 20,
                    'revenue' => 1800.00,
                ],
                [
                    'id' => 4,
                    'name' => 'Tech Training Institute',
                    'students' => 210,
                    'courses' => 18,
                    'revenue' => 1100.00,
                ],
                [
                    'id' => 5,
                    'name' => 'Creative Arts School',
                    'students' => 150,
                    'courses' => 10,
                    'revenue' => 850.00,
                ],
            ],
        ];
    }

    /**
     * Get user analytics data.
     *
     * @return array
     */
    private function getUserAnalyticsData()
    {
        // In a real implementation, this would query the database for user analytics
        return [
            'total_users' => 2500,
            'active_users' => 2100,
            'inactive_users' => 400,
            'users_by_role' => [
                'admin' => 5,
                'tenant' => 120,
                'student' => 2375,
            ],
            'users_by_status' => [
                'active' => 2100,
                'inactive' => 300,
                'pending' => 100,
            ],
            'users_by_month' => [
                1 => 120,
                2 => 150,
                3 => 200,
                4 => 250,
                5 => 180,
                6 => 220,
                7 => 190,
                8 => 160,
                9 => 210,
                10 => 240,
                11 => 280,
                12 => 300,
            ],
            'user_retention_rate' => 78,
            'average_session_duration' => 25, // in minutes
            'most_active_tenants' => [
                [
                    'id' => 3,
                    'name' => 'Brown Education',
                    'active_users' => 320,
                    'sessions_per_user' => 12.5,
                    'avg_session_duration' => 32,
                ],
                [
                    'id' => 1,
                    'name' => 'John\'s Academy',
                    'active_users' => 250,
                    'sessions_per_user' => 10.2,
                    'avg_session_duration' => 28,
                ],
                [
                    'id' => 4,
                    'name' => 'Tech Training Institute',
                    'active_users' => 210,
                    'sessions_per_user' => 15.8,
                    'avg_session_duration' => 35,
                ],
                [
                    'id' => 2,
                    'name' => 'Smith Learning Center',
                    'active_users' => 180,
                    'sessions_per_user' => 9.5,
                    'avg_session_duration' => 22,
                ],
                [
                    'id' => 5,
                    'name' => 'Creative Arts School',
                    'active_users' => 150,
                    'sessions_per_user' => 8.3,
                    'avg_session_duration' => 20,
                ],
            ],
        ];
    }

    /**
     * Get revenue analytics data.
     *
     * @return array
     */
    private function getRevenueAnalyticsData()
    {
        // In a real implementation, this would query the database for revenue analytics
        return [
            'total_revenue' => 125000.00,
            'monthly_revenue' => 15000.00,
            'annual_growth' => 25, // percentage
            'revenue_by_plan' => [
                'Basic' => 22500.00,
                'Standard' => 52500.00,
                'Premium' => 50000.00,
            ],
            'revenue_by_month' => [
                1 => 8500.00,
                2 => 9200.00,
                3 => 10500.00,
                4 => 11800.00,
                5 => 10200.00,
                6 => 12500.00,
                7 => 11000.00,
                8 => 9800.00,
                9 => 10800.00,
                10 => 12200.00,
                11 => 13500.00,
                12 => 15000.00,
            ],
            'average_revenue_per_tenant' => 1041.67,
            'top_revenue_tenants' => [
                [
                    'id' => 3,
                    'name' => 'Brown Education',
                    'plan' => 'Premium',
                    'revenue' => 1800.00,
                    'students' => 320,
                ],
                [
                    'id' => 1,
                    'name' => 'John\'s Academy',
                    'plan' => 'Standard',
                    'revenue' => 1250.00,
                    'students' => 250,
                ],
                [
                    'id' => 4,
                    'name' => 'Tech Training Institute',
                    'plan' => 'Premium',
                    'revenue' => 1100.00,
                    'students' => 210,
                ],
                [
                    'id' => 2,
                    'name' => 'Smith Learning Center',
                    'plan' => 'Standard',
                    'revenue' => 950.00,
                    'students' => 180,
                ],
                [
                    'id' => 5,
                    'name' => 'Creative Arts School',
                    'plan' => 'Basic',
                    'revenue' => 850.00,
                    'students' => 150,
                ],
            ],
        ];
    }

    /**
     * Get mock analytics data.
     *
     * @return array
     */
    private function getMockAnalyticsData()
    {
        return [
            'tenant_count' => 120,
            'user_count' => 2500,
            'active_tenants_count' => 95,
            'pending_tenants_count' => 15,
            'new_tenants_by_month' => [
                1 => 5,
                2 => 8,
                3 => 12,
                4 => 15,
                5 => 10,
                6 => 18,
                7 => 14,
                8 => 9,
                9 => 11,
                10 => 7,
                11 => 6,
                12 => 5,
            ],
            'new_users_by_month' => [
                1 => 120,
                2 => 150,
                3 => 200,
                4 => 250,
                5 => 180,
                6 => 220,
                7 => 190,
                8 => 160,
                9 => 210,
                10 => 240,
                11 => 280,
                12 => 300,
            ],
            'tenants_by_plan' => [
                'Basic' => 45,
                'Standard' => 55,
                'Premium' => 20,
            ],
            'users_by_role' => [
                'admin' => 5,
                'tenant' => 120,
                'student' => 2375,
            ],
        ];
    }
}
