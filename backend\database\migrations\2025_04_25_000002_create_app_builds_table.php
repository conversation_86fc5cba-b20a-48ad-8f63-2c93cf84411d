<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('app_builds', function (Blueprint $table) {
            $table->id();
            $table->foreignId('app_configuration_id')->constrained()->onDelete('cascade');
            $table->foreignId('tenant_id')->constrained()->onDelete('cascade');
            $table->string('version');
            $table->string('build_number');
            $table->enum('platform', ['ios', 'android', 'both'])->default('both');
            $table->enum('environment', ['development', 'staging', 'production'])->default('development');
            $table->enum('status', ['queued', 'building', 'completed', 'failed'])->default('queued');
            $table->text('build_log')->nullable();
            $table->string('ios_build_url')->nullable();
            $table->string('android_build_url')->nullable();
            $table->json('build_metadata')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('app_builds');
    }
};
