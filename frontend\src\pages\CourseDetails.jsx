import { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Container, 
  Typography, 
  Box, 
  Grid, 
  Button, 
  Card, 
  CardContent, 
  Divider, 
  List, 
  ListItem, 
  ListItemIcon, 
  ListItemText, 
  CircularProgress, 
  Alert, 
  Paper, 
  Chip, 
  Dialog, 
  DialogActions, 
  DialogContent, 
  DialogContentText, 
  DialogTitle 
} from '@mui/material';
import PlayCircleOutlineIcon from '@mui/icons-material/PlayCircleOutline';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import SchoolIcon from '@mui/icons-material/School';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import LanguageIcon from '@mui/icons-material/Language';
import { AuthContext } from '../context/AuthContext';
import { courseService, paymentService } from '../services/api';

const CourseDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { isAuthenticated, user } = useContext(AuthContext);
  
  const [course, setCourse] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isEnrolled, setIsEnrolled] = useState(false);
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false);
  const [paymentProcessing, setPaymentProcessing] = useState(false);
  const [paymentData, setPaymentData] = useState(null);

  useEffect(() => {
    fetchCourseDetails();
  }, [id]);

  const fetchCourseDetails = async () => {
    try {
      setLoading(true);
      const response = await courseService.getCourseById(id);
      setCourse(response.data.course);
      setIsEnrolled(response.data.is_enrolled);
      setError(null);
    } catch (error) {
      console.error('Error fetching course details:', error);
      setError('Failed to load course details. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleEnroll = async () => {
    if (!isAuthenticated()) {
      navigate('/login', { state: { redirectTo: `/course/${id}` } });
      return;
    }

    try {
      if (course.price > 0) {
        // If course is paid, initiate payment
        setPaymentProcessing(true);
        const response = await paymentService.createOrder(id);
        setPaymentData(response.data);
        setPaymentDialogOpen(true);
        setPaymentProcessing(false);
      } else {
        // If course is free, enroll directly
        const response = await courseService.enrollInCourse(id);
        setIsEnrolled(true);
        navigate(`/course/${id}/learn`);
      }
    } catch (error) {
      console.error('Error enrolling in course:', error);
      setError('Failed to enroll in course. Please try again later.');
      setPaymentProcessing(false);
    }
  };

  const handlePaymentSuccess = async (paymentResponse) => {
    try {
      // Verify payment with backend
      await paymentService.verifyPayment({
        razorpay_order_id: paymentResponse.razorpay_order_id,
        razorpay_payment_id: paymentResponse.razorpay_payment_id,
        razorpay_signature: paymentResponse.razorpay_signature,
        payment_id: paymentData.payment_id
      });
      
      setIsEnrolled(true);
      setPaymentDialogOpen(false);
      navigate(`/course/${id}/learn`);
    } catch (error) {
      console.error('Error verifying payment:', error);
      setError('Payment verification failed. Please contact support.');
    }
  };

  const initializeRazorpay = () => {
    return new Promise((resolve) => {
      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.onload = () => {
        resolve(true);
      };
      script.onerror = () => {
        resolve(false);
      };
      document.body.appendChild(script);
    });
  };

  const handlePayment = async () => {
    const res = await initializeRazorpay();
    
    if (!res) {
      setError('Razorpay SDK failed to load. Please check your connection.');
      return;
    }
    
    const options = {
      key: paymentData.key_id,
      amount: paymentData.amount * 100,
      currency: paymentData.currency,
      name: "LMS Platform",
      description: `Payment for ${course.title}`,
      order_id: paymentData.order_id,
      handler: handlePaymentSuccess,
      prefill: {
        name: user.name,
        email: user.email,
      },
      theme: {
        color: "#1976d2",
      },
    };
    
    const paymentObject = new window.Razorpay(options);
    paymentObject.open();
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', my: 8 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Container maxWidth="md" sx={{ my: 4 }}>
        <Alert severity="error">{error}</Alert>
      </Container>
    );
  }

  if (!course) {
    return (
      <Container maxWidth="md" sx={{ my: 4 }}>
        <Alert severity="info">Course not found.</Alert>
      </Container>
    );
  }

  return (
    <Box>
      {/* Course Header */}
      <Box sx={{ bgcolor: 'primary.main', color: 'white', py: 6 }}>
        <Container maxWidth="lg">
          <Grid container spacing={4}>
            <Grid item xs={12} md={8}>
              <Typography variant="h3" component="h1" gutterBottom>
                {course.title}
              </Typography>
              <Typography variant="h6" gutterBottom>
                Learn from {course.instructor?.name || 'Expert Instructor'}
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mt: 2 }}>
                <Chip 
                  icon={<SchoolIcon />} 
                  label={`${course.lessons?.length || 0} Lessons`} 
                  sx={{ bgcolor: 'rgba(255,255,255,0.2)' }} 
                />
                <Chip 
                  icon={<AccessTimeIcon />} 
                  label="Self-paced" 
                  sx={{ bgcolor: 'rgba(255,255,255,0.2)' }} 
                />
                <Chip 
                  icon={<LanguageIcon />} 
                  label="English" 
                  sx={{ bgcolor: 'rgba(255,255,255,0.2)' }} 
                />
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h4" color="primary" gutterBottom>
                    ${course.price}
                  </Typography>
                  {isEnrolled ? (
                    <Button 
                      variant="contained" 
                      color="primary" 
                      fullWidth 
                      size="large"
                      onClick={() => navigate(`/course/${id}/learn`)}
                    >
                      Continue Learning
                    </Button>
                  ) : (
                    <Button 
                      variant="contained" 
                      color="primary" 
                      fullWidth 
                      size="large"
                      onClick={handleEnroll}
                      disabled={paymentProcessing}
                    >
                      {paymentProcessing ? 'Processing...' : 'Enroll Now'}
                    </Button>
                  )}
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 2, textAlign: 'center' }}>
                    {course.price > 0 ? 'Secure payment via Razorpay' : 'This course is free'}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Course Content */}
      <Container maxWidth="lg" sx={{ py: 6 }}>
        <Grid container spacing={4}>
          <Grid item xs={12} md={8}>
            <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
              <Typography variant="h5" component="h2" gutterBottom>
                About This Course
              </Typography>
              <Typography variant="body1" paragraph>
                {course.description}
              </Typography>
            </Paper>

            <Paper elevation={2} sx={{ p: 3 }}>
              <Typography variant="h5" component="h2" gutterBottom>
                Course Content
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {course.lessons?.length || 0} lessons • Approximately {Math.ceil((course.lessons?.length || 0) * 15 / 60)} hours
              </Typography>
              <Divider sx={{ my: 2 }} />
              
              {course.lessons && course.lessons.length > 0 ? (
                <List>
                  {course.lessons.map((lesson, index) => (
                    <ListItem key={lesson.id} sx={{ py: 1 }}>
                      <ListItemIcon>
                        <PlayCircleOutlineIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText 
                        primary={`${index + 1}. ${lesson.title}`} 
                        secondary={`${lesson.type.charAt(0).toUpperCase() + lesson.type.slice(1)} • 15 min`} 
                      />
                      {isEnrolled && (
                        <CheckCircleOutlineIcon color="success" />
                      )}
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography variant="body1" sx={{ textAlign: 'center', py: 4 }}>
                  No lessons available for this course yet.
                </Typography>
              )}
            </Paper>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Paper elevation={2} sx={{ p: 3, mb: 4 }}>
              <Typography variant="h6" gutterBottom>
                Instructor
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Box
                  component="img"
                  src="https://source.unsplash.com/random?person"
                  alt={course.instructor?.name}
                  sx={{ width: 60, height: 60, borderRadius: '50%', mr: 2 }}
                />
                <Box>
                  <Typography variant="subtitle1">
                    {course.instructor?.name || 'Expert Instructor'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {course.tenant?.name || 'Institution'}
                  </Typography>
                </Box>
              </Box>
              <Typography variant="body2">
                Experienced instructor with expertise in this subject area.
              </Typography>
            </Paper>
            
            <Paper elevation={2} sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                What You'll Learn
              </Typography>
              <List>
                <ListItem sx={{ py: 0.5 }}>
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    <CheckCircleOutlineIcon color="primary" fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary="Comprehensive understanding of the subject" />
                </ListItem>
                <ListItem sx={{ py: 0.5 }}>
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    <CheckCircleOutlineIcon color="primary" fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary="Practical skills you can apply immediately" />
                </ListItem>
                <ListItem sx={{ py: 0.5 }}>
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    <CheckCircleOutlineIcon color="primary" fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary="Real-world examples and case studies" />
                </ListItem>
                <ListItem sx={{ py: 0.5 }}>
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    <CheckCircleOutlineIcon color="primary" fontSize="small" />
                  </ListItemIcon>
                  <ListItemText primary="Certificate of completion" />
                </ListItem>
              </List>
            </Paper>
          </Grid>
        </Grid>
      </Container>

      {/* Payment Dialog */}
      <Dialog
        open={paymentDialogOpen}
        onClose={() => setPaymentDialogOpen(false)}
      >
        <DialogTitle>Complete Payment</DialogTitle>
        <DialogContent>
          <DialogContentText>
            To enroll in this course, please complete the payment of ${course?.price}.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPaymentDialogOpen(false)}>Cancel</Button>
          <Button onClick={handlePayment} variant="contained" color="primary">
            Pay Now
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CourseDetails;
