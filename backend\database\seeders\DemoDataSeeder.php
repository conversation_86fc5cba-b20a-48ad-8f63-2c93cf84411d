<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Tenant;
use App\Models\Course;
use App\Models\Lesson;
use App\Models\Resource;
use App\Models\Quiz;
use App\Models\QuizQuestion;
use App\Models\QuizAnswer;
use App\Models\Enrollment;
use App\Models\Payment;

class DemoDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Admin User if it doesn't exist
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => Hash::make('password'),
                'role' => 'admin',
            ]
        );

        // Create Tenants (Schools/Instructors)
        $tenants = [
            [
                'name' => 'Tech Academy',
                'domain' => 'techacademy',
                'is_active' => true,
            ],
            [
                'name' => 'Business School',
                'domain' => 'bizschool',
                'is_active' => true,
            ],
            [
                'name' => 'Design Institute',
                'domain' => 'designinstitute',
                'is_active' => true,
            ],
            [
                'name' => 'Language Center',
                'domain' => 'languagecenter',
                'is_active' => false, // Pending approval
            ],
        ];

        foreach ($tenants as $tenantData) {
            // Check if tenant already exists
            $tenant = Tenant::firstOrCreate(
                ['domain' => $tenantData['domain']],
                $tenantData
            );

            // Create tenant owner (instructor) if it doesn't exist
            $instructorEmail = 'instructor@' . $tenant->domain . '.com';
            $instructor = User::firstOrCreate(
                ['email' => $instructorEmail],
                [
                    'name' => $tenant->name . ' Instructor',
                    'password' => Hash::make('password'),
                    'role' => 'tenant',
                    'tenant_id' => $tenant->id,
                ]
            );

            // Skip course creation for inactive tenants
            if (!$tenant->is_active) {
                continue;
            }

            // Create courses for each tenant
            $coursesCount = rand(2, 4);
            for ($i = 1; $i <= $coursesCount; $i++) {
                $courseTitle = $this->getRandomCourseTitle($tenant->name);

                // Check if a similar course already exists for this tenant
                $existingCourse = Course::where('tenant_id', $tenant->id)
                    ->where('title', 'like', '%' . explode(' ', $courseTitle)[0] . '%')
                    ->first();

                if ($existingCourse) {
                    $course = $existingCourse;
                } else {
                    $course = Course::create([
                        'title' => $courseTitle,
                        'description' => $this->getRandomDescription($courseTitle),
                        'price' => rand(0, 5) === 0 ? 0 : rand(10, 100), // Some free courses
                        'tenant_id' => $tenant->id,
                        'instructor_id' => $instructor->id,
                    ]);
                }

                // Create lessons for each course if they don't exist
                $lessonsCount = rand(5, 10);

                // Check if course already has lessons
                $existingLessonsCount = Lesson::where('course_id', $course->id)->count();

                if ($existingLessonsCount === 0) {
                    for ($j = 1; $j <= $lessonsCount; $j++) {
                        $lessonTitle = "Lesson $j: " . $this->getRandomLessonTitle($courseTitle);

                        $lesson = Lesson::create([
                            'course_id' => $course->id,
                            'title' => $lessonTitle,
                            'description' => $this->getRandomDescription("Lesson $j"),
                            'video_url' => 'https://www.youtube.com/embed/dQw4w9WgXcQ', // Placeholder video
                            'type' => rand(0, 10) > 8 ? 'quiz' : 'video', // Mostly videos, some quizzes
                            'order' => $j,
                        ]);

                        // Create resources for some lessons
                        if (rand(0, 2) === 0) {
                            Resource::create([
                                'course_id' => $course->id,
                                'title' => "Resources for Lesson $j",
                                'file_url' => 'https://example.com/resources/lesson' . $j . '.pdf',
                                'type' => 'pdf',
                            ]);
                        }

                        // Create quizzes for quiz-type lessons
                        if ($lesson->type === 'quiz') {
                            $quiz = Quiz::create([
                                'course_id' => $course->id,
                                'title' => "Quiz for Lesson $j",
                                'description' => "Test your knowledge from Lesson $j",
                                'passing_score' => 70,
                            ]);

                            // Create questions for each quiz
                            $questionsCount = rand(3, 5);
                            for ($k = 1; $k <= $questionsCount; $k++) {
                                $question = QuizQuestion::create([
                                    'quiz_id' => $quiz->id,
                                    'question' => "Question $k: " . $this->getRandomQuestion($courseTitle),
                                    'type' => 'multiple_choice',
                                ]);

                                // Create answers for each question
                                $correctAnswer = rand(1, 4);
                                for ($l = 1; $l <= 4; $l++) {
                                    QuizAnswer::create([
                                        'question_id' => $question->id,
                                        'answer' => "Answer option $l",
                                        'is_correct' => $l === $correctAnswer,
                                    ]);
                                }
                            }
                        }
                    }
                }
            }
        }

        // Create regular students
        $students = [
            [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Jane Smith',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Bob Johnson',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Alice Williams',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Charlie Brown',
                'email' => '<EMAIL>',
            ],
        ];

        foreach ($students as $studentData) {
            // Create student if it doesn't exist
            $student = User::firstOrCreate(
                ['email' => $studentData['email']],
                [
                    'name' => $studentData['name'],
                    'password' => Hash::make('password'),
                    'role' => 'student',
                ]
            );

            // Enroll students in some courses
            $courses = Course::inRandomOrder()->limit(rand(2, 5))->get();
            foreach ($courses as $course) {
                $paymentStatus = rand(0, 10) > 2 ? 'completed' : 'pending'; // Most payments completed

                // Check if enrollment already exists
                $enrollment = Enrollment::firstOrCreate(
                    [
                        'user_id' => $student->id,
                        'course_id' => $course->id,
                    ],
                    [
                        'progress' => $paymentStatus === 'completed' ? rand(0, 100) : 0,
                        'payment_status' => $course->price > 0 ? $paymentStatus : 'completed', // Free courses are always completed
                    ]
                );

                // Create payment record for paid courses if it doesn't exist
                if ($course->price > 0) {
                    Payment::firstOrCreate(
                        [
                            'user_id' => $student->id,
                            'course_id' => $course->id,
                        ],
                        [
                            'amount' => $course->price,
                            'razorpay_id' => 'rzp_test_' . uniqid(),
                            'status' => $paymentStatus,
                        ]
                    );
                }
            }
        }
    }

    /**
     * Get a random course title based on the tenant name.
     */
    private function getRandomCourseTitle($tenantName): string
    {
        $techCourses = [
            'Introduction to Programming',
            'Web Development Fundamentals',
            'Advanced JavaScript',
            'Python for Beginners',
            'Data Science Essentials',
            'Machine Learning Basics',
            'Mobile App Development',
            'Cloud Computing',
            'Cybersecurity Fundamentals',
            'DevOps Practices',
        ];

        $businessCourses = [
            'Business Management',
            'Marketing Strategies',
            'Financial Accounting',
            'Entrepreneurship',
            'Project Management',
            'Human Resources',
            'Business Ethics',
            'Supply Chain Management',
            'Digital Marketing',
            'Leadership Skills',
        ];

        $designCourses = [
            'Graphic Design Basics',
            'UI/UX Design',
            'Adobe Photoshop Masterclass',
            'Illustration Techniques',
            'Typography Fundamentals',
            'Web Design Principles',
            'Motion Graphics',
            'Brand Identity Design',
            '3D Modeling',
            'Design Thinking',
        ];

        $languageCourses = [
            'English for Beginners',
            'Spanish Conversation',
            'French Language Basics',
            'German Grammar',
            'Japanese for Travelers',
            'Mandarin Chinese Fundamentals',
            'Italian Cooking and Language',
            'Russian Language Essentials',
            'Arabic Script and Pronunciation',
            'Portuguese for Business',
        ];

        if (stripos($tenantName, 'tech') !== false) {
            return $techCourses[array_rand($techCourses)];
        } elseif (stripos($tenantName, 'business') !== false) {
            return $businessCourses[array_rand($businessCourses)];
        } elseif (stripos($tenantName, 'design') !== false) {
            return $designCourses[array_rand($designCourses)];
        } elseif (stripos($tenantName, 'language') !== false) {
            return $languageCourses[array_rand($languageCourses)];
        } else {
            $allCourses = array_merge($techCourses, $businessCourses, $designCourses, $languageCourses);
            return $allCourses[array_rand($allCourses)];
        }
    }

    /**
     * Get a random lesson title based on the course title.
     */
    private function getRandomLessonTitle($courseTitle): string
    {
        $lessonTitles = [
            'Getting Started with',
            'Understanding the Basics of',
            'Advanced Concepts in',
            'Practical Applications of',
            'Case Studies in',
            'Tools and Techniques for',
            'Best Practices for',
            'Future Trends in',
            'Analyzing',
            'Mastering',
        ];

        return $lessonTitles[array_rand($lessonTitles)] . ' ' . explode(' ', $courseTitle)[0];
    }

    /**
     * Get a random question based on the course title.
     */
    private function getRandomQuestion($courseTitle): string
    {
        $questions = [
            'What is the main purpose of',
            'How would you define',
            'What are the key components of',
            'Why is it important to understand',
            'How can you apply',
            'What are the benefits of',
            'What challenges might you face when implementing',
            'How would you measure the success of',
            'What is the relationship between',
            'How has technology changed',
        ];

        return $questions[array_rand($questions)] . ' ' . $courseTitle . '?';
    }

    /**
     * Get a random description.
     */
    private function getRandomDescription($title): string
    {
        $descriptions = [
            "This comprehensive course on {title} will take you from beginner to expert. You'll learn all the essential concepts and practical skills needed to succeed.",
            "{title} is a cutting-edge subject that's in high demand. This course provides a thorough introduction with hands-on projects and real-world applications.",
            "Master {title} with this in-depth course. We cover theory and practice, with plenty of exercises to reinforce your learning.",
            "In this {title} course, you'll discover the latest techniques and best practices used by industry professionals. Perfect for beginners and intermediate learners.",
            "Take your skills to the next level with our {title} course. Designed for practical learning with immediate application to real-world scenarios.",
        ];

        return str_replace('{title}', $title, $descriptions[array_rand($descriptions)]);
    }
}
