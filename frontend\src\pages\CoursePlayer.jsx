import { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Container, 
  Typography, 
  Box, 
  Grid, 
  List, 
  ListItem, 
  ListItemButton, 
  ListItemIcon, 
  ListItemText, 
  Divider, 
  Paper, 
  CircularProgress, 
  Alert, 
  Button, 
  LinearProgress, 
  Tabs, 
  Tab 
} from '@mui/material';
import PlayCircleOutlineIcon from '@mui/icons-material/PlayCircleOutline';
import DescriptionIcon from '@mui/icons-material/Description';
import QuizIcon from '@mui/icons-material/Quiz';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { AuthContext } from '../context/AuthContext';
import { courseService, lessonService } from '../services/api';

const CoursePlayer = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useContext(AuthContext);
  
  const [course, setCourse] = useState(null);
  const [lessons, setLessons] = useState([]);
  const [currentLesson, setCurrentLesson] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [progress, setProgress] = useState(0);
  const [tabValue, setTabValue] = useState(0);
  const [resources, setResources] = useState([]);

  useEffect(() => {
    fetchCourseDetails();
    fetchLessons();
  }, [id]);

  const fetchCourseDetails = async () => {
    try {
      const response = await courseService.getCourseById(id);
      setCourse(response.data.course);
      
      if (response.data.enrollment) {
        setProgress(response.data.enrollment.progress);
      }
      
      if (!response.data.is_enrolled) {
        navigate(`/course/${id}`);
      }
    } catch (error) {
      console.error('Error fetching course details:', error);
      setError('Failed to load course details. Please try again later.');
    }
  };

  const fetchLessons = async () => {
    try {
      setLoading(true);
      const response = await lessonService.getLessonsByCourse(id);
      const sortedLessons = response.data.sort((a, b) => a.order - b.order);
      setLessons(sortedLessons);
      
      if (sortedLessons.length > 0) {
        fetchLessonDetails(sortedLessons[0].id);
      } else {
        setLoading(false);
      }
    } catch (error) {
      console.error('Error fetching lessons:', error);
      setError('Failed to load lessons. Please try again later.');
      setLoading(false);
    }
  };

  const fetchLessonDetails = async (lessonId) => {
    try {
      setLoading(true);
      const response = await lessonService.getLessonById(lessonId);
      setCurrentLesson(response.data);
      
      // Set resources (in a real app, these would come from the API)
      if (response.data.type === 'video') {
        setResources([
          { id: 1, title: 'Lesson Slides', type: 'pdf', url: '#' },
          { id: 2, title: 'Exercise Files', type: 'zip', url: '#' },
        ]);
      } else {
        setResources([]);
      }
      
      setError(null);
    } catch (error) {
      console.error('Error fetching lesson details:', error);
      setError('Failed to load lesson details. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleLessonClick = (lessonId) => {
    fetchLessonDetails(lessonId);
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const updateProgress = async (newProgress) => {
    try {
      await courseService.updateProgress(id, newProgress);
      setProgress(newProgress);
    } catch (error) {
      console.error('Error updating progress:', error);
    }
  };

  const handleComplete = () => {
    // Calculate new progress based on completed lessons
    const totalLessons = lessons.length;
    const newProgress = Math.min(Math.round((progress + (100 / totalLessons)) * 100) / 100, 100);
    updateProgress(newProgress);
  };

  const handleNextLesson = () => {
    const currentIndex = lessons.findIndex(lesson => lesson.id === currentLesson.id);
    if (currentIndex < lessons.length - 1) {
      handleLessonClick(lessons[currentIndex + 1].id);
    }
  };

  const handlePreviousLesson = () => {
    const currentIndex = lessons.findIndex(lesson => lesson.id === currentLesson.id);
    if (currentIndex > 0) {
      handleLessonClick(lessons[currentIndex - 1].id);
    }
  };

  if (loading && !currentLesson) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', my: 8 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error && !currentLesson) {
    return (
      <Container maxWidth="lg" sx={{ my: 4 }}>
        <Alert severity="error">{error}</Alert>
      </Container>
    );
  }

  return (
    <Box sx={{ bgcolor: '#f5f5f5', minHeight: 'calc(100vh - 64px)' }}>
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <Grid container spacing={3}>
          {/* Sidebar */}
          <Grid item xs={12} md={3}>
            <Paper elevation={2} sx={{ height: '100%' }}>
              <Box sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  {course?.title}
                </Typography>
                <LinearProgress variant="determinate" value={progress} sx={{ mb: 2 }} />
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {progress}% complete
                </Typography>
              </Box>
              <Divider />
              <List sx={{ pt: 0 }}>
                {lessons.map((lesson, index) => {
                  const isActive = currentLesson?.id === lesson.id;
                  const isCompleted = progress >= ((index + 1) / lessons.length) * 100;
                  
                  return (
                    <ListItem key={lesson.id} disablePadding>
                      <ListItemButton 
                        selected={isActive}
                        onClick={() => handleLessonClick(lesson.id)}
                        sx={{ 
                          borderLeft: isActive ? 3 : 0, 
                          borderColor: 'primary.main',
                          bgcolor: isActive ? 'rgba(25, 118, 210, 0.08)' : 'transparent',
                        }}
                      >
                        <ListItemIcon>
                          {lesson.type === 'video' ? (
                            <PlayCircleOutlineIcon color={isActive ? 'primary' : 'inherit'} />
                          ) : lesson.type === 'quiz' ? (
                            <QuizIcon color={isActive ? 'primary' : 'inherit'} />
                          ) : (
                            <DescriptionIcon color={isActive ? 'primary' : 'inherit'} />
                          )}
                        </ListItemIcon>
                        <ListItemText 
                          primary={`${index + 1}. ${lesson.title}`} 
                          secondary={`${lesson.type.charAt(0).toUpperCase() + lesson.type.slice(1)}`}
                        />
                        {isCompleted && (
                          <CheckCircleIcon color="success" fontSize="small" />
                        )}
                      </ListItemButton>
                    </ListItem>
                  );
                })}
              </List>
            </Paper>
          </Grid>
          
          {/* Main Content */}
          <Grid item xs={12} md={9}>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', my: 8 }}>
                <CircularProgress />
              </Box>
            ) : currentLesson ? (
              <Box>
                {/* Video Player */}
                <Paper elevation={3} sx={{ mb: 3 }}>
                  {currentLesson.type === 'video' ? (
                    <Box sx={{ position: 'relative', paddingTop: '56.25%' }}>
                      <iframe
                        src={currentLesson.video_url}
                        style={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          width: '100%',
                          height: '100%',
                          border: 0,
                        }}
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowFullScreen
                        title={currentLesson.title}
                      />
                    </Box>
                  ) : (
                    <Box sx={{ p: 4, textAlign: 'center' }}>
                      {currentLesson.type === 'quiz' ? (
                        <QuizIcon sx={{ fontSize: 80, color: 'primary.main', mb: 2 }} />
                      ) : (
                        <DescriptionIcon sx={{ fontSize: 80, color: 'primary.main', mb: 2 }} />
                      )}
                      <Typography variant="h5" gutterBottom>
                        {currentLesson.type === 'quiz' ? 'Quiz Content' : 'Text Content'}
                      </Typography>
                      <Typography variant="body1">
                        {currentLesson.description || 'Content not available in preview mode.'}
                      </Typography>
                    </Box>
                  )}
                </Paper>
                
                {/* Lesson Details */}
                <Paper elevation={2} sx={{ mb: 3 }}>
                  <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                    <Tabs value={tabValue} onChange={handleTabChange} aria-label="lesson tabs">
                      <Tab label="Overview" id="tab-0" />
                      <Tab label="Resources" id="tab-1" />
                      <Tab label="Discussion" id="tab-2" />
                    </Tabs>
                  </Box>
                  
                  {/* Overview Tab */}
                  <div role="tabpanel" hidden={tabValue !== 0} sx={{ p: 3 }}>
                    {tabValue === 0 && (
                      <Box sx={{ p: 3 }}>
                        <Typography variant="h5" gutterBottom>
                          {currentLesson.title}
                        </Typography>
                        <Typography variant="body1" paragraph>
                          {currentLesson.description || 'No description available for this lesson.'}
                        </Typography>
                      </Box>
                    )}
                  </div>
                  
                  {/* Resources Tab */}
                  <div role="tabpanel" hidden={tabValue !== 1}>
                    {tabValue === 1 && (
                      <Box sx={{ p: 3 }}>
                        <Typography variant="h6" gutterBottom>
                          Lesson Resources
                        </Typography>
                        {resources.length > 0 ? (
                          <List>
                            {resources.map((resource) => (
                              <ListItem key={resource.id}>
                                <ListItemIcon>
                                  <DescriptionIcon />
                                </ListItemIcon>
                                <ListItemText 
                                  primary={resource.title} 
                                  secondary={resource.type.toUpperCase()} 
                                />
                                <Button variant="outlined" size="small" href={resource.url}>
                                  Download
                                </Button>
                              </ListItem>
                            ))}
                          </List>
                        ) : (
                          <Typography variant="body1">
                            No resources available for this lesson.
                          </Typography>
                        )}
                      </Box>
                    )}
                  </div>
                  
                  {/* Discussion Tab */}
                  <div role="tabpanel" hidden={tabValue !== 2}>
                    {tabValue === 2 && (
                      <Box sx={{ p: 3 }}>
                        <Typography variant="h6" gutterBottom>
                          Discussion
                        </Typography>
                        <Typography variant="body1">
                          Discussion forum is not available in preview mode.
                        </Typography>
                      </Box>
                    )}
                  </div>
                </Paper>
                
                {/* Navigation Buttons */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Button 
                    variant="outlined" 
                    onClick={handlePreviousLesson}
                    disabled={lessons.findIndex(lesson => lesson.id === currentLesson.id) === 0}
                  >
                    Previous Lesson
                  </Button>
                  <Button 
                    variant="contained" 
                    color="primary" 
                    onClick={handleComplete}
                  >
                    Mark as Complete
                  </Button>
                  <Button 
                    variant="outlined" 
                    onClick={handleNextLesson}
                    disabled={lessons.findIndex(lesson => lesson.id === currentLesson.id) === lessons.length - 1}
                  >
                    Next Lesson
                  </Button>
                </Box>
              </Box>
            ) : (
              <Alert severity="info">
                No lessons available for this course yet.
              </Alert>
            )}
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default CoursePlayer;
