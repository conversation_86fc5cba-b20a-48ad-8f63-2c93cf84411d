@extends('admin.layouts.app')

@section('title', 'Support Ticket #' . $ticket->ticket_id)

@section('content')
<div class="container mx-auto px-6 py-8">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h3 class="text-3xl font-medium text-gray-700">Ticket #{{ $ticket->ticket_id }}</h3>
            <p class="mt-1 text-sm text-gray-500">{{ $ticket->subject }}</p>
        </div>
        <div class="mt-4 md:mt-0">
            <a href="{{ route('admin.support.index') }}" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-bold py-2 px-4 rounded">
                <i class="fas fa-arrow-left mr-2"></i> Back to Tickets
            </a>
        </div>
    </div>

    @if(session('success'))
    <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4 mt-4" role="alert">
        <p>{{ session('success') }}</p>
    </div>
    @endif

    @if(session('error'))
    <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 mt-4" role="alert">
        <p>{{ session('error') }}</p>
    </div>
    @endif

    <div class="mt-6 grid grid-cols-1 gap-6 lg:grid-cols-3">
        <!-- Ticket Details -->
        <div class="lg:col-span-2">
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                    <div>
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Ticket Information</h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500">Details about the support ticket.</p>
                    </div>
                    <div>
                        <span class="px-2 py-1 text-xs font-semibold rounded-full 
                            @if($ticket->status === 'open') bg-yellow-100 text-yellow-800 
                            @elseif($ticket->status === 'in_progress') bg-blue-100 text-blue-800 
                            @elseif($ticket->status === 'resolved') bg-green-100 text-green-800 
                            @else bg-gray-100 text-gray-800 @endif">
                            {{ str_replace('_', ' ', ucfirst($ticket->status)) }}
                        </span>
                        <span class="ml-2 px-2 py-1 text-xs font-semibold rounded-full 
                            @if($ticket->priority === 'low') bg-gray-100 text-gray-800 
                            @elseif($ticket->priority === 'medium') bg-blue-100 text-blue-800 
                            @elseif($ticket->priority === 'high') bg-yellow-100 text-yellow-800 
                            @else bg-red-100 text-red-800 @endif">
                            {{ ucfirst($ticket->priority) }}
                        </span>
                    </div>
                </div>
                <div class="border-t border-gray-200">
                    <dl>
                        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Submitted by</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                {{ $ticket->user->name ?? 'Unknown User' }}
                                <span class="text-gray-500">({{ $ticket->user->email ?? 'No email' }})</span>
                            </dd>
                        </div>
                        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Date submitted</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                {{ $ticket->created_at->format('F j, Y, g:i a') }}
                                <span class="text-gray-500">({{ $ticket->created_at->diffForHumans() }})</span>
                            </dd>
                        </div>
                        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Last updated</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                {{ $ticket->updated_at->format('F j, Y, g:i a') }}
                                <span class="text-gray-500">({{ $ticket->updated_at->diffForHumans() }})</span>
                            </dd>
                        </div>
                        <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Assigned to</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                @if($ticket->assignedTo)
                                    {{ $ticket->assignedTo->name }}
                                @else
                                    <span class="text-gray-500">Not assigned</span>
                                @endif
                            </dd>
                        </div>
                        <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Message</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                {{ $ticket->message }}
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>

            <!-- Conversation -->
            <div class="mt-6 bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Conversation</h3>
                </div>
                <div class="border-t border-gray-200">
                    <div class="px-4 py-5 sm:px-6">
                        <div class="space-y-6">
                            <!-- Original message -->
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <span class="inline-flex items-center justify-center h-10 w-10 rounded-full bg-gray-100">
                                        <span class="text-sm font-medium leading-none text-gray-800">
                                            {{ strtoupper(substr($ticket->user->name ?? 'User', 0, 2)) }}
                                        </span>
                                    </span>
                                </div>
                                <div class="ml-3">
                                    <div class="text-sm">
                                        <span class="font-medium text-gray-900">{{ $ticket->user->name ?? 'Unknown User' }}</span>
                                        <span class="text-gray-500 ml-2">{{ $ticket->created_at->format('M j, Y, g:i a') }}</span>
                                    </div>
                                    <div class="mt-1 text-sm text-gray-700">
                                        <p>{{ $ticket->message }}</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Replies -->
                            @foreach($ticket->replies as $reply)
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <span class="inline-flex items-center justify-center h-10 w-10 rounded-full 
                                        @if($reply->user->role === 'admin') bg-primary text-white @else bg-gray-100 text-gray-800 @endif">
                                        <span class="text-sm font-medium leading-none">
                                            {{ strtoupper(substr($reply->user->name ?? 'User', 0, 2)) }}
                                        </span>
                                    </span>
                                </div>
                                <div class="ml-3">
                                    <div class="text-sm">
                                        <span class="font-medium text-gray-900">{{ $reply->user->name ?? 'Unknown User' }}</span>
                                        @if($reply->user->role === 'admin')
                                        <span class="ml-2 px-2 py-0.5 text-xs font-semibold rounded-full bg-primary text-white">Staff</span>
                                        @endif
                                        <span class="text-gray-500 ml-2">{{ $reply->created_at->format('M j, Y, g:i a') }}</span>
                                        @if($reply->is_private)
                                        <span class="ml-2 px-2 py-0.5 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Private Note</span>
                                        @endif
                                    </div>
                                    <div class="mt-1 text-sm text-gray-700">
                                        <p>{{ $reply->message }}</p>
                                    </div>
                                </div>
                            </div>
                            @endforeach

                            <!-- Reply Form -->
                            <div class="mt-6">
                                <form action="{{ route('admin.support.reply', $ticket->ticket_id) }}" method="POST">
                                    @csrf
                                    <div>
                                        <label for="message" class="block text-sm font-medium text-gray-700">Add a reply</label>
                                        <div class="mt-1">
                                            <textarea id="message" name="message" rows="4" class="shadow-sm focus:ring-primary focus:border-primary block w-full sm:text-sm border-gray-300 rounded-md" required></textarea>
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <div class="flex items-start">
                                            <div class="flex items-center h-5">
                                                <input id="is_private" name="is_private" type="checkbox" class="focus:ring-primary h-4 w-4 text-primary border-gray-300 rounded">
                                            </div>
                                            <div class="ml-3 text-sm">
                                                <label for="is_private" class="font-medium text-gray-700">Private note</label>
                                                <p class="text-gray-500">This reply will only be visible to staff members.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-4">
                                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                            Send Reply
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ticket Actions -->
        <div class="lg:col-span-1">
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Ticket Actions</h3>
                </div>
                <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
                    <div class="space-y-6">
                        <!-- Update Status -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-500">Update Status</h4>
                            <form action="{{ route('admin.support.update-status', $ticket->ticket_id) }}" method="POST" class="mt-2">
                                @csrf
                                <div class="flex space-x-2">
                                    <select name="status" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                        <option value="open" {{ $ticket->status === 'open' ? 'selected' : '' }}>Open</option>
                                        <option value="in_progress" {{ $ticket->status === 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                        <option value="resolved" {{ $ticket->status === 'resolved' ? 'selected' : '' }}>Resolved</option>
                                        <option value="closed" {{ $ticket->status === 'closed' ? 'selected' : '' }}>Closed</option>
                                    </select>
                                    <button type="submit" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                        Update
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Update Priority -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-500">Update Priority</h4>
                            <form action="{{ route('admin.support.update-priority', $ticket->ticket_id) }}" method="POST" class="mt-2">
                                @csrf
                                <div class="flex space-x-2">
                                    <select name="priority" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                        <option value="low" {{ $ticket->priority === 'low' ? 'selected' : '' }}>Low</option>
                                        <option value="medium" {{ $ticket->priority === 'medium' ? 'selected' : '' }}>Medium</option>
                                        <option value="high" {{ $ticket->priority === 'high' ? 'selected' : '' }}>High</option>
                                        <option value="critical" {{ $ticket->priority === 'critical' ? 'selected' : '' }}>Critical</option>
                                    </select>
                                    <button type="submit" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                        Update
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Assign Ticket -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-500">Assign Ticket</h4>
                            <form action="{{ route('admin.support.assign', $ticket->ticket_id) }}" method="POST" class="mt-2">
                                @csrf
                                <div class="flex space-x-2">
                                    <select name="assigned_to" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                        <option value="">Unassigned</option>
                                        @foreach($admins as $admin)
                                        <option value="{{ $admin->id }}" {{ $ticket->assigned_to == $admin->id ? 'selected' : '' }}>{{ $admin->name }}</option>
                                        @endforeach
                                    </select>
                                    <button type="submit" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                        Assign
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Ticket Information -->
                        <div class="pt-4 border-t border-gray-200">
                            <h4 class="text-sm font-medium text-gray-500">Ticket Information</h4>
                            <dl class="mt-2 space-y-1">
                                <div class="sm:grid sm:grid-cols-3 sm:gap-4">
                                    <dt class="text-xs font-medium text-gray-500">Created</dt>
                                    <dd class="mt-1 text-xs text-gray-900 sm:mt-0 sm:col-span-2">{{ $ticket->created_at->format('M j, Y') }}</dd>
                                </div>
                                <div class="sm:grid sm:grid-cols-3 sm:gap-4">
                                    <dt class="text-xs font-medium text-gray-500">Updated</dt>
                                    <dd class="mt-1 text-xs text-gray-900 sm:mt-0 sm:col-span-2">{{ $ticket->updated_at->format('M j, Y') }}</dd>
                                </div>
                                <div class="sm:grid sm:grid-cols-3 sm:gap-4">
                                    <dt class="text-xs font-medium text-gray-500">User</dt>
                                    <dd class="mt-1 text-xs text-gray-900 sm:mt-0 sm:col-span-2">{{ $ticket->user->name ?? 'Unknown' }}</dd>
                                </div>
                                <div class="sm:grid sm:grid-cols-3 sm:gap-4">
                                    <dt class="text-xs font-medium text-gray-500">Email</dt>
                                    <dd class="mt-1 text-xs text-gray-900 sm:mt-0 sm:col-span-2">{{ $ticket->user->email ?? 'Unknown' }}</dd>
                                </div>
                                @if($ticket->user && $ticket->user->tenant)
                                <div class="sm:grid sm:grid-cols-3 sm:gap-4">
                                    <dt class="text-xs font-medium text-gray-500">Tenant</dt>
                                    <dd class="mt-1 text-xs text-gray-900 sm:mt-0 sm:col-span-2">{{ $ticket->user->tenant->name }}</dd>
                                </div>
                                @endif
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
