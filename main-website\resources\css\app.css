@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles */
.first\:mt-0:first-child {
    margin-top: 0;
}

.last\:border-b-0:last-child {
    border-bottom-width: 0;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Prose styles for documentation */
.prose {
    color: #374151;
    max-width: 65ch;
}

.prose h4 {
    font-weight: 600;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    font-size: 1.125em;
    line-height: 1.5;
}

.prose pre {
    background-color: #f3f4f6;
    border-radius: 0.375rem;
    padding: 1em;
    overflow-x: auto;
    margin-top: 1.25em;
    margin-bottom: 1.25em;
}

.prose code {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    font-size: 0.875em;
}

.prose ul {
    list-style-type: disc;
    padding-left: 1.625em;
    margin-top: 1.25em;
    margin-bottom: 1.25em;
}

.prose li {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
}
