<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class TenantController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth:api', ['except' => ['index', 'show']]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $tenants = Tenant::where('is_active', true)->get();
        return response()->json($tenants);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Check if user is admin
        $user = Auth::user();
        if ($user->role !== 'admin') {
            return response()->json(['error' => 'Unauthorized. Only admins can create tenants.'], 403);
        }

        // Validate request
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'domain' => 'required|string|unique:tenants,domain',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 400);
        }

        // Handle logo upload
        $logoPath = null;
        if ($request->hasFile('logo')) {
            $logoPath = $request->file('logo')->store('logos', 's3');
        }

        // Create tenant
        $tenant = Tenant::create([
            'name' => $request->name,
            'domain' => $request->domain,
            'logo' => $logoPath,
            'is_active' => $request->is_active ?? true,
        ]);

        return response()->json([
            'message' => 'Tenant created successfully',
            'tenant' => $tenant
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(string $id)
    {
        $tenant = Tenant::with(['courses' => function ($query) {
            $query->where('is_active', true);
        }])->findOrFail($id);

        return response()->json($tenant);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, string $id)
    {
        // Find tenant
        $tenant = Tenant::findOrFail($id);

        // Check if user is admin or the tenant owner
        $user = Auth::user();
        if ($user->role !== 'admin' && ($user->role !== 'tenant' || $user->tenant_id !== $tenant->id)) {
            return response()->json(['error' => 'Unauthorized. Only the tenant owner or admin can update this tenant.'], 403);
        }

        // Validate request
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'domain' => 'sometimes|required|string|unique:tenants,domain,' . $id,
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 400);
        }

        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo if exists
            if ($tenant->logo) {
                Storage::disk('s3')->delete($tenant->logo);
            }

            $logoPath = $request->file('logo')->store('logos', 's3');
            $tenant->logo = $logoPath;
        }

        // Update tenant
        if ($request->has('name')) {
            $tenant->name = $request->name;
        }

        if ($request->has('domain')) {
            $tenant->domain = $request->domain;
        }

        // Only admin can update is_active status
        if ($request->has('is_active') && $user->role === 'admin') {
            $tenant->is_active = $request->is_active;
        }

        $tenant->save();

        return response()->json([
            'message' => 'Tenant updated successfully',
            'tenant' => $tenant
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(string $id)
    {
        // Check if user is admin
        $user = Auth::user();
        if ($user->role !== 'admin') {
            return response()->json(['error' => 'Unauthorized. Only admins can delete tenants.'], 403);
        }

        // Find tenant
        $tenant = Tenant::findOrFail($id);

        // Delete logo if exists
        if ($tenant->logo) {
            Storage::disk('s3')->delete($tenant->logo);
        }

        // Delete tenant
        $tenant->delete();

        return response()->json(['message' => 'Tenant deleted successfully']);
    }

    /**
     * Approve a tenant.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function approve(string $id)
    {
        // Check if user is admin
        $user = Auth::user();
        if ($user->role !== 'admin') {
            return response()->json(['error' => 'Unauthorized. Only admins can approve tenants.'], 403);
        }

        // Find tenant
        $tenant = Tenant::findOrFail($id);

        // Approve tenant
        $tenant->is_active = true;
        $tenant->save();

        return response()->json([
            'message' => 'Tenant approved successfully',
            'tenant' => $tenant
        ]);
    }
}
