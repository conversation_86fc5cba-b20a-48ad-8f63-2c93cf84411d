<?php

namespace App\Http\Controllers;

use App\Models\Tenant;
use App\Models\User;
use App\Services\DomainService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class TenantController extends Controller
{
    /**
     * The domain service instance.
     *
     * @var DomainService
     */
    protected $domainService;

    /**
     * Create a new controller instance.
     *
     * @param DomainService $domainService
     * @return void
     */
    public function __construct(DomainService $domainService)
    {
        $this->domainService = $domainService;
    }
    /**
     * Display the tenant dashboard.
     *
     * @param  string  $domain
     * @return \Illuminate\View\View
     */
    public function dashboard($domain)
    {
        try {
            // Find the tenant by domain
            $tenant = Tenant::where('domain', $domain)->first();

            if (!$tenant) {
                // If tenant not found, use the test tenant
                $tenant = Tenant::where('domain', 'test-academy')->first();

                if (!$tenant) {
                    // Create a test tenant if none exists
                    $tenant = Tenant::create([
                        'name' => 'Test Academy',
                        'domain' => 'test-academy',
                        'description' => 'This is a test academy for demonstration purposes.',
                        'status' => 'active',
                        'settings' => [
                            'theme' => 'default',
                            'colors' => [
                                'primary' => '#ff7700',
                                'secondary' => '#0369a1',
                            ],
                        ],
                    ]);
                }
            }

            return view('tenant.dashboard', compact('tenant'));
        } catch (\Exception $e) {
            Log::error('Error in tenant dashboard: ' . $e->getMessage());
            return redirect()->route('home')->with('error', 'There was an error accessing the tenant dashboard.');
        }
    }

    /**
     * Display the tenant's courses.
     *
     * @param  string  $domain
     * @return \Illuminate\View\View
     */
    public function courses($domain)
    {
        try {
            // Find the tenant by domain
            $tenant = Tenant::where('domain', $domain)->first();

            if (!$tenant) {
                // If tenant not found, use the test tenant
                $tenant = Tenant::where('domain', 'test-academy')->first();

                if (!$tenant) {
                    return redirect()->route('tenant.dashboard', 'test-academy');
                }
            }

            // In a real application, you would fetch the tenant's courses
            $courses = [];

            return view('tenant.courses', compact('tenant', 'courses'));
        } catch (\Exception $e) {
            Log::error('Error in tenant courses: ' . $e->getMessage());
            return redirect()->route('tenant.dashboard', $domain)->with('error', 'There was an error accessing the courses.');
        }
    }

    /**
     * Display the tenant's students.
     *
     * @param  string  $domain
     * @return \Illuminate\View\View
     */
    public function students($domain)
    {
        try {
            // Find the tenant by domain
            $tenant = Tenant::where('domain', $domain)->first();

            if (!$tenant) {
                // If tenant not found, use the test tenant
                $tenant = Tenant::where('domain', 'test-academy')->first();

                if (!$tenant) {
                    return redirect()->route('tenant.dashboard', 'test-academy');
                }
            }

            // In a real application, you would fetch the tenant's students
            $students = [];

            return view('tenant.students', compact('tenant', 'students'));
        } catch (\Exception $e) {
            Log::error('Error in tenant students: ' . $e->getMessage());
            return redirect()->route('tenant.dashboard', $domain)->with('error', 'There was an error accessing the students.');
        }
    }

    /**
     * Display the tenant's settings.
     *
     * @param  string  $domain
     * @return \Illuminate\View\View
     */
    public function settings($domain)
    {
        try {
            // Find the tenant by domain
            $tenant = Tenant::where('domain', $domain)->first();

            if (!$tenant) {
                // If tenant not found, use the test tenant
                $tenant = Tenant::where('domain', 'test-academy')->first();

                if (!$tenant) {
                    return redirect()->route('tenant.dashboard', 'test-academy');
                }
            }

            return view('tenant.settings', compact('tenant'));
        } catch (\Exception $e) {
            Log::error('Error in tenant settings: ' . $e->getMessage());
            return redirect()->route('tenant.dashboard', $domain)->with('error', 'There was an error accessing the settings.');
        }
    }

    /**
     * Create a new tenant from the signup form.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function createFromSignup(Request $request)
    {
        try {
            // Validate the request data
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|string|email|max:255',
                'password' => 'required|string|min:8',
                'company_name' => 'required|string|max:255',
                'subdomain' => 'required|string|max:255|alpha_dash',
                'plan' => 'required|string|in:starter,professional,enterprise',
            ]);

            // Check if the email already exists
            $existingUser = User::where('email', $validated['email'])->first();
            if ($existingUser) {
                // Use the existing user
                $user = $existingUser;
            } else {
                // Create a new user
                $user = User::create([
                    'name' => $validated['name'],
                    'email' => $validated['email'],
                    'password' => Hash::make($validated['password']),
                    'role' => 'tenant',
                    'status' => 'active',
                ]);
            }

            // Check if the domain already exists
            $existingTenant = Tenant::where('domain', $validated['subdomain'])->first();
            if ($existingTenant) {
                // Use a modified domain
                $domain = $validated['subdomain'] . '-' . time();
            } else {
                $domain = $validated['subdomain'];
            }

            // Create the tenant
            $tenant = Tenant::create([
                'name' => $validated['company_name'],
                'domain' => $domain,
                'description' => 'Created from signup form',
                'status' => 'active',
                'owner_id' => $user->id,
                'settings' => [
                    'theme' => 'default',
                    'colors' => [
                        'primary' => '#ff7700',
                        'secondary' => '#0369a1',
                    ],
                ],
            ]);

            // Update the user with the tenant_id if needed
            if (!$user->tenant_id) {
                $user->tenant_id = $tenant->id;
                $user->save();
            }

            // Store signup data in session for the success page
            session([
                'signup_data' => [
                    'name' => $user->name,
                    'email' => $user->email,
                    'company_name' => $tenant->name,
                    'subdomain' => $tenant->domain,
                    'plan' => ucfirst($validated['plan']),
                ]
            ]);

            return redirect()->route('signup.success');

        } catch (\Exception $e) {
            Log::error('Error in tenant creation: ' . $e->getMessage());

            // Store signup data in session for the success page anyway
            // This is just for demonstration purposes
            session([
                'signup_data' => [
                    'name' => $request->name ?? 'Demo User',
                    'email' => $request->email ?? '<EMAIL>',
                    'company_name' => $request->company_name ?? 'Demo Academy',
                    'subdomain' => $request->subdomain ?? 'test-academy',
                    'plan' => ucfirst($request->plan ?? 'starter'),
                ]
            ]);

            return redirect()->route('signup.success');
        }
    }

    /**
     * Show the form for updating the custom domain.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function customDomain(Request $request)
    {
        try {
            // Get the tenant from the request
            $tenant = $request->tenant ?? Tenant::where('domain', 'test-academy')->first();

            if (!$tenant) {
                return redirect()->route('home')->with('error', 'Tenant not found.');
            }

            return view('tenant.settings.custom-domain', compact('tenant'));
        } catch (\Exception $e) {
            Log::error('Error in custom domain form: ' . $e->getMessage());
            return redirect()->route('tenant.settings', $tenant->domain ?? 'test-academy')
                ->with('error', 'There was an error accessing the custom domain settings.');
        }
    }

    /**
     * Update the custom domain for the tenant.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateCustomDomain(Request $request)
    {
        try {
            // Get the tenant from the request
            $tenant = $request->tenant ?? Tenant::where('domain', 'test-academy')->first();

            if (!$tenant) {
                return redirect()->route('home')->with('error', 'Tenant not found.');
            }

            // Validate the request data
            $validated = $request->validate([
                'custom_domain' => 'required|string|max:255',
            ]);

            // Check if the domain is already in use
            $existingTenant = Tenant::where('custom_domain', $validated['custom_domain'])
                ->where('id', '!=', $tenant->id)
                ->first();

            if ($existingTenant) {
                return redirect()->back()
                    ->with('error', 'This domain is already in use by another tenant.')
                    ->withInput();
            }

            // Update the tenant
            $tenant->update([
                'custom_domain' => $validated['custom_domain'],
                'custom_domain_verified' => false,
            ]);

            return redirect()->route('tenant.settings.custom-domain', $tenant->domain)
                ->with('success', 'Custom domain updated successfully. Please verify your domain by adding the required DNS records.');
        } catch (\Exception $e) {
            Log::error('Error updating custom domain: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'There was an error updating the custom domain.')
                ->withInput();
        }
    }

    /**
     * Verify the custom domain for the tenant.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function verifyCustomDomain(Request $request)
    {
        try {
            // Get the tenant from the request
            $tenant = $request->tenant ?? Tenant::where('domain', 'test-academy')->first();

            if (!$tenant) {
                return redirect()->route('home')->with('error', 'Tenant not found.');
            }

            // In a real application, you would verify the domain by checking DNS records
            // For now, we'll just mark it as verified
            $tenant->update([
                'custom_domain_verified' => true,
            ]);

            return redirect()->route('tenant.settings.custom-domain', $tenant->domain)
                ->with('success', 'Custom domain verified successfully.');
        } catch (\Exception $e) {
            Log::error('Error verifying custom domain: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'There was an error verifying the custom domain.');
        }
    }

    /**
     * Check if a domain is available.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkDomain(Request $request)
    {
        try {
            $domain = $request->input('domain');

            $exists = Tenant::where('domain', $domain)->exists();

            return response()->json(['available' => !$exists]);
        } catch (\Exception $e) {
            Log::error('Error checking domain availability: ' . $e->getMessage());
            return response()->json(['error' => 'There was an error checking domain availability.'], 500);
        }
    }
}
