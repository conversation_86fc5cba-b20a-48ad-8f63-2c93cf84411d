import React, { useState, useContext } from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

import Container from '@/components/ui/Container';
import Text from '@/components/ui/Text';
import Button from '@/components/ui/Button';
import { AuthContext } from '@/context/AuthContext';
import { useTheme } from '@/components/ThemeProvider';

// Mock notifications data
const mockNotifications = [
  {
    id: 1,
    title: 'New Course Available',
    message: 'Check out our new course on Machine Learning Fundamentals!',
    date: '2025-04-20T10:30:00Z',
    read: false,
    type: 'course',
    courseId: 6,
  },
  {
    id: 2,
    title: 'Assignment Due Soon',
    message: 'Your assignment for Advanced Web Development is due in 2 days.',
    date: '2025-04-19T15:45:00Z',
    read: true,
    type: 'assignment',
    courseId: 1,
  },
  {
    id: 3,
    title: 'Certificate Earned',
    message: 'Congratulations! You have earned a certificate for Data Science Fundamentals.',
    date: '2025-04-15T09:20:00Z',
    read: true,
    type: 'certificate',
    courseId: 2,
  },
  {
    id: 4,
    title: 'Course Discount',
    message: 'Limited time offer: 30% off on all design courses!',
    date: '2025-04-10T14:00:00Z',
    read: false,
    type: 'promotion',
  },
  {
    id: 5,
    title: 'New Comment on Your Discussion',
    message: 'Someone replied to your question in the Web Development forum.',
    date: '2025-04-05T11:15:00Z',
    read: true,
    type: 'discussion',
    courseId: 1,
  },
];

export default function NotificationsScreen() {
  const [notifications, setNotifications] = useState(mockNotifications);
  const [loading, setLoading] = useState(false);
  const { isAuthenticated, user } = useContext(AuthContext);
  const { theme } = useTheme();

  const handleBack = () => {
    router.back();
  };

  const handleNotificationPress = (notification) => {
    // Mark as read
    const updatedNotifications = notifications.map(item => 
      item.id === notification.id ? { ...item, read: true } : item
    );
    setNotifications(updatedNotifications);
    
    // Navigate based on notification type
    switch (notification.type) {
      case 'course':
        router.push(`/course/${notification.courseId}`);
        break;
      case 'certificate':
        router.push('/certificates');
        break;
      case 'assignment':
        router.push(`/course/${notification.courseId}/learn`);
        break;
      case 'discussion':
        router.push(`/course/${notification.courseId}`);
        break;
      default:
        // Just mark as read for other types
        break;
    }
  };

  const handleMarkAllAsRead = () => {
    const updatedNotifications = notifications.map(item => ({ ...item, read: true }));
    setNotifications(updatedNotifications);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return 'Today';
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'course':
        return 'book-outline';
      case 'certificate':
        return 'ribbon-outline';
      case 'assignment':
        return 'document-text-outline';
      case 'discussion':
        return 'chatbubble-outline';
      case 'promotion':
        return 'pricetag-outline';
      default:
        return 'notifications-outline';
    }
  };

  const renderNotificationItem = ({ item }) => (
    <TouchableOpacity 
      style={[
        styles.notificationItem, 
        { backgroundColor: item.read ? theme.card : theme.backgroundSecondary }
      ]}
      onPress={() => handleNotificationPress(item)}
      activeOpacity={0.7}
    >
      <View style={[styles.iconContainer, { backgroundColor: theme.primary + '20' }]}>
        <Ionicons name={getNotificationIcon(item.type)} size={24} color={theme.primary} />
      </View>
      <View style={styles.notificationContent}>
        <View style={styles.notificationHeader}>
          <Text variant="subtitle" style={styles.notificationTitle}>{item.title}</Text>
          <Text variant="caption" color="muted">{formatDate(item.date)}</Text>
        </View>
        <Text variant="body" numberOfLines={2} style={styles.notificationMessage}>
          {item.message}
        </Text>
      </View>
      {!item.read && <View style={[styles.unreadIndicator, { backgroundColor: theme.primary }]} />}
    </TouchableOpacity>
  );

  if (!isAuthenticated) {
    return (
      <Container style={styles.container}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Ionicons name="arrow-back" size={24} color={theme.text} />
        </TouchableOpacity>
        
        <View style={styles.authContainer}>
          <Text variant="h2" style={styles.title}>Notifications</Text>
          <Text style={styles.message}>Please log in to view your notifications.</Text>
          <Button 
            onPress={() => router.push('/login')}
            style={styles.loginButton}
          >
            Log In
          </Button>
        </View>
      </Container>
    );
  }

  return (
    <Container style={styles.container}>
      <TouchableOpacity style={styles.backButton} onPress={handleBack}>
        <Ionicons name="arrow-back" size={24} color={theme.text} />
      </TouchableOpacity>
      
      <View style={styles.header}>
        <Text variant="h2" style={styles.title}>Notifications</Text>
        {notifications.some(n => !n.read) && (
          <TouchableOpacity onPress={handleMarkAllAsRead}>
            <Text color="primary">Mark all as read</Text>
          </TouchableOpacity>
        )}
      </View>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.primary} />
        </View>
      ) : notifications.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="notifications-off-outline" size={64} color={theme.textSecondary} />
          <Text style={styles.emptyText}>
            You don't have any notifications yet.
          </Text>
        </View>
      ) : (
        <FlatList
          data={notifications}
          renderItem={renderNotificationItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
        />
      )}
    </Container>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backButton: {
    position: 'absolute',
    top: 50,
    left: 16,
    zIndex: 10,
    padding: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 60,
    marginBottom: 20,
  },
  title: {
    flex: 1,
  },
  notificationItem: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    alignItems: 'center',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  notificationContent: {
    flex: 1,
  },
  notificationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  notificationTitle: {
    fontWeight: '600',
    flex: 1,
  },
  notificationMessage: {
    lineHeight: 20,
  },
  unreadIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginLeft: 8,
  },
  listContent: {
    paddingBottom: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    marginTop: 16,
    textAlign: 'center',
  },
  authContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  message: {
    marginBottom: 24,
    textAlign: 'center',
  },
  loginButton: {
    minWidth: 150,
  },
});
