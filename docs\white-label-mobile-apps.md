# White-label Mobile Apps

The White-label Mobile Apps feature allows tenants to create and customize their own branded mobile applications for iOS and Android platforms. This feature enables tenants to extend their educational platform to mobile devices with their own branding, features, and distribution.

## Features

- Custom branding (app name, icons, splash screens, colors)
- Feature configuration (enabled/disabled features)
- App Store configuration (screenshots, descriptions, keywords)
- Build management for iOS and Android platforms
- Development, staging, and production environments
- Build status tracking and logs

## Implementation Details

### Database Structure

The White-label Mobile Apps feature is built on the following database tables:

- **AppConfiguration**: Stores the configuration for a tenant's mobile app
- **AppBuild**: Tracks build history and status for each app

### API Endpoints

#### App Configuration Endpoints

- `GET /api/app-builder/configuration`: Get the app configuration for the authenticated tenant
- `POST /api/app-builder/configuration`: Create or update the app configuration
- `GET /api/app-builder/configuration/expo-config`: Generate the Expo configuration for the app

#### App Build Endpoints

- `GET /api/app-builder/builds`: Get builds for the authenticated tenant
- `POST /api/app-builder/builds`: Create a new build
- `GET /api/app-builder/builds/{id}`: Get a specific build

#### Admin Endpoints

- `GET /api/app-builder/configurations`: List all app configurations (admin only)
- `GET /api/app-builder/configurations/{id}`: Get a specific app configuration (admin only)
- `GET /api/app-builder/all-builds`: List all builds (admin only)
- `GET /api/app-builder/all-builds/{id}`: Get a specific build (admin only)

### Build Process

The build process for white-label mobile apps is handled by Expo's EAS Build service. The process involves:

1. **Configuration**: Tenant configures their app (branding, features, etc.)
2. **Build Initiation**: Tenant initiates a build for iOS, Android, or both
3. **Build Processing**: System generates app.json and other necessary files
4. **EAS Build**: System triggers EAS Build to create the app binaries
5. **Distribution**: Completed builds are made available for download or app store submission

### App Customization

Tenants can customize various aspects of their mobile app:

#### Branding

- App name
- App icon
- Splash screen
- Theme colors
- Fonts

#### Features

- Enabled/disabled features
- Feature configuration
- Navigation structure

#### App Store

- App store screenshots
- App description
- Keywords
- Privacy policy URL
- Support URL

## Usage

### For Tenants

1. Navigate to the App Builder section
2. Configure your app (branding, features, etc.)
3. Initiate a build for iOS, Android, or both
4. Monitor build status
5. Download completed builds or submit to app stores

### For Administrators

1. Monitor all tenant app configurations and builds
2. Assist tenants with build issues
3. Manage build quotas and limits

## Future Enhancements

- App store submission automation
- Over-the-air updates
- Push notification configuration
- Analytics integration
- Advanced theming options
