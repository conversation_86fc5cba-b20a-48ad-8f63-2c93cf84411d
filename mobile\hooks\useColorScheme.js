import { useColorScheme as _useColorScheme } from 'react-native';

// The useColorScheme value is always either light or dark, but the built-in
// type suggests that it can be null. This will not happen in practice, so this
// makes it a bit easier to work with.
export function useColorScheme() {
  // Always use light theme for now, as per user's request
  return 'light';
  
  // Uncomment this to use the device's color scheme
  // return _useColorScheme() || 'light';
}
