<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class AboutController extends Controller
{
    /**
     * Display the about us page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $team = [
            [
                'name' => '<PERSON>',
                'position' => 'CEO & Founder',
                'bio' => '<PERSON> has over 15 years of experience in education technology and is passionate about making education accessible to everyone.',
                'image' => 'img/marketing/team/john.jpg',
            ],
            [
                'name' => '<PERSON>',
                'position' => 'CTO',
                'bio' => '<PERSON> leads our technology team and has a background in building scalable software solutions for educational institutions.',
                'image' => 'img/marketing/team/jane.jpg',
            ],
            [
                'name' => '<PERSON>',
                'position' => 'Head of Product',
                'bio' => '<PERSON> oversees product development and ensures our platform meets the needs of educators and learners worldwide.',
                'image' => 'img/marketing/team/michael.jpg',
            ],
            [
                'name' => '<PERSON>',
                'position' => 'Head of Customer Success',
                'bio' => '<PERSON> and her team ensure that all our customers get the most out of our platform and achieve their educational goals.',
                'image' => 'img/marketing/team/sarah.jpg',
            ],
        ];

        $mission = 'Our mission is to democratize education by providing powerful, accessible tools for educators and institutions to create and deliver high-quality online learning experiences.';
        
        $vision = 'We envision a world where quality education is accessible to everyone, regardless of location or resources, through technology that empowers educators and engages learners.';
        
        $values = [
            'Accessibility' => 'We believe education should be accessible to all.',
            'Innovation' => 'We continuously innovate to improve the learning experience.',
            'Quality' => 'We are committed to providing high-quality tools and resources.',
            'Community' => 'We foster a supportive community of educators and learners.',
            'Empowerment' => 'We empower educators to create impactful learning experiences.',
        ];

        return view('marketing.about', compact('team', 'mission', 'vision', 'values'));
    }
}
