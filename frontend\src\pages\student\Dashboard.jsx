import React, { useState, useEffect, useContext } from 'react';
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  Button,
  LinearProgress,
  Divider,
  Chip,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Tab,
  Tabs
} from '@mui/material';
import {
  PlayCircleOutline as PlayCircleOutlineIcon,
  School as SchoolIcon,
  CheckCircle as CheckCircleIcon,
  AccessTime as AccessTimeIcon,
  Bookmark as BookmarkIcon,
  Star as StarIcon,
  Search as SearchIcon
} from '@mui/icons-material';
import { Link as RouterLink } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { courseService } from '../../services/api';

const StudentDashboard = () => {
  const { user } = useContext(AuthContext);
  const [loading, setLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);
  const [enrolledCourses, setEnrolledCourses] = useState([]);
  const [completedCourses, setCompletedCourses] = useState([]);
  const [inProgressCourses, setInProgressCourses] = useState([]);
  const [bookmarkedCourses, setBookmarkedCourses] = useState([]);
  const [recentActivities, setRecentActivities] = useState([]);
  const [recommendedCourses, setRecommendedCourses] = useState([]);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        
        // In a real implementation, you would fetch actual data from your API
        // const enrolledResponse = await courseService.getEnrolledCourses();
        // const activitiesResponse = await courseService.getRecentActivities();
        // const recommendedResponse = await courseService.getRecommendedCourses();
        
        // Simulate API response
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const mockEnrolledCourses = [
          {
            id: 1,
            title: 'Advanced Web Development',
            description: 'Learn modern web development techniques with React, Node.js, and MongoDB.',
            thumbnail: 'https://source.unsplash.com/random?web',
            progress: 75,
            instructor: 'John Smith',
            total_lessons: 24,
            completed_lessons: 18,
            is_completed: false,
            last_accessed: '2025-04-18T14:30:00Z'
          },
          {
            id: 2,
            title: 'Data Science Fundamentals',
            description: 'Master the basics of data science, including statistics, Python, and machine learning.',
            thumbnail: 'https://source.unsplash.com/random?data',
            progress: 40,
            instructor: 'Emily Johnson',
            total_lessons: 32,
            completed_lessons: 13,
            is_completed: false,
            last_accessed: '2025-04-20T09:15:00Z'
          },
          {
            id: 3,
            title: 'UI/UX Design Principles',
            description: 'Learn the fundamentals of user interface and user experience design.',
            thumbnail: 'https://source.unsplash.com/random?design',
            progress: 100,
            instructor: 'Michael Brown',
            total_lessons: 18,
            completed_lessons: 18,
            is_completed: true,
            last_accessed: '2025-04-15T16:45:00Z'
          },
          {
            id: 4,
            title: 'Digital Marketing Basics',
            description: 'Understand the fundamentals of digital marketing, including SEO, social media, and content marketing.',
            thumbnail: 'https://source.unsplash.com/random?marketing',
            progress: 100,
            instructor: 'Sarah Wilson',
            total_lessons: 20,
            completed_lessons: 20,
            is_completed: true,
            last_accessed: '2025-04-10T11:20:00Z'
          },
          {
            id: 5,
            title: 'Mobile App Development with Flutter',
            description: 'Build cross-platform mobile applications using Flutter and Dart.',
            thumbnail: 'https://source.unsplash.com/random?mobile',
            progress: 10,
            instructor: 'David Lee',
            total_lessons: 28,
            completed_lessons: 3,
            is_completed: false,
            last_accessed: '2025-04-21T08:00:00Z'
          }
        ];
        
        const mockRecentActivities = [
          {
            id: 1,
            type: 'lesson_completed',
            course_id: 1,
            course_title: 'Advanced Web Development',
            lesson_title: 'React Hooks in Depth',
            timestamp: '2025-04-21T10:30:00Z'
          },
          {
            id: 2,
            type: 'quiz_completed',
            course_id: 1,
            course_title: 'Advanced Web Development',
            quiz_title: 'JavaScript ES6 Features',
            score: 85,
            timestamp: '2025-04-20T15:45:00Z'
          },
          {
            id: 3,
            type: 'course_started',
            course_id: 5,
            course_title: 'Mobile App Development with Flutter',
            timestamp: '2025-04-21T08:00:00Z'
          },
          {
            id: 4,
            type: 'certificate_earned',
            course_id: 3,
            course_title: 'UI/UX Design Principles',
            timestamp: '2025-04-15T16:50:00Z'
          },
          {
            id: 5,
            type: 'lesson_completed',
            course_id: 2,
            course_title: 'Data Science Fundamentals',
            lesson_title: 'Introduction to Pandas',
            timestamp: '2025-04-19T14:20:00Z'
          }
        ];
        
        const mockRecommendedCourses = [
          {
            id: 6,
            title: 'Machine Learning Masterclass',
            description: 'Dive deep into machine learning algorithms and applications.',
            thumbnail: 'https://source.unsplash.com/random?machine',
            instructor: 'Robert Chen',
            rating: 4.8,
            review_count: 245,
            price: 89.99
          },
          {
            id: 7,
            title: 'Advanced React Patterns',
            description: 'Learn advanced React patterns and best practices for building scalable applications.',
            thumbnail: 'https://source.unsplash.com/random?react',
            instructor: 'Jessica Miller',
            rating: 4.9,
            review_count: 189,
            price: 79.99
          },
          {
            id: 8,
            title: 'DevOps for Developers',
            description: 'Master DevOps practices and tools for modern software development.',
            thumbnail: 'https://source.unsplash.com/random?devops',
            instructor: 'Thomas Wilson',
            rating: 4.7,
            review_count: 156,
            price: 99.99
          },
          {
            id: 9,
            title: 'Blockchain Development',
            description: 'Learn to build decentralized applications using blockchain technology.',
            thumbnail: 'https://source.unsplash.com/random?blockchain',
            instructor: 'Amanda Johnson',
            rating: 4.6,
            review_count: 132,
            price: 109.99
          }
        ];
        
        setEnrolledCourses(mockEnrolledCourses);
        setCompletedCourses(mockEnrolledCourses.filter(course => course.is_completed));
        setInProgressCourses(mockEnrolledCourses.filter(course => !course.is_completed));
        setBookmarkedCourses([mockEnrolledCourses[0], mockEnrolledCourses[2]]);
        setRecentActivities(mockRecentActivities);
        setRecommendedCourses(mockRecommendedCourses);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case 'lesson_completed':
        return <CheckCircleIcon color="success" />;
      case 'quiz_completed':
        return <SchoolIcon color="primary" />;
      case 'course_started':
        return <PlayCircleOutlineIcon color="info" />;
      case 'certificate_earned':
        return <StarIcon color="warning" />;
      default:
        return <AccessTimeIcon />;
    }
  };

  const getActivityText = (activity) => {
    switch (activity.type) {
      case 'lesson_completed':
        return `Completed lesson: ${activity.lesson_title} in ${activity.course_title}`;
      case 'quiz_completed':
        return `Completed quiz: ${activity.quiz_title} in ${activity.course_title} with score ${activity.score}%`;
      case 'course_started':
        return `Started new course: ${activity.course_title}`;
      case 'certificate_earned':
        return `Earned certificate for completing ${activity.course_title}`;
      default:
        return `Activity in ${activity.course_title}`;
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Welcome back, {user?.name || 'Student'}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Continue your learning journey
        </Typography>
      </Box>
      
      {/* Continue Learning Section */}
      {inProgressCourses.length > 0 && (
        <Box sx={{ mb: 4 }}>
          <Typography variant="h5" gutterBottom>
            Continue Learning
          </Typography>
          <Paper sx={{ p: 3 }}>
            <Grid container spacing={3}>
              {inProgressCourses.slice(0, 1).map((course) => (
                <Grid item xs={12} key={course.id}>
                  <Card sx={{ display: { xs: 'block', md: 'flex' } }}>
                    <CardMedia
                      component="img"
                      sx={{ width: { xs: '100%', md: 240 }, height: { xs: 200, md: 'auto' } }}
                      image={course.thumbnail}
                      alt={course.title}
                    />
                    <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                      <CardContent sx={{ flex: '1 0 auto' }}>
                        <Typography variant="h5" component="div">
                          {course.title}
                        </Typography>
                        <Typography variant="subtitle1" color="text.secondary" gutterBottom>
                          by {course.instructor}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <LinearProgress 
                            variant="determinate" 
                            value={course.progress} 
                            sx={{ flexGrow: 1, mr: 2 }} 
                          />
                          <Typography variant="body2">
                            {course.progress}%
                          </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          {course.completed_lessons} of {course.total_lessons} lessons completed
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Last accessed: {formatDate(course.last_accessed)}
                        </Typography>
                      </CardContent>
                      <CardActions sx={{ p: 2 }}>
                        <Button
                          variant="contained"
                          color="primary"
                          startIcon={<PlayCircleOutlineIcon />}
                          component={RouterLink}
                          to={`/courses/${course.id}/learn`}
                        >
                          Continue Learning
                        </Button>
                      </CardActions>
                    </Box>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Box>
      )}
      
      {/* My Courses Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" gutterBottom>
          My Courses
        </Typography>
        <Paper sx={{ p: 3 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            sx={{ mb: 3 }}
          >
            <Tab label="All Courses" />
            <Tab label="In Progress" />
            <Tab label="Completed" />
            <Tab label="Bookmarked" />
          </Tabs>
          
          <Grid container spacing={3}>
            {(tabValue === 0 ? enrolledCourses :
              tabValue === 1 ? inProgressCourses :
              tabValue === 2 ? completedCourses :
              bookmarkedCourses).map((course) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={course.id}>
                <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                  <CardMedia
                    component="img"
                    height="140"
                    image={course.thumbnail}
                    alt={course.title}
                  />
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Typography variant="h6" component="div" gutterBottom noWrap>
                      {course.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2, height: 40, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                      {course.description}
                    </Typography>
                    <Box sx={{ mb: 1 }}>
                      <LinearProgress 
                        variant="determinate" 
                        value={course.progress} 
                        sx={{ mb: 1 }} 
                      />
                      <Typography variant="body2" color="text.secondary">
                        {course.progress}% Complete
                      </Typography>
                    </Box>
                    {course.is_completed && (
                      <Chip 
                        icon={<CheckCircleIcon />} 
                        label="Completed" 
                        color="success" 
                        size="small" 
                        sx={{ mt: 1 }} 
                      />
                    )}
                  </CardContent>
                  <Divider />
                  <CardActions>
                    <Button
                      size="small"
                      startIcon={<PlayCircleOutlineIcon />}
                      component={RouterLink}
                      to={`/courses/${course.id}/learn`}
                    >
                      {course.is_completed ? 'Review' : 'Continue'}
                    </Button>
                    <Button
                      size="small"
                      startIcon={<BookmarkIcon />}
                    >
                      Bookmark
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
            
            {(tabValue === 0 && enrolledCourses.length === 0) ||
             (tabValue === 1 && inProgressCourses.length === 0) ||
             (tabValue === 2 && completedCourses.length === 0) ||
             (tabValue === 3 && bookmarkedCourses.length === 0) ? (
              <Grid item xs={12}>
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant="h6" gutterBottom>
                    No courses found
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    {tabValue === 0 ? "You haven't enrolled in any courses yet" :
                     tabValue === 1 ? "You don't have any courses in progress" :
                     tabValue === 2 ? "You haven't completed any courses yet" :
                     "You haven't bookmarked any courses yet"}
                  </Typography>
                  {tabValue === 0 && (
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<SearchIcon />}
                      component={RouterLink}
                      to="/courses"
                      sx={{ mt: 2 }}
                    >
                      Browse Courses
                    </Button>
                  )}
                </Box>
              </Grid>
            ) : null}
          </Grid>
        </Paper>
      </Box>
      
      <Grid container spacing={4}>
        {/* Recent Activity Section */}
        <Grid item xs={12} md={6}>
          <Typography variant="h5" gutterBottom>
            Recent Activity
          </Typography>
          <Paper sx={{ p: 3 }}>
            <List>
              {recentActivities.map((activity) => (
                <React.Fragment key={activity.id}>
                  <ListItem alignItems="flex-start">
                    <ListItemAvatar>
                      <Avatar>
                        {getActivityIcon(activity.type)}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={getActivityText(activity)}
                      secondary={formatDate(activity.timestamp)}
                    />
                  </ListItem>
                  <Divider variant="inset" component="li" />
                </React.Fragment>
              ))}
            </List>
          </Paper>
        </Grid>
        
        {/* Recommended Courses Section */}
        <Grid item xs={12} md={6}>
          <Typography variant="h5" gutterBottom>
            Recommended For You
          </Typography>
          <Paper sx={{ p: 3 }}>
            <List>
              {recommendedCourses.slice(0, 3).map((course) => (
                <React.Fragment key={course.id}>
                  <ListItem alignItems="flex-start">
                    <ListItemAvatar>
                      <Avatar 
                        variant="rounded"
                        src={course.thumbnail}
                        alt={course.title}
                        sx={{ width: 60, height: 60, mr: 1 }}
                      />
                    </ListItemAvatar>
                    <ListItemText
                      primary={course.title}
                      secondary={
                        <>
                          <Typography variant="body2" component="span" color="text.primary">
                            by {course.instructor}
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                            <StarIcon fontSize="small" color="warning" />
                            <Typography variant="body2" sx={{ ml: 0.5, mr: 1 }}>
                              {course.rating} ({course.review_count} reviews)
                            </Typography>
                            <Typography variant="body2" color="primary.main" fontWeight="bold">
                              ${course.price}
                            </Typography>
                          </Box>
                        </>
                      }
                    />
                    <Button
                      variant="outlined"
                      size="small"
                      component={RouterLink}
                      to={`/courses/${course.id}`}
                      sx={{ ml: 2, alignSelf: 'center' }}
                    >
                      View
                    </Button>
                  </ListItem>
                  <Divider variant="inset" component="li" />
                </React.Fragment>
              ))}
            </List>
            <Box sx={{ textAlign: 'center', mt: 2 }}>
              <Button
                variant="text"
                component={RouterLink}
                to="/courses"
              >
                Browse All Courses
              </Button>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default StudentDashboard;
