<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ExtensionsController extends Controller
{
    /**
     * Display the extensions page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $categories = [
            'Learning Tools',
            'Engagement',
            'Analytics',
            'Integrations',
            'E-commerce',
            'Communication',
        ];

        $extensions = [
            [
                'name' => 'Certificate Generator',
                'description' => 'Automatically generate and issue certificates when students complete courses.',
                'price' => 29,
                'is_free' => false,
                'category' => 'Learning Tools',
                'thumbnail' => 'https://source.unsplash.com/random/800x600/?certificate',
                'features' => [
                    'Customizable certificate templates',
                    'Automatic issuance upon course completion',
                    'PDF and image formats',
                    'Verification system',
                    'Social sharing',
                ],
                'popular' => true,
            ],
            [
                'name' => 'Gamification',
                'description' => 'Add points, badges, and leaderboards to increase student engagement.',
                'price' => 39,
                'is_free' => false,
                'category' => 'Engagement',
                'thumbnail' => 'https://source.unsplash.com/random/800x600/?game',
                'features' => [
                    'Points system',
                    'Customizable badges',
                    'Leaderboards',
                    'Achievement tracking',
                    'Progress visualization',
                ],
                'popular' => true,
            ],
            [
                'name' => 'Advanced Analytics',
                'description' => 'Get deeper insights into student behavior and course performance.',
                'price' => 49,
                'is_free' => false,
                'category' => 'Analytics',
                'thumbnail' => 'https://source.unsplash.com/random/800x600/?analytics',
                'features' => [
                    'Student engagement metrics',
                    'Course performance analytics',
                    'Custom reports',
                    'Data export',
                    'Predictive analytics',
                ],
                'popular' => false,
            ],
            [
                'name' => 'Zoom Integration',
                'description' => 'Seamlessly integrate Zoom for live classes and webinars.',
                'price' => 0,
                'is_free' => true,
                'category' => 'Integrations',
                'thumbnail' => 'https://source.unsplash.com/random/800x600/?video-conference',
                'features' => [
                    'Schedule Zoom meetings',
                    'Automatic recording',
                    'Student notifications',
                    'Attendance tracking',
                ],
                'popular' => false,
            ],
            [
                'name' => 'Social Learning',
                'description' => 'Add social features like discussion forums, student profiles, and messaging.',
                'price' => 39,
                'is_free' => false,
                'category' => 'Engagement',
                'thumbnail' => 'https://source.unsplash.com/random/800x600/?social',
                'features' => [
                    'Discussion forums',
                    'Student profiles',
                    'Direct messaging',
                    'Study groups',
                    'Social sharing',
                ],
                'popular' => false,
            ],
            [
                'name' => 'Subscription Management',
                'description' => 'Manage recurring subscriptions and membership plans.',
                'price' => 49,
                'is_free' => false,
                'category' => 'E-commerce',
                'thumbnail' => 'https://source.unsplash.com/random/800x600/?subscription',
                'features' => [
                    'Multiple subscription plans',
                    'Recurring billing',
                    'Trial periods',
                    'Coupon management',
                    'Subscription analytics',
                ],
                'popular' => true,
            ],
            [
                'name' => 'Email Notifications',
                'description' => 'Send automated email notifications to students and instructors.',
                'price' => 0,
                'is_free' => true,
                'category' => 'Communication',
                'thumbnail' => 'https://source.unsplash.com/random/800x600/?email',
                'features' => [
                    'Customizable email templates',
                    'Automated notifications',
                    'Scheduled emails',
                    'Email analytics',
                ],
                'popular' => false,
            ],
            [
                'name' => 'Quiz Builder',
                'description' => 'Create interactive quizzes and assessments with various question types.',
                'price' => 29,
                'is_free' => false,
                'category' => 'Learning Tools',
                'thumbnail' => 'https://source.unsplash.com/random/800x600/?quiz',
                'features' => [
                    'Multiple question types',
                    'Timed quizzes',
                    'Randomized questions',
                    'Instant feedback',
                    'Detailed results',
                ],
                'popular' => false,
            ],
        ];

        return view('marketing.extensions', compact('categories', 'extensions'));
    }
}
