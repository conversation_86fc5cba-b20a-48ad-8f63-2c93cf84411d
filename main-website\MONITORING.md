# Monitoring and Maintenance Guide

This guide provides instructions for monitoring and maintaining the Naxofy LMS platform in production.

## Monitoring Strategy

### Key Metrics to Monitor

#### System Metrics

| Metric | Description | Warning Threshold | Critical Threshold |
|--------|-------------|-------------------|-------------------|
| CPU Usage | Percentage of CPU being used | 70% | 90% |
| Memory Usage | Percentage of memory being used | 80% | 95% |
| Disk Usage | Percentage of disk space being used | 75% | 90% |
| Load Average | System load over time | 5 | 10 |
| Network Traffic | Inbound/outbound network traffic | Varies | Varies |

#### Application Metrics

| Metric | Description | Warning Threshold | Critical Threshold |
|--------|-------------|-------------------|-------------------|
| Response Time | Time to respond to requests | 500ms | 2000ms |
| Error Rate | Percentage of requests resulting in errors | 1% | 5% |
| Request Rate | Number of requests per second | Varies | Varies |
| Queue Length | Number of jobs in the queue | 100 | 1000 |
| Queue Processing Time | Time to process jobs | 5s | 30s |

#### Database Metrics

| Metric | Description | Warning Threshold | Critical Threshold |
|--------|-------------|-------------------|-------------------|
| Query Time | Time to execute queries | 100ms | 1000ms |
| Connection Count | Number of active connections | 80% of max | 95% of max |
| Index Usage | Percentage of queries using indexes | < 90% | < 75% |
| Table Size | Size of database tables | Varies | Varies |
| Replication Lag | Lag between primary and replicas | 10s | 60s |

#### Business Metrics

| Metric | Description | Goal |
|--------|-------------|------|
| User Registrations | Number of new user registrations | Increasing trend |
| Course Enrollments | Number of course enrollments | Increasing trend |
| Course Completions | Number of course completions | Increasing trend |
| Revenue | Total revenue generated | Increasing trend |
| Active Users | Number of active users | Increasing trend |

### Monitoring Tools

#### System Monitoring

1. **Prometheus + Grafana**:
   - Collect and visualize system metrics
   - Set up alerts for threshold violations
   - Create dashboards for key metrics

2. **Netdata**:
   - Real-time system monitoring
   - Low overhead
   - Detailed system metrics

#### Application Monitoring

1. **Laravel Telescope**:
   - Monitor requests, queries, jobs, etc.
   - Debug issues in development
   - Not recommended for production use

2. **New Relic APM**:
   - Monitor application performance
   - Track transactions
   - Identify bottlenecks

3. **Sentry**:
   - Track errors and exceptions
   - Get detailed error reports
   - Set up alerts for new errors

#### Log Monitoring

1. **ELK Stack (Elasticsearch, Logstash, Kibana)**:
   - Centralize logs
   - Search and analyze logs
   - Create visualizations

2. **Papertrail**:
   - Cloud-based log management
   - Real-time log monitoring
   - Search and filter logs

#### Uptime Monitoring

1. **Uptime Robot**:
   - Monitor website uptime
   - Get alerts for downtime
   - Track response time

2. **Pingdom**:
   - Monitor website uptime
   - Track page load time
   - Get detailed reports

### Setting Up Monitoring

#### Prometheus + Grafana Setup

1. Install Prometheus:

```bash
# Download Prometheus
wget https://github.com/prometheus/prometheus/releases/download/v2.37.0/prometheus-2.37.0.linux-amd64.tar.gz

# Extract the archive
tar xvfz prometheus-2.37.0.linux-amd64.tar.gz

# Move to /opt
sudo mv prometheus-2.37.0.linux-amd64 /opt/prometheus

# Create a Prometheus user
sudo useradd --no-create-home --shell /bin/false prometheus

# Create directories
sudo mkdir -p /etc/prometheus /var/lib/prometheus

# Set ownership
sudo chown prometheus:prometheus /etc/prometheus /var/lib/prometheus

# Copy binaries
sudo cp /opt/prometheus/prometheus /usr/local/bin/
sudo cp /opt/prometheus/promtool /usr/local/bin/

# Set ownership
sudo chown prometheus:prometheus /usr/local/bin/prometheus
sudo chown prometheus:prometheus /usr/local/bin/promtool

# Copy configuration
sudo cp -r /opt/prometheus/consoles /etc/prometheus
sudo cp -r /opt/prometheus/console_libraries /etc/prometheus
sudo cp /opt/prometheus/prometheus.yml /etc/prometheus/

# Set ownership
sudo chown -R prometheus:prometheus /etc/prometheus/consoles
sudo chown -R prometheus:prometheus /etc/prometheus/console_libraries
sudo chown prometheus:prometheus /etc/prometheus/prometheus.yml
```

2. Configure Prometheus:

```bash
sudo nano /etc/prometheus/prometheus.yml
```

Add the following configuration:

```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'prometheus'
    scrape_interval: 5s
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'node_exporter'
    scrape_interval: 5s
    static_configs:
      - targets: ['localhost:9100']
```

3. Create a systemd service for Prometheus:

```bash
sudo nano /etc/systemd/system/prometheus.service
```

Add the following configuration:

```ini
[Unit]
Description=Prometheus
Wants=network-online.target
After=network-online.target

[Service]
User=prometheus
Group=prometheus
Type=simple
ExecStart=/usr/local/bin/prometheus \
    --config.file /etc/prometheus/prometheus.yml \
    --storage.tsdb.path /var/lib/prometheus/ \
    --web.console.templates=/etc/prometheus/consoles \
    --web.console.libraries=/etc/prometheus/console_libraries

[Install]
WantedBy=multi-user.target
```

4. Install Node Exporter:

```bash
# Download Node Exporter
wget https://github.com/prometheus/node_exporter/releases/download/v1.3.1/node_exporter-1.3.1.linux-amd64.tar.gz

# Extract the archive
tar xvfz node_exporter-1.3.1.linux-amd64.tar.gz

# Move the binary
sudo cp node_exporter-1.3.1.linux-amd64/node_exporter /usr/local/bin/

# Create a Node Exporter user
sudo useradd --no-create-home --shell /bin/false node_exporter

# Set ownership
sudo chown node_exporter:node_exporter /usr/local/bin/node_exporter
```

5. Create a systemd service for Node Exporter:

```bash
sudo nano /etc/systemd/system/node_exporter.service
```

Add the following configuration:

```ini
[Unit]
Description=Node Exporter
Wants=network-online.target
After=network-online.target

[Service]
User=node_exporter
Group=node_exporter
Type=simple
ExecStart=/usr/local/bin/node_exporter

[Install]
WantedBy=multi-user.target
```

6. Start the services:

```bash
sudo systemctl daemon-reload
sudo systemctl start prometheus
sudo systemctl enable prometheus
sudo systemctl start node_exporter
sudo systemctl enable node_exporter
```

7. Install Grafana:

```bash
# Add the GPG key
wget -q -O - https://packages.grafana.com/gpg.key | sudo apt-key add -

# Add the repository
sudo add-apt-repository "deb https://packages.grafana.com/oss/deb stable main"

# Update and install
sudo apt update
sudo apt install grafana

# Start and enable the service
sudo systemctl start grafana-server
sudo systemctl enable grafana-server
```

8. Configure Grafana:
   - Access Grafana at `http://your-server-ip:3000`
   - Default credentials: admin/admin
   - Add Prometheus as a data source
   - Import dashboards for Node Exporter

#### Laravel Telescope Setup

1. Install Laravel Telescope:

```bash
composer require laravel/telescope --dev
php artisan telescope:install
php artisan migrate
```

2. Configure Telescope in `config/telescope.php`:

```php
'enabled' => env('TELESCOPE_ENABLED', true),

'middleware' => [
    'web',
    \Laravel\Telescope\Http\Middleware\Authorize::class,
],

'storage' => [
    'database' => [
        'connection' => env('DB_CONNECTION', 'mysql'),
        'chunk' => 1000,
    ],
],
```

3. Access Telescope at `http://your-app-url/telescope`

#### Sentry Setup

1. Install Sentry:

```bash
composer require sentry/sentry-laravel
php artisan vendor:publish --provider="Sentry\Laravel\ServiceProvider"
```

2. Configure Sentry in `.env`:

```
SENTRY_LARAVEL_DSN=your-sentry-dsn
```

3. Test Sentry:

```php
throw new Exception('Test Sentry Exception');
```

## Maintenance Procedures

### Daily Maintenance

1. **Check System Health**:
   - Review monitoring dashboards
   - Check for alerts
   - Verify system resource usage

2. **Check Application Logs**:
   - Review error logs
   - Look for patterns or recurring issues
   - Address any critical errors

3. **Check Queue Status**:
   - Verify queue workers are running
   - Check for failed jobs
   - Retry or fix failed jobs

### Weekly Maintenance

1. **Database Maintenance**:
   - Check for slow queries
   - Optimize tables if needed
   - Review index usage

2. **Backup Verification**:
   - Verify backups are running
   - Test backup restoration
   - Check backup storage usage

3. **Security Checks**:
   - Review access logs for suspicious activity
   - Check for failed login attempts
   - Verify firewall rules

### Monthly Maintenance

1. **Update Dependencies**:
   - Check for security updates
   - Update packages with security fixes
   - Plan for major version upgrades

2. **Performance Review**:
   - Analyze performance trends
   - Identify bottlenecks
   - Plan for optimizations

3. **Capacity Planning**:
   - Review resource usage trends
   - Plan for scaling if needed
   - Adjust resources based on usage

### Quarterly Maintenance

1. **Security Audit**:
   - Conduct vulnerability scans
   - Review security policies
   - Update security measures

2. **Database Optimization**:
   - Review database schema
   - Optimize queries and indexes
   - Archive old data if needed

3. **Documentation Update**:
   - Update system documentation
   - Review and update procedures
   - Train team on new procedures

## Backup and Recovery

### Backup Strategy

1. **Database Backups**:
   - Daily full backups
   - Hourly incremental backups
   - Retain backups for 30 days

2. **File System Backups**:
   - Daily backups of user uploads
   - Weekly backups of application files
   - Retain backups for 30 days

3. **Configuration Backups**:
   - Backup server configurations
   - Backup environment variables
   - Store in a secure location

### Backup Implementation

1. **Database Backup Script**:

```bash
#!/bin/bash
TIMESTAMP=$(date +"%Y%m%d%H%M%S")
BACKUP_DIR="/var/backups/mysql"
MYSQL_USER="backup_user"
MYSQL_PASSWORD="your_secure_password"
DATABASE="lms_unified"

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Create backup
mysqldump -u$MYSQL_USER -p$MYSQL_PASSWORD $DATABASE | gzip > $BACKUP_DIR/$DATABASE-$TIMESTAMP.sql.gz

# Remove backups older than 30 days
find $BACKUP_DIR -type f -name "*.sql.gz" -mtime +30 -delete
```

2. **File System Backup Script**:

```bash
#!/bin/bash
TIMESTAMP=$(date +"%Y%m%d%H%M%S")
BACKUP_DIR="/var/backups/files"
APP_DIR="/var/www/naxofy"

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Backup uploads
tar -czf $BACKUP_DIR/uploads-$TIMESTAMP.tar.gz $APP_DIR/storage/app/public

# Remove backups older than 30 days
find $BACKUP_DIR -type f -name "*.tar.gz" -mtime +30 -delete
```

3. **Schedule Backups with Cron**:

```bash
# Database backups
0 * * * * /path/to/database_backup.sh

# File system backups
0 0 * * * /path/to/file_backup.sh
```

### Recovery Procedures

1. **Database Recovery**:

```bash
# Restore database
gunzip < /var/backups/mysql/lms_unified-20230101000000.sql.gz | mysql -u root -p lms_unified
```

2. **File System Recovery**:

```bash
# Restore uploads
tar -xzf /var/backups/files/uploads-20230101000000.tar.gz -C /
```

3. **Application Recovery**:

```bash
# Clone the repository
git clone https://github.com/your-org/naxofy-lms.git /var/www/naxofy

# Install dependencies
cd /var/www/naxofy
composer install --no-dev --optimize-autoloader

# Restore environment variables
cp /path/to/backup/.env /var/www/naxofy/.env

# Set permissions
sudo chown -R www-data:www-data /var/www/naxofy/storage /var/www/naxofy/bootstrap/cache

# Clear cache
php artisan optimize:clear

# Run migrations
php artisan migrate --force

# Restart services
sudo systemctl restart php8.1-fpm
sudo systemctl restart nginx
sudo supervisorctl restart all
```

## Incident Response

### Incident Response Plan

1. **Detection**:
   - Monitor alerts
   - Review logs
   - Respond to user reports

2. **Assessment**:
   - Determine the severity
   - Identify the scope
   - Estimate the impact

3. **Containment**:
   - Isolate affected systems
   - Block malicious traffic
   - Prevent further damage

4. **Eradication**:
   - Remove malicious code
   - Fix vulnerabilities
   - Apply security patches

5. **Recovery**:
   - Restore from backups
   - Verify system integrity
   - Resume normal operations

6. **Post-Incident Analysis**:
   - Document the incident
   - Identify root causes
   - Implement preventive measures

### Common Incidents and Responses

1. **High Server Load**:
   - Check for resource-intensive processes
   - Optimize database queries
   - Scale resources if needed

2. **Database Connection Issues**:
   - Check database service status
   - Verify connection credentials
   - Check for connection limits

3. **Application Errors**:
   - Check application logs
   - Fix code issues
   - Deploy hotfixes

4. **Security Breaches**:
   - Isolate affected systems
   - Reset compromised credentials
   - Apply security patches
