import React, { createContext, useState, useEffect } from 'react';
import { Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { authService } from '../services/api';
import { mockUsers } from '../services/mockData';

// Use mock data when true (for development without backend)
const USE_MOCK_DATA = true;

export const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Load token from storage on app start
    const loadToken = async () => {
      try {
        // Get the token from AsyncStorage
        const storedToken = await AsyncStorage.getItem('token');
        console.log('Loaded token from storage:', storedToken ? 'Found token' : 'No token')

        if (storedToken) {
          setToken(storedToken);
          fetchUserProfile(storedToken);
        } else {
          // For demo purposes, set a mock user if no token is found
          // This allows the app to work without a backend
          if (USE_MOCK_DATA) {
            console.log('Setting mock user for demo');
            setUser(mockUsers[0]);
          }
          setLoading(false);
        }
      } catch (error) {
        console.error('Error loading token:', error);
        setLoading(false);
      }
    };

    loadToken();
  }, []);

  const fetchUserProfile = async (authToken) => {
    try {
      setLoading(true);

      // Set the token in the request header
      authService.defaults.headers.common['Authorization'] = `Bearer ${authToken}`;

      const response = await authService.getProfile();
      setUser(response.data);
    } catch (error) {
      console.error('Error fetching user profile:', error);
      logout();
    } finally {
      setLoading(false);
    }
  };

  const login = async (email, password) => {
    try {
      console.log('Attempting to login with:', email);
      const response = await authService.login({ email, password });
      const { access_token, user } = response.data;

      // Store the token in AsyncStorage
      await AsyncStorage.setItem('token', access_token);
      console.log('Token stored in AsyncStorage:', access_token ? 'Token exists' : 'No token');

      setToken(access_token);
      setUser(user);

      console.log('Login successful, user:', user?.name || 'Unknown');
      console.log('Authentication state after login:', !!access_token);

      return { success: true };
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        message: error.response?.data?.error || 'Login failed. Please try again.'
      };
    }
  };

  const register = async (userData) => {
    try {
      const response = await authService.register(userData);
      return { success: true, data: response.data };
    } catch (error) {
      console.error('Registration error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Registration failed. Please try again.'
      };
    }
  };

  const logout = async () => {
    try {
      // Remove the token from AsyncStorage
      await AsyncStorage.removeItem('token');
      console.log('Token removed from AsyncStorage');

      setToken(null);
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        token,
        loading,
        login,
        register,
        logout,
        isAuthenticated: !!token || !!user, // Consider authenticated if either token or user exists
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
