import React, { useState, useEffect } from 'react';
import { StyleSheet, FlatList, View, TextInput, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

import Container from '@/components/ui/Container';
import Text from '@/components/ui/Text';
import Button from '@/components/ui/Button';
import CourseCard from '@/components/CourseCard';
import { courseService } from '@/services/api';
import { useTheme } from '@/components/ThemeProvider';

export default function ExploreScreen() {
  const params = useLocalSearchParams();
  const initialSearch = params.search?.toString() || '';
  const initialCategory = params.category?.toString() || '';

  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState(initialSearch);
  const [activeCategory, setActiveCategory] = useState(initialCategory);
  const { theme } = useTheme();

  const categories = [
    { id: 'all', name: 'All Courses' },
    { id: 'web', name: 'Web Development' },
    { id: 'data', name: 'Data Science' },
    { id: 'design', name: 'Design' },
    { id: 'marketing', name: 'Marketing' },
    { id: 'business', name: 'Business' },
    { id: 'it', name: 'IT & Software' },
  ];

  useEffect(() => {
    fetchCourses();
  }, [activeCategory]);

  useEffect(() => {
    if (initialSearch) {
      handleSearch();
    }
    if (initialCategory) {
      setActiveCategory(initialCategory);
    }
  }, [initialSearch, initialCategory]);

  const fetchCourses = async () => {
    try {
      setLoading(true);
      // Fetch courses, optionally filtered by category
      const response = await courseService.getAllCourses();
      let filteredCourses = response.data;

      // Filter by category if one is selected (except 'all')
      if (activeCategory && activeCategory !== 'all') {
        // This is a simple simulation of category filtering
        // In a real app, you would have proper category data and filtering
        filteredCourses = filteredCourses.filter(course => {
          const title = course.title.toLowerCase();
          const description = course.description.toLowerCase();
          const category = activeCategory.toLowerCase();

          return title.includes(category) || description.includes(category);
        });
      }

      setCourses(filteredCourses);
      setError(null);
    } catch (error) {
      console.error('Error fetching courses:', error);
      setError('Failed to load courses. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      return fetchCourses();
    }

    try {
      setLoading(true);
      // Search courses by query
      const response = await courseService.getAllCourses({ search: searchQuery });
      setCourses(response.data);
      setError(null);
    } catch (error) {
      console.error('Error searching courses:', error);
      setError('Failed to search courses. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCategoryPress = (categoryId) => {
    setActiveCategory(categoryId);
  };

  const renderCategoryItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.categoryButton,
        activeCategory === item.id && { backgroundColor: theme.primary },
      ]}
      onPress={() => handleCategoryPress(item.id)}
    >
      <Text
        style={[
          styles.categoryButtonText,
          activeCategory === item.id && { color: theme.textLight },
        ]}
      >
        {item.name}
      </Text>
    </TouchableOpacity>
  );

  const renderCourseItem = ({ item }) => (
    <CourseCard course={item} />
  );

  return (
    <Container style={styles.container}>
      <Text variant="h2" style={styles.title}>Explore Courses</Text>

      <View style={styles.searchContainer}>
        <TextInput
          style={[styles.searchInput, { backgroundColor: theme.backgroundSecondary, borderColor: theme.border }]}
          placeholder="Search for courses..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          onSubmitEditing={handleSearch}
        />
        <TouchableOpacity
          style={[styles.searchButton, { backgroundColor: theme.primary }]}
          onPress={handleSearch}
        >
          <Ionicons name="search" size={20} color="white" />
        </TouchableOpacity>
      </View>

      <FlatList
        data={categories}
        renderItem={renderCategoryItem}
        keyExtractor={(item) => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.categoriesList}
      />

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.primary} />
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <Button onPress={fetchCourses}>Try Again</Button>
        </View>
      ) : (
        <FlatList
          data={courses}
          renderItem={renderCourseItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.coursesList}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>
                {searchQuery ? `No courses found for "${searchQuery}"` : 'No courses available'}
              </Text>
              {searchQuery && (
                <Button
                  onPress={() => {
                    setSearchQuery('');
                    fetchCourses();
                  }}
                  style={styles.clearButton}
                >
                  Clear Search
                </Button>
              )}
            </View>
          }
        />
      )}
    </Container>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  title: {
    marginBottom: 20,
  },
  searchContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    alignItems: 'center',
  },
  searchInput: {
    flex: 1,
    height: 46,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
  },
  searchButton: {
    width: 46,
    height: 46,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  categoriesList: {
    paddingBottom: 16,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: '#f3f4f6',
  },
  categoryButtonText: {
    fontWeight: '500',
  },
  coursesList: {
    paddingBottom: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {
    marginBottom: 16,
    textAlign: 'center',
  },
  clearButton: {
    marginTop: 8,
  },
});
