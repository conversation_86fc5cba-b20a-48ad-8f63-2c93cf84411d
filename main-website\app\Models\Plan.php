<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Plan extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
        'price',
        'billing_cycle',
        'trial_days',
        'features',
        'is_active',
        'is_featured',
        'sort_order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price' => 'decimal:2',
        'features' => 'array',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'sort_order' => 'integer',
        'trial_days' => 'integer',
    ];

    /**
     * Get the tenants that are subscribed to this plan.
     */
    public function tenants()
    {
        return $this->hasMany(Tenant::class);
    }

    /**
     * Get the subscriptions for this plan.
     */
    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Check if the plan is free.
     */
    public function isFree()
    {
        return $this->price == 0;
    }

    /**
     * Get the formatted price.
     */
    public function getFormattedPriceAttribute(): string
    {
        if ($this->isFree()) {
            return 'Free';
        }

        return '$' . number_format($this->price, 2);
    }

    /**
     * Get the billing cycle text.
     */
    public function getBillingCycleTextAttribute(): string
    {
        if ($this->billing_cycle === 'monthly') {
            return 'per month';
        } elseif ($this->billing_cycle === 'yearly') {
            return 'per year';
        } else {
            return 'one-time';
        }
    }

    /**
     * Get the student limit.
     */
    public function getStudentLimitAttribute(): int
    {
        return $this->features['students'] ?? 0;
    }

    /**
     * Get the course limit.
     */
    public function getCourseLimitAttribute(): int
    {
        return $this->features['courses'] ?? 0;
    }

    /**
     * Get the storage limit.
     */
    public function getStorageLimitAttribute(): int
    {
        return $this->features['storage'] ?? 0;
    }

    /**
     * Get the bandwidth limit.
     */
    public function getBandwidthLimitAttribute(): int
    {
        return $this->features['bandwidth'] ?? 0;
    }
}
