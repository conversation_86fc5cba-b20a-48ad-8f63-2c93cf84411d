<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Lara<PERSON>\Socialite\Facades\Socialite;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;

class GoogleController extends Controller
{
    /**
     * Redirect the user to the Google authentication page.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function redirectToGoogle()
    {
        return Socialite::driver('google')->redirect();
    }

    /**
     * Obtain the user information from Google.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function handleGoogleCallback(Request $request)
    {
        try {
            $googleUser = Socialite::driver('google')->user();
            
            // Check if user exists
            $user = User::where('email', $googleUser->email)->first();
            
            if (!$user) {
                // Create new user
                $user = User::create([
                    'name' => $googleUser->name,
                    'email' => $googleUser->email,
                    'password' => Hash::make(uniqid()), // Random password
                    'role' => 'student',
                    'status' => 'active',
                    'profile_photo' => $googleUser->avatar,
                ]);
            }
            
            // Generate JWT token
            $token = JWTAuth::fromUser($user);
            
            // Determine redirect URL based on user role
            $redirectUrl = $this->getRedirectUrl($user, $token);
            
            return redirect($redirectUrl);
            
        } catch (\Exception $e) {
            return redirect('/login')->with('error', 'Google authentication failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Get the redirect URL based on user role.
     *
     * @param  \App\Models\User  $user
     * @param  string  $token
     * @return string
     */
    private function getRedirectUrl(User $user, string $token)
    {
        $baseUrl = config('app.frontend_url', 'http://localhost:3000');
        $queryParams = http_build_query([
            'token' => $token,
            'user' => json_encode([
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
            ]),
        ]);
        
        if ($user->role === 'admin') {
            return "/admin/dashboard?{$queryParams}";
        } elseif ($user->role === 'tenant') {
            return "/tenant/dashboard?{$queryParams}";
        } else {
            return "{$baseUrl}/auth/callback?{$queryParams}";
        }
    }
}
