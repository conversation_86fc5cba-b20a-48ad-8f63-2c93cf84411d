<?php

use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\CourseController;
use App\Http\Controllers\API\LessonController;
use App\Http\Controllers\API\PaymentController;
use App\Http\Controllers\API\TenantController;
use App\Http\Controllers\API\ThemeController;
use App\Http\Controllers\API\ModuleController;
use App\Http\Controllers\API\MarketplaceController;
use App\Http\Controllers\API\AppBuildController;
use App\Http\Controllers\API\AppConfigurationController;
use App\Http\Controllers\API\DeveloperAccountController;
use App\Http\Controllers\API\DeveloperAnalyticController;
use App\Http\Controllers\API\DeveloperSubmissionController;
use App\Http\Controllers\API\MarketplaceReviewController;
use App\Http\Controllers\API\PageBuilderController;
use App\Http\Controllers\API\RegisterController;
use App\Http\Controllers\Auth\GoogleController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Cookie;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// CSRF Token route
Route::get('/csrf-token', function (Request $request) {
    $token = csrf_token();
    return response()->json(['token' => $token]);
});

/*
|--------------------------------------------------------------------------
| Authentication Routes
|--------------------------------------------------------------------------
*/

Route::prefix('auth')->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/register', [RegisterController::class, 'register']);
    Route::post('/register-tenant', [AuthController::class, 'registerTenant']);
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::post('/refresh', [AuthController::class, 'refresh']);
    Route::get('/profile', [AuthController::class, 'userProfile']);

    // Google OAuth routes
    Route::get('/google', [GoogleController::class, 'redirectToGoogle']);
    Route::get('/google/callback', [GoogleController::class, 'handleGoogleCallback']);
});

/*
|--------------------------------------------------------------------------
| Public API Routes
|--------------------------------------------------------------------------
*/

// Course routes (public)
Route::get('/courses', [CourseController::class, 'index']);
Route::get('/courses/{id}', [CourseController::class, 'show']);

// Tenant routes (public)
Route::get('/tenants', [TenantController::class, 'index']);
Route::get('/tenants/{id}', [TenantController::class, 'show']);

// Marketplace routes (public)
Route::get('/marketplace/themes', [MarketplaceController::class, 'themes']);
Route::get('/marketplace/modules', [MarketplaceController::class, 'modules']);
Route::get('/marketplace/themes/{id}', [MarketplaceController::class, 'showTheme']);
Route::get('/marketplace/modules/{id}', [MarketplaceController::class, 'showModule']);

/*
|--------------------------------------------------------------------------
| Protected API Routes
|--------------------------------------------------------------------------
*/

Route::middleware('auth:api')->group(function () {
    // User profile
    Route::get('/user', function (Request $request) {
        return $request->user();
    });
    
    // Course management
    Route::post('/courses', [CourseController::class, 'store']);
    Route::put('/courses/{id}', [CourseController::class, 'update']);
    Route::delete('/courses/{id}', [CourseController::class, 'destroy']);
    Route::post('/courses/{id}/enroll', [CourseController::class, 'enroll']);
    Route::post('/courses/{id}/progress', [CourseController::class, 'updateProgress']);
    
    // Lesson management
    Route::apiResource('lessons', LessonController::class);
    
    // Tenant management
    Route::post('/tenants', [TenantController::class, 'store']);
    Route::put('/tenants/{id}', [TenantController::class, 'update']);
    Route::delete('/tenants/{id}', [TenantController::class, 'destroy']);
    Route::post('/tenants/{id}/approve', [TenantController::class, 'approve']);
    
    // Payment routes
    Route::post('/payments/create-order', [PaymentController::class, 'createOrder']);
    Route::post('/payments/verify', [PaymentController::class, 'verifyPayment']);
    Route::get('/payments/history', [PaymentController::class, 'history']);
    
    // Theme and module management
    Route::apiResource('themes', ThemeController::class);
    Route::apiResource('modules', ModuleController::class);
    Route::post('/modules/{id}/install', [ModuleController::class, 'install']);
    Route::post('/modules/{id}/uninstall', [ModuleController::class, 'uninstall']);
    Route::post('/modules/{id}/enable', [ModuleController::class, 'enable']);
    Route::post('/modules/{id}/disable', [ModuleController::class, 'disable']);
    
    // App build and configuration
    Route::apiResource('app-builds', AppBuildController::class);
    Route::apiResource('app-configurations', AppConfigurationController::class);
    Route::post('/app-builds/{id}/generate', [AppBuildController::class, 'generate']);
    
    // Developer routes
    Route::apiResource('developer-accounts', DeveloperAccountController::class);
    Route::apiResource('developer-submissions', DeveloperSubmissionController::class);
    Route::apiResource('developer-analytics', DeveloperAnalyticController::class);
    
    // Marketplace reviews
    Route::apiResource('marketplace-reviews', MarketplaceReviewController::class);
    
    // Page builder
    Route::apiResource('pages', PageBuilderController::class);
});

// Tenant-specific API routes
Route::domain('{domain}.'.config('app.tenant_domain', 'localhost'))->middleware('auth:api')->group(function () {
    Route::get('/tenant-info', [TenantController::class, 'info']);
    Route::get('/tenant-courses', [CourseController::class, 'tenantCourses']);
    Route::get('/tenant-students', [TenantController::class, 'students']);
});
