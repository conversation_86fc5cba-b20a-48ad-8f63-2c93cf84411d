<?php

namespace Database\Seeders;

use App\Models\MarketplaceReview;
use App\Models\Theme;
use App\Models\Module;
use App\Models\User;
use Illuminate\Database\Seeder;

class MarketplaceReviewSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some users to create reviews
        $users = User::take(5)->get();
        if ($users->isEmpty()) {
            $this->command->info('No users found. Skipping review seeding.');
            return;
        }

        // Get themes and modules
        $themes = Theme::all();
        $modules = Module::all();

        // Sample review comments
        $comments = [
            'Great theme! Very easy to customize and looks professional.',
            'Excellent module. Works perfectly with my courses.',
            'Good design and functionality. Would recommend.',
            'Exactly what I needed for my online courses.',
            'Very intuitive and easy to use. Students love it!',
            'Solid performance and great design.',
            'Works as expected. Good value for money.',
            'Highly recommended for any educational platform.',
            'Easy to install and configure. No issues so far.',
            'Perfect for my needs. Very satisfied with this purchase.'
        ];

        // Create reviews for themes
        foreach ($themes as $theme) {
            // Generate 1-3 reviews per theme
            $reviewCount = rand(1, 3);
            
            for ($i = 0; $i < $reviewCount; $i++) {
                // Skip if we've already used all users
                if ($i >= count($users)) break;
                
                MarketplaceReview::create([
                    'user_id' => $users[$i]->id,
                    'reviewable_type' => Theme::class,
                    'reviewable_id' => $theme->id,
                    'rating' => rand(3, 5), // Mostly positive ratings (3-5 stars)
                    'comment' => $comments[array_rand($comments)],
                    'is_approved' => true,
                    'created_at' => now()->subDays(rand(1, 30)), // Random date in the last month
                ]);
            }
        }

        // Create reviews for modules
        foreach ($modules as $module) {
            // Generate 1-3 reviews per module
            $reviewCount = rand(1, 3);
            
            for ($i = 0; $i < $reviewCount; $i++) {
                // Skip if we've already used all users
                if ($i >= count($users)) break;
                
                MarketplaceReview::create([
                    'user_id' => $users[$i]->id,
                    'reviewable_type' => Module::class,
                    'reviewable_id' => $module->id,
                    'rating' => rand(3, 5), // Mostly positive ratings (3-5 stars)
                    'comment' => $comments[array_rand($comments)],
                    'is_approved' => true,
                    'created_at' => now()->subDays(rand(1, 30)), // Random date in the last month
                ]);
            }
        }

        $this->command->info('Marketplace reviews seeded successfully.');
    }
}
