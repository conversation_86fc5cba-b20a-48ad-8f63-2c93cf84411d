import React, { useState, useEffect, useContext } from 'react';
import { StyleSheet, FlatList, ActivityIndicator, TouchableOpacity, Alert } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

import Container from '@/components/ui/Container';
import Text from '@/components/ui/Text';
import Card from '@/components/ui/Card';
import CourseCard from '@/components/CourseCard';
import EmptyState from '@/components/EmptyState';
import { AuthContext } from '@/context/AuthContext';
import { courseService } from '@/services/api';
import { useTheme } from '@/components/ThemeProvider';

export default function MyCoursesScreen() {
  const { isAuthenticated, user } = useContext(AuthContext);
  const { theme } = useTheme();
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!isAuthenticated) {
      router.replace('/login');
      return;
    }
    
    fetchEnrolledCourses();
  }, [isAuthenticated]);

  const fetchEnrolledCourses = async () => {
    try {
      setLoading(true);
      const response = await courseService.getEnrolledCourses();
      setCourses(response.data);
      setError(null);
    } catch (error) {
      console.error('Error fetching enrolled courses:', error);
      setError('Failed to load your courses. Please try again.');
      
      // Use mock data as fallback
      setCourses([
        {
          id: 1,
          title: "Advanced Web Development",
          description: "Learn modern web development techniques with React, Node.js, and MongoDB.",
          thumbnail: "https://source.unsplash.com/random?web",
          progress: 35,
          last_accessed: "2023-06-15T10:30:00Z"
        },
        {
          id: 2,
          title: "Mobile App Development with React Native",
          description: "Build cross-platform mobile apps using React Native and Expo.",
          thumbnail: "https://source.unsplash.com/random?mobile",
          progress: 68,
          last_accessed: "2023-06-14T15:45:00Z"
        },
        {
          id: 3,
          title: "Data Science Fundamentals",
          description: "Introduction to data science, statistics, and machine learning.",
          thumbnail: "https://source.unsplash.com/random?data",
          progress: 12,
          last_accessed: "2023-06-10T09:15:00Z"
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleCoursePress = (courseId) => {
    router.push(`/course/${courseId}/learn`);
  };

  if (!isAuthenticated) {
    return null; // Will redirect in useEffect
  }

  return (
    <Container style={styles.container}>
      <TouchableOpacity 
        style={styles.backButton}
        onPress={() => router.back()}
      >
        <Ionicons name="arrow-back" size={24} color={theme.text} />
        <Text style={styles.backButtonText}>Back</Text>
      </TouchableOpacity>
      
      <Text variant="h2" style={styles.title}>My Courses</Text>
      
      {loading ? (
        <ActivityIndicator size="large" color={theme.primary} style={styles.loader} />
      ) : error ? (
        <Card style={styles.errorCard}>
          <Text color="error" style={styles.errorText}>{error}</Text>
          <TouchableOpacity 
            style={[styles.retryButton, { backgroundColor: theme.primary }]}
            onPress={fetchEnrolledCourses}
          >
            <Text color="light">Try Again</Text>
          </TouchableOpacity>
        </Card>
      ) : courses.length === 0 ? (
        <EmptyState
          icon="book-outline"
          title="No courses yet"
          message="You haven't enrolled in any courses yet. Browse our catalog to find courses that interest you."
          actionLabel="Browse Courses"
          onAction={() => router.push('/')}
        />
      ) : (
        <FlatList
          data={courses}
          keyExtractor={(item) => item.id.toString()}
          renderItem={({ item }) => (
            <CourseCard
              course={item}
              onPress={() => handleCoursePress(item.id)}
              showProgress
            />
          )}
          contentContainerStyle={styles.coursesList}
          showsVerticalScrollIndicator={false}
        />
      )}
    </Container>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  backButtonText: {
    marginLeft: 8,
  },
  title: {
    marginBottom: 24,
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorCard: {
    padding: 16,
    alignItems: 'center',
  },
  errorText: {
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  coursesList: {
    paddingBottom: 24,
  },
});
