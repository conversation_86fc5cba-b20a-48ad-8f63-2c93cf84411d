<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ThemeCustomization extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'theme_id',
        'theme_template_id',
        'name',
        'slug',
        'type',
        'description',
        'content',
        'variables',
        'settings',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'variables' => 'array',
        'settings' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the tenant that owns the customization.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the theme that owns the customization.
     */
    public function theme(): BelongsTo
    {
        return $this->belongsTo(Theme::class);
    }

    /**
     * Get the template that the customization is based on.
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(ThemeTemplate::class, 'theme_template_id');
    }

    /**
     * Scope a query to only include customizations of a specific type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query to only include active customizations.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Reset the customization to the original template.
     */
    public function resetToTemplate(): bool
    {
        if (!$this->theme_template_id) {
            return false;
        }

        $template = ThemeTemplate::find($this->theme_template_id);
        if (!$template) {
            return false;
        }

        $this->content = $template->content;
        $this->variables = $template->variables;
        $this->settings = $template->settings;
        $this->save();

        return true;
    }

    /**
     * Render the customization with variable substitution.
     */
    public function render(array $additionalVariables = []): string
    {
        $content = $this->content;
        $variables = array_merge($this->variables ?? [], $additionalVariables);

        // Simple variable substitution
        foreach ($variables as $key => $value) {
            if (is_string($value)) {
                $content = str_replace('{{' . $key . '}}', $value, $content);
            }
        }

        return $content;
    }
}
