# Testing Frontend and Mobile Integration

This guide provides instructions for testing the integration between the unified backend, React frontend, and React Native mobile app.

## Prerequisites

1. The unified backend is running on `http://localhost:8000`
2. The database has been migrated and seeded with test data
3. The frontend and mobile app have been updated with the new API endpoints

## Testing the Frontend Integration

### 1. Start the Frontend

```bash
cd frontend
npm run dev
```

The frontend should be accessible at `http://localhost:3000`.

### 2. Test Authentication

1. **Registration**:
   - Navigate to the registration page
   - Fill in the registration form with valid data
   - Submit the form
   - Verify that you are redirected to the login page or dashboard

2. **Login**:
   - Navigate to the login page
   - Enter valid credentials
   - Submit the form
   - Verify that you are redirected to the dashboard
   - Verify that the JWT token is stored in localStorage

3. **Profile**:
   - Navigate to the profile page
   - Verify that your user information is displayed correctly

4. **Logout**:
   - Click the logout button
   - Verify that you are redirected to the login page
   - Verify that the JWT token is removed from localStorage

### 3. Test Course Management

1. **Course Listing**:
   - Navigate to the courses page
   - Verify that courses are displayed correctly
   - Test the search and filter functionality

2. **Course Details**:
   - Click on a course to view its details
   - Verify that the course information is displayed correctly
   - Verify that the lessons are displayed correctly

3. **Enrollment**:
   - Enroll in a course
   - Verify that you are redirected to the course page
   - Verify that you can access the course content

### 4. Test Tenant Features (for tenant users)

1. **Theme Management**:
   - Navigate to the themes page
   - Verify that themes are displayed correctly
   - Test installing a theme
   - Test activating a theme

2. **Module Management**:
   - Navigate to the modules page
   - Verify that modules are displayed correctly
   - Test installing a module
   - Test enabling and disabling a module

3. **Marketplace**:
   - Navigate to the marketplace
   - Verify that themes and modules are displayed correctly
   - Test searching and filtering
   - Test viewing theme and module details

## Testing the Mobile App Integration

### 1. Start the Mobile App

```bash
cd mobile
npx expo start
```

### 2. Configure the API URL

Make sure the API URL in `mobile/services/api.js` is set correctly:

- For Android emulator: `********:8000`
- For iOS simulator: `localhost:8000`
- For physical device: Your computer's IP address (e.g., `*************:8000`)

### 3. Test Authentication

1. **Registration**:
   - Navigate to the registration screen
   - Fill in the registration form with valid data
   - Submit the form
   - Verify that you are redirected to the login screen or dashboard

2. **Login**:
   - Navigate to the login screen
   - Enter valid credentials
   - Submit the form
   - Verify that you are redirected to the dashboard
   - Verify that the JWT token is stored in AsyncStorage

3. **Profile**:
   - Navigate to the profile screen
   - Verify that your user information is displayed correctly

4. **Logout**:
   - Tap the logout button
   - Verify that you are redirected to the login screen
   - Verify that the JWT token is removed from AsyncStorage

### 4. Test Course Management

1. **Course Listing**:
   - Navigate to the courses screen
   - Verify that courses are displayed correctly
   - Test the search functionality

2. **Course Details**:
   - Tap on a course to view its details
   - Verify that the course information is displayed correctly
   - Verify that the lessons are displayed correctly

3. **Enrollment**:
   - Enroll in a course
   - Verify that you can access the course content

## Troubleshooting

### CORS Issues

If you encounter CORS issues, check the following:

1. The backend has the proper CORS configuration:
   ```php
   // config/cors.php
   return [
       'paths' => ['api/*', 'sanctum/csrf-cookie'],
       'allowed_methods' => ['*'],
       'allowed_origins' => ['*'], // In production, specify your frontend and mobile app origins
       'allowed_origins_patterns' => [],
       'allowed_headers' => ['*'],
       'exposed_headers' => [],
       'max_age' => 0,
       'supports_credentials' => true,
   ];
   ```

2. The frontend is making requests with the correct headers:
   ```javascript
   headers: {
     'Content-Type': 'application/json',
     'Accept': 'application/json',
     'X-Requested-With': 'XMLHttpRequest',
   }
   ```

### Authentication Issues

If you encounter authentication issues, check the following:

1. The JWT token is being properly stored and sent with requests
2. The token refresh mechanism is working correctly
3. The backend logs for any authentication errors

### API Endpoint Issues

If you encounter issues with API endpoints, check the following:

1. The endpoint paths are correct
2. The request parameters are correct
3. The backend logs for any errors
4. Use browser developer tools or Postman to test the API endpoints directly

## Automated Testing

### Frontend Tests

```bash
cd frontend
npm test
```

### Mobile App Tests

```bash
cd mobile
npm test
```

## Continuous Integration

The integration tests are automatically run on every pull request and push to the main branch. You can view the test results in the GitHub Actions tab of the repository.
