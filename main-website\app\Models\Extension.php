<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Extension extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'thumbnail',
        'author',
        'version',
        'status',
        'is_premium',
        'price',
        'settings',
        'tenant_id',
        'required_plan_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'settings' => 'array',
        'is_premium' => 'boolean',
        'price' => 'float',
    ];

    /**
     * Get the tenant that created the extension.
     */
    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the plan that is required for this extension.
     */
    public function requiredPlan()
    {
        return $this->belongsTo(Plan::class, 'required_plan_id');
    }

    /**
     * Get the tenants that have installed this extension.
     */
    public function installedBy()
    {
        return $this->belongsToMany(Tenant::class, 'extension_tenant')
            ->withPivot('status', 'settings')
            ->withTimestamps();
    }

    /**
     * Check if the extension is active.
     */
    public function isActive()
    {
        return $this->status === 'active';
    }

    /**
     * Check if the extension is pending.
     */
    public function isPending()
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the extension is rejected.
     */
    public function isRejected()
    {
        return $this->status === 'rejected';
    }

    /**
     * Check if the extension is free.
     */
    public function isFree()
    {
        return !$this->is_premium || $this->price == 0;
    }

    /**
     * Get the formatted price.
     */
    public function getFormattedPriceAttribute()
    {
        if ($this->isFree()) {
            return 'Free';
        }

        return '$' . number_format($this->price, 2);
    }
}
