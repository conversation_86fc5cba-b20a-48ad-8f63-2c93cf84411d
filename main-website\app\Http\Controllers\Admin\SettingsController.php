<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class SettingsController extends Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        // Check if user is admin for all methods
        $this->middleware(function ($request, $next) {
            if (!Auth::check() || !Auth::user()->isAdmin()) {
                return redirect()->route('login')->with('error', 'You do not have permission to access this area.');
            }
            return $next($request);
        });
    }

    /**
     * Display the settings page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get settings from config or database
        $settings = $this->getSettings();

        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Update the general settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateGeneral(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'site_name' => 'required|string|max:255',
            'site_description' => 'required|string',
            'site_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'site_favicon' => 'nullable|image|mimes:ico,png|max:1024',
            'primary_color' => 'required|string|max:7',
            'secondary_color' => 'required|string|max:7',
            'tenant_domain' => 'required|string|max:255',
            'contact_email' => 'required|email',
            'contact_phone' => 'nullable|string|max:20',
            'contact_address' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Handle logo upload
            if ($request->hasFile('site_logo')) {
                $logoPath = $request->file('site_logo')->store('settings', 'public');
                $this->updateSetting('site_logo', $logoPath);
            }

            // Handle favicon upload
            if ($request->hasFile('site_favicon')) {
                $faviconPath = $request->file('site_favicon')->store('settings', 'public');
                $this->updateSetting('site_favicon', $faviconPath);
            }

            // Update other settings
            $this->updateSetting('site_name', $request->site_name);
            $this->updateSetting('site_description', $request->site_description);
            $this->updateSetting('primary_color', $request->primary_color);
            $this->updateSetting('secondary_color', $request->secondary_color);
            $this->updateSetting('tenant_domain', $request->tenant_domain);
            $this->updateSetting('contact_email', $request->contact_email);
            $this->updateSetting('contact_phone', $request->contact_phone);
            $this->updateSetting('contact_address', $request->contact_address);

            return redirect()->route('admin.settings.index')
                ->with('success', 'General settings updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update general settings: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Update the email settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'mail_driver' => 'required|string|in:smtp,sendmail,mailgun,ses,postmark,log',
            'mail_host' => 'required_if:mail_driver,smtp|nullable|string',
            'mail_port' => 'required_if:mail_driver,smtp|nullable|integer',
            'mail_username' => 'required_if:mail_driver,smtp|nullable|string',
            'mail_password' => 'required_if:mail_driver,smtp|nullable|string',
            'mail_encryption' => 'nullable|string|in:tls,ssl',
            'mail_from_address' => 'required|email',
            'mail_from_name' => 'required|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Update email settings
            $this->updateSetting('mail_driver', $request->mail_driver);
            $this->updateSetting('mail_host', $request->mail_host);
            $this->updateSetting('mail_port', $request->mail_port);
            $this->updateSetting('mail_username', $request->mail_username);
            $this->updateSetting('mail_password', $request->mail_password);
            $this->updateSetting('mail_encryption', $request->mail_encryption);
            $this->updateSetting('mail_from_address', $request->mail_from_address);
            $this->updateSetting('mail_from_name', $request->mail_from_name);

            return redirect()->route('admin.settings.index')
                ->with('success', 'Email settings updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update email settings: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Update the payment settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updatePayment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'payment_currency' => 'required|string|size:3',
            'stripe_enabled' => 'boolean',
            'stripe_key' => 'required_if:stripe_enabled,1|nullable|string',
            'stripe_secret' => 'required_if:stripe_enabled,1|nullable|string',
            'paypal_enabled' => 'boolean',
            'paypal_client_id' => 'required_if:paypal_enabled,1|nullable|string',
            'paypal_secret' => 'required_if:paypal_enabled,1|nullable|string',
            'paypal_mode' => 'required_if:paypal_enabled,1|nullable|in:sandbox,live',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Update payment settings
            $this->updateSetting('payment_currency', $request->payment_currency);
            $this->updateSetting('stripe_enabled', $request->has('stripe_enabled'));
            $this->updateSetting('stripe_key', $request->stripe_key);
            $this->updateSetting('stripe_secret', $request->stripe_secret);
            $this->updateSetting('paypal_enabled', $request->has('paypal_enabled'));
            $this->updateSetting('paypal_client_id', $request->paypal_client_id);
            $this->updateSetting('paypal_secret', $request->paypal_secret);
            $this->updateSetting('paypal_mode', $request->paypal_mode);

            return redirect()->route('admin.settings.index')
                ->with('success', 'Payment settings updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update payment settings: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Update the social login settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateSocialLogin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'google_enabled' => 'boolean',
            'google_client_id' => 'required_if:google_enabled,1|nullable|string',
            'google_client_secret' => 'required_if:google_enabled,1|nullable|string',
            'facebook_enabled' => 'boolean',
            'facebook_client_id' => 'required_if:facebook_enabled,1|nullable|string',
            'facebook_client_secret' => 'required_if:facebook_enabled,1|nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Update social login settings
            $this->updateSetting('google_enabled', $request->has('google_enabled'));
            $this->updateSetting('google_client_id', $request->google_client_id);
            $this->updateSetting('google_client_secret', $request->google_client_secret);
            $this->updateSetting('facebook_enabled', $request->has('facebook_enabled'));
            $this->updateSetting('facebook_client_id', $request->facebook_client_id);
            $this->updateSetting('facebook_client_secret', $request->facebook_client_secret);

            return redirect()->route('admin.settings.index')
                ->with('success', 'Social login settings updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update social login settings: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Update the recaptcha settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateRecaptcha(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'recaptcha_enabled' => 'boolean',
            'recaptcha_site_key' => 'required_if:recaptcha_enabled,1|nullable|string',
            'recaptcha_secret_key' => 'required_if:recaptcha_enabled,1|nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Update recaptcha settings
            $this->updateSetting('recaptcha_enabled', $request->has('recaptcha_enabled'));
            $this->updateSetting('recaptcha_site_key', $request->recaptcha_site_key);
            $this->updateSetting('recaptcha_secret_key', $request->recaptcha_secret_key);

            return redirect()->route('admin.settings.index')
                ->with('success', 'reCAPTCHA settings updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update reCAPTCHA settings: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Get all settings.
     *
     * @return array
     */
    private function getSettings()
    {
        // In a real implementation, this would fetch settings from the database
        return [
            'general' => [
                'site_name' => 'Naxofy',
                'site_description' => 'A multi-tenant LMS platform',
                'site_logo' => 'settings/logo.png',
                'site_favicon' => 'settings/favicon.ico',
                'primary_color' => '#ff7700',
                'secondary_color' => '#0369a1',
                'tenant_domain' => 'naxofy.com',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+****************',
                'contact_address' => '123 Main St, City, Country',
            ],
            'email' => [
                'mail_driver' => 'smtp',
                'mail_host' => 'smtp.mailtrap.io',
                'mail_port' => 2525,
                'mail_username' => 'username',
                'mail_password' => 'password',
                'mail_encryption' => 'tls',
                'mail_from_address' => '<EMAIL>',
                'mail_from_name' => 'Naxofy',
            ],
            'payment' => [
                'payment_currency' => 'USD',
                'stripe_enabled' => true,
                'stripe_key' => 'pk_test_123456789',
                'stripe_secret' => 'sk_test_123456789',
                'paypal_enabled' => true,
                'paypal_client_id' => 'client_id',
                'paypal_secret' => 'secret',
                'paypal_mode' => 'sandbox',
            ],
            'social' => [
                'google_enabled' => true,
                'google_client_id' => 'client_id',
                'google_client_secret' => 'client_secret',
                'facebook_enabled' => false,
                'facebook_client_id' => '',
                'facebook_client_secret' => '',
            ],
            'recaptcha' => [
                'recaptcha_enabled' => false,
                'recaptcha_site_key' => '',
                'recaptcha_secret_key' => '',
            ],
        ];
    }

    /**
     * Update a setting.
     *
     * @param  string  $key
     * @param  mixed  $value
     * @return void
     */
    private function updateSetting($key, $value)
    {
        // In a real implementation, this would update the setting in the database
        // For now, we'll just log it
        \Log::info("Setting updated: {$key} = {$value}");
    }
}
