<?php

namespace App\Services;

use App\Models\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class DomainService
{
    /**
     * Get the tenant from the request domain.
     *
     * @param Request $request
     * @return Tenant|null
     */
    public function getTenantFromRequest(Request $request): ?Tenant
    {
        $host = $request->getHost();
        
        // Check if this is a custom domain
        $tenant = $this->getTenantByCustomDomain($host);
        if ($tenant) {
            return $tenant;
        }
        
        // Check if this is a subdomain
        $tenant = $this->getTenantBySubdomain($host);
        if ($tenant) {
            return $tenant;
        }
        
        return null;
    }
    
    /**
     * Get a tenant by custom domain.
     *
     * @param string $domain
     * @return Tenant|null
     */
    public function getTenantByCustomDomain(string $domain): ?Tenant
    {
        // Cache the result to improve performance
        return Cache::remember("tenant_custom_domain_{$domain}", 3600, function () use ($domain) {
            return Tenant::where('custom_domain', $domain)
                ->where('custom_domain_verified', true)
                ->where('status', 'active')
                ->first();
        });
    }
    
    /**
     * Get a tenant by subdomain.
     *
     * @param string $host
     * @return Tenant|null
     */
    public function getTenantBySubdomain(string $host): ?Tenant
    {
        $tenantDomain = config('app.tenant_domain', 'naxofy.com');
        
        // If the host doesn't contain the tenant domain, it's not a subdomain
        if (!str_contains($host, $tenantDomain)) {
            return null;
        }
        
        // Extract the subdomain part
        $subdomain = str_replace('.' . $tenantDomain, '', $host);
        
        // Cache the result to improve performance
        return Cache::remember("tenant_subdomain_{$subdomain}", 3600, function () use ($subdomain) {
            return Tenant::where('domain', $subdomain)
                ->where('status', 'active')
                ->first();
        });
    }
    
    /**
     * Check if the current request is for the main website.
     *
     * @param Request $request
     * @return bool
     */
    public function isMainWebsite(Request $request): bool
    {
        $host = $request->getHost();
        $mainDomain = config('app.domain', 'naxofy.com');
        
        return $host === $mainDomain || $host === 'www.' . $mainDomain || $host === 'localhost';
    }
    
    /**
     * Get the tenant's frontend URL.
     *
     * @param Tenant $tenant
     * @return string
     */
    public function getTenantFrontendUrl(Tenant $tenant): string
    {
        return $tenant->frontend_url;
    }
    
    /**
     * Get the tenant's dashboard URL.
     *
     * @param Tenant $tenant
     * @return string
     */
    public function getTenantDashboardUrl(Tenant $tenant): string
    {
        return $tenant->dashboard_url;
    }
    
    /**
     * Get the tenant's API URL.
     *
     * @param Tenant $tenant
     * @return string
     */
    public function getTenantApiUrl(Tenant $tenant): string
    {
        return $tenant->api_url;
    }
}
