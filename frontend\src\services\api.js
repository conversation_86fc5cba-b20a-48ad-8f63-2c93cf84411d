import axios from 'axios';
import { mockCourses, mockUsers, mockTenants, mockLessons } from '../mockData';

// Flag to use mock data when backend is not available
const USE_MOCK_DATA = true;

const API_URL = 'http://localhost:8000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
  },
  withCredentials: true, // Set to true if using cookies for authentication
});

// Add request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }

    // Add CSRF token for Laravel
    config.headers['X-Requested-With'] = 'XMLHttpRequest';
    config.headers['Accept'] = 'application/json';

    // Get XSRF token from cookie if available
    const xsrfToken = document.cookie
      .split('; ')
      .find(row => row.startsWith('XSRF-TOKEN='));

    if (xsrfToken) {
      const token = xsrfToken.split('=')[1];
      config.headers['X-XSRF-TOKEN'] = decodeURIComponent(token);
    }

    config.withCredentials = true;

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle token expiration
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // If the error is 401 and not already retrying
    if (error.response.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh the token
        const refreshResponse = await axios.post(`${API_URL}/auth/refresh`, {}, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        const { access_token } = refreshResponse.data;

        // Save the new token
        localStorage.setItem('token', access_token);

        // Update the authorization header
        originalRequest.headers['Authorization'] = `Bearer ${access_token}`;

        // Retry the original request
        return api(originalRequest);
      } catch (refreshError) {
        // If refresh fails, logout the user
        localStorage.removeItem('token');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// Auth services
export const authService = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  registerTenant: (tenantData) => api.post('/auth/register-tenant', tenantData),
  logout: () => api.post('/auth/logout'),
  getProfile: () => api.get('/auth/profile'),
};

// User services
export const userService = {
  getAllUsers: async () => {
    if (USE_MOCK_DATA) {
      return { data: mockUsers };
    }
    return api.get('/users');
  },
  getUserById: async (id) => {
    if (USE_MOCK_DATA) {
      const user = mockUsers.find(u => u.id.toString() === id.toString());
      return { data: user };
    }
    return api.get(`/users/${id}`);
  },
  createUser: (userData) => api.post('/users', userData),
  updateUser: (id, userData) => api.put(`/users/${id}`, userData),
  deleteUser: (id) => api.delete(`/users/${id}`),
  banUser: (id) => api.post(`/users/${id}/ban`),
  unbanUser: (id) => api.post(`/users/${id}/unban`),
};

// Course services
export const courseService = {
  getAllCourses: async (params) => {
    if (USE_MOCK_DATA) {
      // Filter mock data based on search params if provided
      let filteredCourses = [...mockCourses];
      if (params && params.search) {
        const searchTerm = params.search.toLowerCase();
        filteredCourses = filteredCourses.filter(course =>
          course.title.toLowerCase().includes(searchTerm) ||
          course.description.toLowerCase().includes(searchTerm)
        );
      }
      return { data: filteredCourses };
    }
    return api.get('/courses', { params });
  },
  getCourseById: async (id) => {
    if (USE_MOCK_DATA) {
      const course = mockCourses.find(c => c.id.toString() === id.toString());
      return { data: { course, is_enrolled: false, enrollment: null } };
    }
    return api.get(`/courses/${id}`);
  },
  createCourse: (courseData) => api.post('/courses', courseData),
  updateCourse: (id, courseData) => api.put(`/courses/${id}`, courseData),
  deleteCourse: (id) => api.delete(`/courses/${id}`),
  enrollInCourse: (id) => api.post(`/courses/${id}/enroll`),
  updateProgress: (id, progress) => api.post(`/courses/${id}/progress`, { progress }),
};

// Lesson services
export const lessonService = {
  getLessonsByCourse: (courseId) => api.get('/lessons', { params: { course_id: courseId } }),
  getLessonById: (id) => api.get(`/lessons/${id}`),
  createLesson: (lessonData) => api.post('/lessons', lessonData),
  updateLesson: (id, lessonData) => api.put(`/lessons/${id}`, lessonData),
  deleteLesson: (id) => api.delete(`/lessons/${id}`),
};

// Tenant services
export const tenantService = {
  getAllTenants: () => api.get('/tenants'),
  getTenantById: (id) => api.get(`/tenants/${id}`),
  updateTenant: (id, tenantData) => api.put(`/tenants/${id}`, tenantData),
  deleteTenant: (id) => api.delete(`/tenants/${id}`),
  approveTenant: (id) => api.post(`/tenants/${id}/approve`),
};

// Payment services
export const paymentService = {
  createOrder: (courseId) => api.post('/payments/create-order', { course_id: courseId }),
  verifyPayment: (paymentData) => api.post('/payments/verify', paymentData),
  getPaymentHistory: () => api.get('/payments/history'),
};

// Theme services
export const themeService = {
  getAllThemes: (params) => api.get('/themes', { params }),
  getThemeById: (id) => api.get(`/themes/${id}`),
  createTheme: (themeData) => api.post('/themes', themeData),
  updateTheme: (id, themeData) => api.put(`/themes/${id}`, themeData),
  deleteTheme: (id) => api.delete(`/themes/${id}`),
  activateTheme: (id) => api.post(`/themes/${id}/activate`),
  installTheme: (id) => api.post(`/themes/${id}/install`),
};

// Module services
export const moduleService = {
  getAllModules: (params) => api.get('/modules', { params }),
  getModuleById: (id) => api.get(`/modules/${id}`),
  createModule: (moduleData) => api.post('/modules', moduleData),
  updateModule: (id, moduleData) => api.put(`/modules/${id}`, moduleData),
  deleteModule: (id) => api.delete(`/modules/${id}`),
  installModule: (id) => api.post(`/modules/${id}/install`),
  uninstallModule: (id) => api.post(`/modules/${id}/uninstall`),
  enableModule: (id) => api.post(`/modules/${id}/enable`),
  disableModule: (id) => api.post(`/modules/${id}/disable`),
};

// Marketplace services
export const marketplaceService = {
  getFeatured: () => api.get('/marketplace/featured'),
  getThemes: (params) => api.get('/marketplace/themes', { params }),
  getModules: (params) => api.get('/marketplace/modules', { params }),
  getThemeById: (id) => api.get(`/marketplace/themes/${id}`),
  getModuleById: (id) => api.get(`/marketplace/modules/${id}`),
  getInstalledThemes: () => api.get('/marketplace/installed/themes'),
  getInstalledModules: () => api.get('/marketplace/installed/modules'),
  getThemeReviews: (id) => api.get(`/marketplace/themes/${id}/reviews`),
  getModuleReviews: (id) => api.get(`/marketplace/modules/${id}/reviews`),
  createThemeReview: (id, reviewData) => api.post(`/marketplace/themes/${id}/reviews`, reviewData),
  createModuleReview: (id, reviewData) => api.post(`/marketplace/modules/${id}/reviews`, reviewData),
  updateReview: (id, reviewData) => api.put(`/marketplace/reviews/${id}`, reviewData),
  deleteReview: (id) => api.delete(`/marketplace/reviews/${id}`),
};

export default api;
