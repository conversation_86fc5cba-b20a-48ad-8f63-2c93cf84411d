<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AppConfiguration extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'app_name',
        'bundle_id',
        'version',
        'build_number',
        'branding',
        'features',
        'app_store',
        'build_settings',
        'status',
        'last_build_at',
        'last_build_status',
        'last_build_log',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'branding' => 'array',
        'features' => 'array',
        'app_store' => 'array',
        'build_settings' => 'array',
        'last_build_at' => 'datetime',
    ];

    /**
     * Get the tenant that owns the app configuration.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the builds for the app configuration.
     */
    public function builds(): HasMany
    {
        return $this->hasMany(AppBuild::class);
    }

    /**
     * Generate app.json configuration for Expo.
     */
    public function generateExpoConfig(): array
    {
        $branding = $this->branding ?? [];
        $features = $this->features ?? [];
        $appStore = $this->app_store ?? [];

        return [
            'expo' => [
                'name' => $this->app_name,
                'slug' => strtolower(str_replace(' ', '-', $this->app_name)),
                'version' => $this->version,
                'orientation' => 'portrait',
                'icon' => $branding['icon'] ?? './assets/icon.png',
                'userInterfaceStyle' => $branding['theme'] ?? 'light',
                'splash' => [
                    'image' => $branding['splash'] ?? './assets/splash.png',
                    'resizeMode' => 'contain',
                    'backgroundColor' => $branding['splashBackground'] ?? '#ffffff',
                ],
                'updates' => [
                    'fallbackToCacheTimeout' => 0,
                ],
                'assetBundlePatterns' => [
                    '**/*',
                ],
                'ios' => [
                    'supportsTablet' => true,
                    'bundleIdentifier' => $this->bundle_id,
                    'buildNumber' => $this->build_number,
                ],
                'android' => [
                    'adaptiveIcon' => [
                        'foregroundImage' => $branding['adaptiveIcon'] ?? './assets/adaptive-icon.png',
                        'backgroundColor' => $branding['adaptiveIconBackground'] ?? '#FFFFFF',
                    ],
                    'package' => $this->bundle_id,
                    'versionCode' => (int) $this->build_number,
                ],
                'web' => [
                    'favicon' => $branding['favicon'] ?? './assets/favicon.png',
                ],
                'extra' => [
                    'tenantId' => $this->tenant_id,
                    'features' => $features,
                    'apiUrl' => $this->tenant->domain ?? 'https://api.lms.com',
                ],
                'plugins' => [
                    'expo-build-properties' => [
                        'ios' => [
                            'useFrameworks' => 'static',
                        ],
                    ],
                ],
            ],
        ];
    }

    /**
     * Increment the build number.
     */
    public function incrementBuildNumber(): void
    {
        $this->build_number = (string) ((int) $this->build_number + 1);
        $this->save();
    }
}
