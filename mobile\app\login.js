import React, { useState, useContext } from 'react';
import { StyleSheet, View, Image, TouchableOpacity, Alert, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

import Container from '@/components/ui/Container';
import Text from '@/components/ui/Text';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { AuthContext } from '@/context/AuthContext';
import { useTheme } from '@/components/ThemeProvider';

export default function LoginScreen() {
  // For demo purposes, pre-fill with a mock user
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('password');
  const [isLoading, setIsLoading] = useState(false);
  const { login, isAuthenticated } = useContext(AuthContext);
  const { theme } = useTheme();

  // Debug authentication state
  React.useEffect(() => {
    console.log('Login screen - Authentication state:', isAuthenticated ? 'Authenticated' : 'Not authenticated');
  }, [isAuthenticated]);

  const handleLogin = async () => {
    console.log('Login button pressed');
    if (!email || !password) {
      Alert.alert('Error', 'Please enter both email and password');
      return;
    }

    setIsLoading(true);
    try {
      console.log('Attempting login with:', email);
      const result = await login(email, password);

      if (result.success) {
        console.log('Login successful, navigating to home');
        Alert.alert(
          'Login Successful',
          'You have successfully logged in!',
          [
            {
              text: 'OK',
              onPress: () => {
                // Navigate to home after user acknowledges
                router.replace('/');
              }
            }
          ]
        );
      } else {
        console.log('Login failed:', result.message);
        Alert.alert('Login Failed', result.message || 'Invalid credentials. Please try again.');
      }
    } catch (error) {
      console.error('Unexpected login error:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegister = () => {
    router.push('/register');
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <Container style={styles.container}>
      <TouchableOpacity style={styles.backButton} onPress={handleBack}>
        <Ionicons name="arrow-back" size={24} color={theme.text} />
      </TouchableOpacity>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.logoContainer}>
            <Image
              source={require('@/assets/images/icon.png')}
              style={styles.logo}
              resizeMode="contain"
            />
          </View>

          <Text variant="h1" style={styles.title}>Welcome Back</Text>
          <Text variant="body" color="muted" style={styles.subtitle}>
            Sign in to continue to your learning journey
          </Text>

          <View style={styles.form}>
            <Input
              label="Email"
              value={email}
              onChangeText={setEmail}
              autoCapitalize="none"
              keyboardType="email-address"
              placeholder="Enter your email"
              fullWidth
            />

            <Input
              label="Password"
              value={password}
              onChangeText={setPassword}
              secureTextEntry
              placeholder="Enter your password"
              fullWidth
            />

            <TouchableOpacity style={styles.forgotPassword}>
              <Text color="primary">Forgot Password?</Text>
            </TouchableOpacity>

            <Button
              onPress={handleLogin}
              loading={isLoading}
              fullWidth
              style={styles.loginButton}
            >
              Sign In
            </Button>

            <View style={styles.dividerContainer}>
              <View style={[styles.divider, { backgroundColor: theme.border }]} />
              <Text variant="caption" color="muted" style={styles.dividerText}>OR</Text>
              <View style={[styles.divider, { backgroundColor: theme.border }]} />
            </View>

            <Button
              variant="outline"
              onPress={() => Alert.alert('Google Sign In', 'This would connect to Google authentication')}
              fullWidth
              style={styles.googleButton}
            >
              <View style={styles.googleButtonContent}>
                <Image
                  source={require('@/assets/images/icon.png')}
                  style={styles.googleIcon}
                />
                <Text>Sign in with Google</Text>
              </View>
            </Button>
          </View>

          <View style={styles.footer}>
            <Text variant="body">Don't have an account? </Text>
            <TouchableOpacity onPress={handleRegister}>
              <Text color="primary" bold>Sign Up</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </Container>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backButton: {
    position: 'absolute',
    top: 50,
    left: 16,
    zIndex: 10,
    padding: 8,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
    paddingTop: 80,
    paddingBottom: 40,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  logo: {
    width: 100,
    height: 100,
  },
  title: {
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    textAlign: 'center',
    marginBottom: 32,
  },
  form: {
    width: '100%',
    marginBottom: 24,
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: 24,
  },
  loginButton: {
    marginBottom: 24,
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  divider: {
    flex: 1,
    height: 1,
  },
  dividerText: {
    marginHorizontal: 16,
  },
  googleButton: {
    marginBottom: 24,
  },
  googleButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  googleIcon: {
    width: 20,
    height: 20,
    marginRight: 8,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 16,
  },
});
