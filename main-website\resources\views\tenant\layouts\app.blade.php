<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ $tenant->name }} - @yield('title', 'Learning Management System')</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    <link rel="stylesheet" href="{{ asset('build/assets/app.css') }}">

    <style>
        :root {
            --primary-color: #ff7700;
            --primary-color-light: #ff9a40;
            --primary-color-dark: #cc5c00;
            --secondary-color: #0369a1;
            --secondary-color-light: #38bdf8;
            --secondary-color-dark: #075985;
        }
        
        /* Primary color utility classes */
        .bg-primary-50 { background-color: #fff7ed; }
        .bg-primary-100 { background-color: #ffedd5; }
        .bg-primary-200 { background-color: #fed7aa; }
        .bg-primary-300 { background-color: #fdba74; }
        .bg-primary-400 { background-color: #fb923c; }
        .bg-primary-500 { background-color: #ff7700; }
        .bg-primary-600 { background-color: #ea580c; }
        .bg-primary-700 { background-color: #cc5c00; }
        .bg-primary-800 { background-color: #9a3412; }
        .bg-primary-900 { background-color: #7c2d12; }
        
        .text-primary-50 { color: #fff7ed; }
        .text-primary-100 { color: #ffedd5; }
        .text-primary-200 { color: #fed7aa; }
        .text-primary-300 { color: #fdba74; }
        .text-primary-400 { color: #fb923c; }
        .text-primary-500 { color: #ff7700; }
        .text-primary-600 { color: #ea580c; }
        .text-primary-700 { color: #cc5c00; }
        .text-primary-800 { color: #9a3412; }
        .text-primary-900 { color: #7c2d12; }
        
        .border-primary-50 { border-color: #fff7ed; }
        .border-primary-100 { border-color: #ffedd5; }
        .border-primary-200 { border-color: #fed7aa; }
        .border-primary-300 { border-color: #fdba74; }
        .border-primary-400 { border-color: #fb923c; }
        .border-primary-500 { border-color: #ff7700; }
        .border-primary-600 { border-color: #ea580c; }
        .border-primary-700 { border-color: #cc5c00; }
        .border-primary-800 { border-color: #9a3412; }
        .border-primary-900 { border-color: #7c2d12; }
        
        .ring-primary-50 { --tw-ring-color: #fff7ed; }
        .ring-primary-100 { --tw-ring-color: #ffedd5; }
        .ring-primary-200 { --tw-ring-color: #fed7aa; }
        .ring-primary-300 { --tw-ring-color: #fdba74; }
        .ring-primary-400 { --tw-ring-color: #fb923c; }
        .ring-primary-500 { --tw-ring-color: #ff7700; }
        .ring-primary-600 { --tw-ring-color: #ea580c; }
        .ring-primary-700 { --tw-ring-color: #cc5c00; }
        .ring-primary-800 { --tw-ring-color: #9a3412; }
        .ring-primary-900 { --tw-ring-color: #7c2d12; }
    </style>

    <!-- Scripts -->
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</head>
<body class="font-sans antialiased">
    <div class="min-h-screen bg-gray-100">
        <!-- Navigation -->
        <nav class="bg-white border-b border-gray-200" x-data="{ open: false }">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex">
                        <div class="flex-shrink-0 flex items-center">
                            <img class="block h-8 w-auto" src="{{ asset('images/logo.png') }}" alt="{{ $tenant->name }}">
                            <span class="ml-2 text-xl font-bold text-gray-900">{{ $tenant->name }}</span>
                        </div>
                        <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                            <a href="{{ route('tenant.dashboard', $tenant->domain) }}" class="border-primary-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                                Dashboard
                            </a>
                            <a href="{{ route('tenant.courses', $tenant->domain) }}" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                                Courses
                            </a>
                            <a href="{{ route('tenant.students', $tenant->domain) }}" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                                Students
                            </a>
                            <a href="{{ route('tenant.settings', $tenant->domain) }}" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                                Settings
                            </a>
                        </div>
                    </div>
                    <div class="hidden sm:ml-6 sm:flex sm:items-center">
                        <!-- Profile dropdown -->
                        <div class="ml-3 relative" x-data="{ open: false }">
                            <div>
                                <button @click="open = !open" type="button" class="bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500" id="user-menu-button" aria-expanded="false" aria-haspopup="true">
                                    <span class="sr-only">Open user menu</span>
                                    <img class="h-8 w-8 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="">
                                </button>
                            </div>
                            <div x-show="open" @click.away="open = false" class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none" role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button" tabindex="-1">
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem" tabindex="-1" id="user-menu-item-0">Your Profile</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem" tabindex="-1" id="user-menu-item-1">Settings</a>
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem" tabindex="-1" id="user-menu-item-2">Sign out</button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="-mr-2 flex items-center sm:hidden">
                        <!-- Mobile menu button -->
                        <button @click="open = !open" type="button" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500" aria-controls="mobile-menu" aria-expanded="false">
                            <span class="sr-only">Open main menu</span>
                            <svg x-show="!open" class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                            <svg x-show="open" class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Mobile menu, show/hide based on menu state. -->
            <div x-show="open" class="sm:hidden" id="mobile-menu">
                <div class="pt-2 pb-3 space-y-1">
                    <a href="{{ route('tenant.dashboard', $tenant->domain) }}" class="bg-primary-50 border-primary-500 text-primary-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Dashboard</a>
                    <a href="{{ route('tenant.courses', $tenant->domain) }}" class="border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Courses</a>
                    <a href="{{ route('tenant.students', $tenant->domain) }}" class="border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Students</a>
                    <a href="{{ route('tenant.settings', $tenant->domain) }}" class="border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Settings</a>
                </div>
                <div class="pt-4 pb-3 border-t border-gray-200">
                    <div class="flex items-center px-4">
                        <div class="flex-shrink-0">
                            <img class="h-10 w-10 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="">
                        </div>
                        <div class="ml-3">
                            <div class="text-base font-medium text-gray-800">Admin User</div>
                            <div class="text-sm font-medium text-gray-500"><EMAIL></div>
                        </div>
                    </div>
                    <div class="mt-3 space-y-1">
                        <a href="#" class="block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100">Your Profile</a>
                        <a href="#" class="block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100">Settings</a>
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" class="block w-full text-left px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100">Sign out</button>
                        </form>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <main>
            @yield('content')
        </main>
        
        <!-- Footer -->
        <footer class="bg-white">
            <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center">
                    <div>
                        <p class="text-sm text-gray-500">&copy; {{ date('Y') }} {{ $tenant->name }}. All rights reserved.</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Powered by <a href="/" class="text-primary-600 hover:text-primary-500">Naxofy</a></p>
                    </div>
                </div>
            </div>
        </footer>
    </div>
</body>
</html>
