<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class PricingController extends Controller
{
    /**
     * Display the pricing page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $plans = [
            [
                'name' => 'Starter',
                'price' => 49,
                'billing_period' => 'month',
                'description' => 'Perfect for individuals just starting out',
                'features' => [
                    'Up to 5 courses',
                    'Up to 500 students',
                    'Basic theme customization',
                    'Email support',
                    'Standard video hosting',
                    'Basic analytics',
                ],
                'popular' => false,
                'cta_text' => 'Start with Starter',
            ],
            [
                'name' => 'Professional',
                'price' => 99,
                'billing_period' => 'month',
                'description' => 'For growing educational businesses',
                'features' => [
                    'Unlimited courses',
                    'Up to 5,000 students',
                    'Advanced theme customization',
                    'Priority email support',
                    'HD video hosting',
                    'Advanced analytics',
                    'Custom domain',
                    'Remove platform branding',
                    'Course certificates',
                ],
                'popular' => true,
                'cta_text' => 'Go Professional',
            ],
            [
                'name' => 'Enterprise',
                'price' => 299,
                'billing_period' => 'month',
                'description' => 'For established educational institutions',
                'features' => [
                    'Unlimited courses',
                    'Unlimited students',
                    'Complete theme customization',
                    '24/7 dedicated support',
                    '4K video hosting',
                    'Advanced analytics with custom reports',
                    'Custom domain',
                    'Remove platform branding',
                    'Course certificates',
                    'White-label mobile app',
                    'API access',
                    'Single Sign-On (SSO)',
                    'Custom integrations',
                ],
                'popular' => false,
                'cta_text' => 'Contact Sales',
            ],
        ];

        $faqs = [
            [
                'question' => 'Can I upgrade or downgrade my plan later?',
                'answer' => 'Yes, you can upgrade or downgrade your plan at any time. When you upgrade, you\'ll be charged the prorated difference for the remainder of your billing cycle. When you downgrade, the new rate will apply at the start of your next billing cycle.',
            ],
            [
                'question' => 'Do you offer annual billing?',
                'answer' => 'Yes, we offer annual billing with a 20% discount compared to monthly billing. You can select annual billing during signup or switch to annual billing from your account settings.',
            ],
            [
                'question' => 'What payment methods do you accept?',
                'answer' => 'We accept all major credit cards (Visa, Mastercard, American Express, Discover) as well as PayPal. For Enterprise plans, we also offer invoice-based payment options.',
            ],
            [
                'question' => 'Is there a setup fee?',
                'answer' => 'No, there are no setup fees for any of our plans. You only pay the advertised price for your selected plan.',
            ],
            [
                'question' => 'What happens when I reach my student limit?',
                'answer' => 'When you approach your student limit, we\'ll notify you so you can upgrade to a higher plan. If you exceed your limit, existing students will still have access, but new enrollments will be paused until you upgrade or remove students.',
            ],
            [
                'question' => 'Can I get a refund if I\'m not satisfied?',
                'answer' => 'We offer a 30-day money-back guarantee for all new subscriptions. If you\'re not satisfied with our platform within the first 30 days, contact our support team for a full refund.',
            ],
        ];

        return view('marketing.pricing', compact('plans', 'faqs'));
    }
}
