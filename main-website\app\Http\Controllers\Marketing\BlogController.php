<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class BlogController extends Controller
{
    /**
     * Display the blog index page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $categories = [
            'E-Learning',
            'Course Creation',
            'Online Teaching',
            'EdTech',
            'Student Engagement',
            'Mobile Learning',
        ];

        $posts = [
            [
                'id' => 1,
                'title' => '10 Tips for Creating Engaging Online Courses',
                'slug' => '10-tips-for-creating-engaging-online-courses',
                'excerpt' => 'Learn how to create online courses that keep students engaged and coming back for more.',
                'content' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.',
                'category' => 'Course Creation',
                'author' => '<PERSON>',
                'author_avatar' => 'https://source.unsplash.com/random/100x100/?man',
                'published_at' => '2023-06-15',
                'image' => 'https://source.unsplash.com/random/800x600/?course',
                'reading_time' => 5,
            ],
            [
                'id' => 2,
                'title' => 'The Future of E-Learning: Trends to Watch in 2023',
                'slug' => 'the-future-of-e-learning-trends-to-watch-in-2023',
                'excerpt' => 'Stay ahead of the curve with these emerging trends in online education and e-learning.',
                'content' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.',
                'category' => 'E-Learning',
                'author' => 'Sarah Johnson',
                'author_avatar' => 'https://source.unsplash.com/random/100x100/?woman',
                'published_at' => '2023-05-28',
                'image' => 'https://source.unsplash.com/random/800x600/?future',
                'reading_time' => 7,
            ],
            [
                'id' => 3,
                'title' => 'How to Build a Successful Online Teaching Business',
                'slug' => 'how-to-build-a-successful-online-teaching-business',
                'excerpt' => 'Turn your expertise into a thriving online teaching business with these proven strategies.',
                'content' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.',
                'category' => 'Online Teaching',
                'author' => 'Michael Chen',
                'author_avatar' => 'https://source.unsplash.com/random/100x100/?asian,man',
                'published_at' => '2023-05-10',
                'image' => 'https://source.unsplash.com/random/800x600/?business',
                'reading_time' => 10,
            ],
            [
                'id' => 4,
                'title' => 'Mobile Learning: Designing Courses for On-the-Go Students',
                'slug' => 'mobile-learning-designing-courses-for-on-the-go-students',
                'excerpt' => 'Learn how to optimize your courses for mobile devices and reach students wherever they are.',
                'content' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.',
                'category' => 'Mobile Learning',
                'author' => 'Emily Rodriguez',
                'author_avatar' => 'https://source.unsplash.com/random/100x100/?latina',
                'published_at' => '2023-04-22',
                'image' => 'https://source.unsplash.com/random/800x600/?mobile',
                'reading_time' => 6,
            ],
            [
                'id' => 5,
                'title' => 'The Power of Gamification in Online Learning',
                'slug' => 'the-power-of-gamification-in-online-learning',
                'excerpt' => 'Discover how gamification can increase student engagement and improve learning outcomes.',
                'content' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.',
                'category' => 'Student Engagement',
                'author' => 'David Wilson',
                'author_avatar' => 'https://source.unsplash.com/random/100x100/?man,black',
                'published_at' => '2023-04-05',
                'image' => 'https://source.unsplash.com/random/800x600/?game',
                'reading_time' => 8,
            ],
            [
                'id' => 6,
                'title' => 'EdTech Tools Every Online Educator Should Know About',
                'slug' => 'edtech-tools-every-online-educator-should-know-about',
                'excerpt' => 'Enhance your online teaching with these powerful educational technology tools.',
                'content' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.',
                'category' => 'EdTech',
                'author' => 'Lisa Patel',
                'author_avatar' => 'https://source.unsplash.com/random/100x100/?indian,woman',
                'published_at' => '2023-03-18',
                'image' => 'https://source.unsplash.com/random/800x600/?technology',
                'reading_time' => 9,
            ],
        ];

        return view('marketing.blog.index', compact('categories', 'posts'));
    }

    /**
     * Display a specific blog post.
     *
     * @param  string  $slug
     * @return \Illuminate\View\View
     */
    public function show($slug)
    {
        // In a real application, you would fetch the post from the database
        // For now, we'll just return a dummy post based on the slug

        $post = [
            'id' => 1,
            'title' => '10 Tips for Creating Engaging Online Courses',
            'slug' => '10-tips-for-creating-engaging-online-courses',
            'excerpt' => 'Learn how to create online courses that keep students engaged and coming back for more.',
            'content' => '<p>Creating engaging online courses is essential for student success and satisfaction. Here are 10 tips to help you create courses that students will love:</p>
                        <h2>1. Start with clear learning objectives</h2>
                        <p>Define what students will be able to do after completing your course. Clear objectives help you design focused content and help students understand what they\'ll gain.</p>
                        <h2>2. Use a variety of content formats</h2>
                        <p>Mix videos, text, images, quizzes, and interactive elements to cater to different learning styles and keep students engaged.</p>
                        <h2>3. Keep videos short and focused</h2>
                        <p>Aim for videos under 10 minutes that focus on a single concept. This helps maintain attention and makes it easier for students to find specific information later.</p>
                        <h2>4. Include real-world examples and case studies</h2>
                        <p>Connect theoretical concepts to practical applications to help students understand the relevance of what they\'re learning.</p>
                        <h2>5. Add interactive elements</h2>
                        <p>Include quizzes, assignments, discussions, and other activities that require active participation from students.</p>
                        <h2>6. Provide regular feedback</h2>
                        <p>Give students feedback on their progress and performance to help them improve and stay motivated.</p>
                        <h2>7. Create a community</h2>
                        <p>Foster a sense of community through discussion forums, group projects, or live sessions to reduce isolation and increase engagement.</p>
                        <h2>8. Use storytelling techniques</h2>
                        <p>Frame content within stories or narratives to make it more memorable and engaging.</p>
                        <h2>9. Design for mobile</h2>
                        <p>Ensure your course is accessible and functional on mobile devices so students can learn on the go.</p>
                        <h2>10. Continuously improve</h2>
                        <p>Gather feedback from students and use it to refine and improve your course over time.</p>',
            'category' => 'Course Creation',
            'author' => 'John Smith',
            'author_avatar' => 'https://source.unsplash.com/random/100x100/?man',
            'author_bio' => 'John is an experienced online educator with over 10 years of experience in course creation and instructional design.',
            'published_at' => '2023-06-15',
            'image' => 'https://source.unsplash.com/random/1200x600/?course',
            'reading_time' => 5,
        ];

        $relatedPosts = [
            [
                'id' => 2,
                'title' => 'The Future of E-Learning: Trends to Watch in 2023',
                'slug' => 'the-future-of-e-learning-trends-to-watch-in-2023',
                'excerpt' => 'Stay ahead of the curve with these emerging trends in online education and e-learning.',
                'category' => 'E-Learning',
                'author' => 'Sarah Johnson',
                'published_at' => '2023-05-28',
                'image' => 'https://source.unsplash.com/random/400x300/?future',
            ],
            [
                'id' => 3,
                'title' => 'How to Build a Successful Online Teaching Business',
                'slug' => 'how-to-build-a-successful-online-teaching-business',
                'excerpt' => 'Turn your expertise into a thriving online teaching business with these proven strategies.',
                'category' => 'Online Teaching',
                'author' => 'Michael Chen',
                'published_at' => '2023-05-10',
                'image' => 'https://source.unsplash.com/random/400x300/?business',
            ],
            [
                'id' => 5,
                'title' => 'The Power of Gamification in Online Learning',
                'slug' => 'the-power-of-gamification-in-online-learning',
                'excerpt' => 'Discover how gamification can increase student engagement and improve learning outcomes.',
                'category' => 'Student Engagement',
                'author' => 'David Wilson',
                'published_at' => '2023-04-05',
                'image' => 'https://source.unsplash.com/random/400x300/?game',
            ],
        ];

        return view('marketing.blog.show', compact('post', 'relatedPosts'));
    }
}
