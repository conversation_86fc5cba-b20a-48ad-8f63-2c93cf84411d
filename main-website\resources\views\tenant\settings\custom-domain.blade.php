@extends('layouts.tenant')

@section('title', 'Custom Domain Settings')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-900">Custom Domain Settings</h1>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">Current Domain</h2>
        <div class="mb-6">
            <p class="text-gray-700 mb-2">Your LMS is currently accessible at:</p>
            <div class="flex items-center">
                <a href="{{ $tenant->frontend_url }}" target="_blank" class="text-blue-600 hover:text-blue-800 font-medium">
                    {{ $tenant->full_domain }}
                </a>
                <span class="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs font-semibold rounded">Active</span>
            </div>
        </div>

        @if($tenant->custom_domain)
            <h2 class="text-xl font-semibold mb-4">Custom Domain</h2>
            <div class="mb-6">
                <p class="text-gray-700 mb-2">Your custom domain:</p>
                <div class="flex items-center">
                    @if($tenant->custom_domain_verified)
                        <a href="https://{{ $tenant->custom_domain }}" target="_blank" class="text-blue-600 hover:text-blue-800 font-medium">
                            {{ $tenant->custom_domain }}
                        </a>
                        <span class="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs font-semibold rounded">Verified</span>
                    @else
                        <span class="text-gray-800 font-medium">{{ $tenant->custom_domain }}</span>
                        <span class="ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-semibold rounded">Pending Verification</span>
                    @endif
                </div>
            </div>

            @if(!$tenant->custom_domain_verified)
                <div class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-blue-700">
                                Your custom domain needs to be verified. Please add the following DNS records to your domain:
                            </p>
                            <div class="mt-2 bg-white p-3 rounded border border-blue-200">
                                <p class="text-sm font-mono mb-1">Type: <span class="font-semibold">CNAME</span></p>
                                <p class="text-sm font-mono mb-1">Name: <span class="font-semibold">@</span></p>
                                <p class="text-sm font-mono">Value: <span class="font-semibold">{{ config('app.domain', 'naxofy.com') }}</span></p>
                            </div>
                            <div class="mt-4">
                                <form action="{{ route('tenant.settings.custom-domain.verify', $tenant->domain) }}" method="POST">
                                    @csrf
                                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                        Verify Domain
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        @endif

        <h2 class="text-xl font-semibold mb-4">{{ $tenant->custom_domain ? 'Update' : 'Add' }} Custom Domain</h2>
        <form action="{{ route('tenant.settings.custom-domain.update', $tenant->domain) }}" method="POST">
            @csrf
            <div class="mb-4">
                <label for="custom_domain" class="block text-sm font-medium text-gray-700 mb-1">Domain Name</label>
                <input type="text" name="custom_domain" id="custom_domain" value="{{ old('custom_domain', $tenant->custom_domain) }}" 
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="yourdomain.com">
                <p class="mt-1 text-sm text-gray-500">Enter your domain without 'http://' or 'https://'</p>
                @error('custom_domain')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="bg-gray-50 p-4 rounded-md mb-4">
                <h3 class="text-sm font-medium text-gray-900 mb-2">Important Notes:</h3>
                <ul class="list-disc pl-5 text-sm text-gray-700 space-y-1">
                    <li>You must own the domain you want to use.</li>
                    <li>After adding your domain, you'll need to update your DNS settings with your domain registrar.</li>
                    <li>It may take up to 48 hours for DNS changes to propagate.</li>
                    <li>Your subdomain ({{ $tenant->domain }}.{{ config('app.tenant_domain', 'naxofy.com') }}) will continue to work even after setting up a custom domain.</li>
                </ul>
            </div>

            <div class="flex justify-end">
                <button type="submit" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    {{ $tenant->custom_domain ? 'Update Domain' : 'Add Domain' }}
                </button>
            </div>
        </form>
    </div>
</div>
@endsection
