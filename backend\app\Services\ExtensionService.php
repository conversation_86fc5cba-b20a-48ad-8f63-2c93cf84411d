<?php

namespace App\Services;

use App\Models\Module;
use App\Models\Theme;
use App\Models\Tenant;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use ZipArchive;

class ExtensionService
{
    /**
     * Install a theme from a zip file.
     *
     * @param string $zipPath Path to the zip file
     * @param int|null $tenantId Tenant ID if this is a tenant-specific theme
     * @return Theme|null
     */
    public function installTheme(string $zipPath, ?int $tenantId = null): ?Theme
    {
        $tempDir = storage_path('app/temp/themes/' . Str::random(10));
        
        if (!$this->extractZip($zipPath, $tempDir)) {
            return null;
        }
        
        // Check for theme.json file
        $themeJsonPath = $tempDir . '/theme.json';
        if (!file_exists($themeJsonPath)) {
            $this->cleanupTempDir($tempDir);
            return null;
        }
        
        // Parse theme.json
        $themeData = json_decode(file_get_contents($themeJsonPath), true);
        if (!$themeData || !isset($themeData['name']) || !isset($themeData['version'])) {
            $this->cleanupTempDir($tempDir);
            return null;
        }
        
        // Create theme record
        $theme = new Theme();
        $theme->name = $themeData['name'];
        $theme->slug = Str::slug($themeData['name']);
        $theme->version = $themeData['version'];
        $theme->description = $themeData['description'] ?? null;
        $theme->author = $themeData['author'] ?? null;
        $theme->author_url = $themeData['author_url'] ?? null;
        $theme->settings = $themeData['settings'] ?? null;
        $theme->colors = $themeData['colors'] ?? null;
        $theme->fonts = $themeData['fonts'] ?? null;
        $theme->components = $themeData['components'] ?? null;
        $theme->tenant_id = $tenantId;
        
        // Handle thumbnail
        if (isset($themeData['thumbnail']) && file_exists($tempDir . '/' . $themeData['thumbnail'])) {
            $thumbnailPath = 'themes/' . $theme->slug . '/thumbnail.' . pathinfo($themeData['thumbnail'], PATHINFO_EXTENSION);
            Storage::disk('public')->put($thumbnailPath, file_get_contents($tempDir . '/' . $themeData['thumbnail']));
            $theme->thumbnail = Storage::url($thumbnailPath);
        }
        
        // Copy theme files to public directory
        $themePath = 'themes/' . $theme->slug;
        $this->copyDirectory($tempDir, public_path($themePath));
        
        // Save theme record
        $theme->save();
        
        // Cleanup
        $this->cleanupTempDir($tempDir);
        
        return $theme;
    }
    
    /**
     * Install a module from a zip file.
     *
     * @param string $zipPath Path to the zip file
     * @param int|null $tenantId Tenant ID if this is a tenant-specific module
     * @return Module|null
     */
    public function installModule(string $zipPath, ?int $tenantId = null): ?Module
    {
        $tempDir = storage_path('app/temp/modules/' . Str::random(10));
        
        if (!$this->extractZip($zipPath, $tempDir)) {
            return null;
        }
        
        // Check for module.json file
        $moduleJsonPath = $tempDir . '/module.json';
        if (!file_exists($moduleJsonPath)) {
            $this->cleanupTempDir($tempDir);
            return null;
        }
        
        // Parse module.json
        $moduleData = json_decode(file_get_contents($moduleJsonPath), true);
        if (!$moduleData || !isset($moduleData['name']) || !isset($moduleData['version'])) {
            $this->cleanupTempDir($tempDir);
            return null;
        }
        
        // Create module record
        $module = new Module();
        $module->name = $moduleData['name'];
        $module->slug = Str::slug($moduleData['name']);
        $module->version = $moduleData['version'];
        $module->description = $moduleData['description'] ?? null;
        $module->author = $moduleData['author'] ?? null;
        $module->author_url = $moduleData['author_url'] ?? null;
        $module->settings = $moduleData['settings'] ?? null;
        $module->permissions = $moduleData['permissions'] ?? null;
        $module->hooks = $moduleData['hooks'] ?? null;
        $module->dependencies = $moduleData['dependencies'] ?? null;
        $module->tenant_id = $tenantId;
        
        // Handle thumbnail
        if (isset($moduleData['thumbnail']) && file_exists($tempDir . '/' . $moduleData['thumbnail'])) {
            $thumbnailPath = 'modules/' . $module->slug . '/thumbnail.' . pathinfo($moduleData['thumbnail'], PATHINFO_EXTENSION);
            Storage::disk('public')->put($thumbnailPath, file_get_contents($tempDir . '/' . $moduleData['thumbnail']));
            $module->thumbnail = Storage::url($thumbnailPath);
        }
        
        // Copy module files to modules directory
        $modulePath = 'modules/' . $module->slug;
        $this->copyDirectory($tempDir, base_path($modulePath));
        
        // Save module record
        $module->save();
        
        // Cleanup
        $this->cleanupTempDir($tempDir);
        
        return $module;
    }
    
    /**
     * Get active theme for a tenant.
     *
     * @param int|null $tenantId Tenant ID
     * @return Theme|null
     */
    public function getActiveTheme(?int $tenantId = null): ?Theme
    {
        if ($tenantId) {
            // First check for tenant-specific active theme
            $theme = Theme::where('tenant_id', $tenantId)
                ->where('is_active', true)
                ->first();
                
            if ($theme) {
                return $theme;
            }
        }
        
        // Fall back to global active theme
        return Theme::whereNull('tenant_id')
            ->where('is_active', true)
            ->first();
    }
    
    /**
     * Get active modules for a tenant.
     *
     * @param int|null $tenantId Tenant ID
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getActiveModules(?int $tenantId = null)
    {
        $modules = Module::where('is_active', true)
            ->whereNull('tenant_id')
            ->get();
            
        if ($tenantId) {
            // Add tenant-specific active modules
            $tenantModules = Module::where('is_active', true)
                ->where('tenant_id', $tenantId)
                ->get();
                
            $modules = $modules->merge($tenantModules);
            
            // Add modules installed by the tenant
            $tenant = Tenant::find($tenantId);
            if ($tenant) {
                $installedModules = $tenant->installedModules()
                    ->wherePivot('is_active', true)
                    ->get();
                    
                $modules = $modules->merge($installedModules);
            }
        }
        
        return $modules;
    }
    
    /**
     * Extract a zip file to a directory.
     *
     * @param string $zipPath Path to the zip file
     * @param string $extractTo Path to extract to
     * @return bool
     */
    private function extractZip(string $zipPath, string $extractTo): bool
    {
        if (!file_exists($zipPath)) {
            return false;
        }
        
        // Create extraction directory if it doesn't exist
        if (!file_exists($extractTo)) {
            mkdir($extractTo, 0755, true);
        }
        
        $zip = new ZipArchive();
        if ($zip->open($zipPath) !== true) {
            return false;
        }
        
        $zip->extractTo($extractTo);
        $zip->close();
        
        return true;
    }
    
    /**
     * Copy a directory recursively.
     *
     * @param string $source Source directory
     * @param string $destination Destination directory
     * @return bool
     */
    private function copyDirectory(string $source, string $destination): bool
    {
        if (!is_dir($source)) {
            return false;
        }
        
        // Create destination directory if it doesn't exist
        if (!is_dir($destination)) {
            mkdir($destination, 0755, true);
        }
        
        $dir = opendir($source);
        while (($file = readdir($dir)) !== false) {
            if ($file === '.' || $file === '..') {
                continue;
            }
            
            $sourcePath = $source . '/' . $file;
            $destPath = $destination . '/' . $file;
            
            if (is_dir($sourcePath)) {
                $this->copyDirectory($sourcePath, $destPath);
            } else {
                copy($sourcePath, $destPath);
            }
        }
        
        closedir($dir);
        return true;
    }
    
    /**
     * Clean up a temporary directory.
     *
     * @param string $tempDir Path to the temporary directory
     * @return void
     */
    private function cleanupTempDir(string $tempDir): void
    {
        if (!is_dir($tempDir)) {
            return;
        }
        
        $dir = opendir($tempDir);
        while (($file = readdir($dir)) !== false) {
            if ($file === '.' || $file === '..') {
                continue;
            }
            
            $path = $tempDir . '/' . $file;
            
            if (is_dir($path)) {
                $this->cleanupTempDir($path);
            } else {
                unlink($path);
            }
        }
        
        closedir($dir);
        rmdir($tempDir);
    }
}
