<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class ContactController extends Controller
{
    /**
     * Display the contact us page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $inquiryTypes = [
            'general' => 'General Inquiry',
            'sales' => 'Sales Question',
            'support' => 'Technical Support',
            'partnership' => 'Partnership Opportunity',
            'enterprise' => 'Enterprise Plan',
        ];

        $inquiry = request('inquiry', 'general');

        return view('marketing.contact', compact('inquiryTypes', 'inquiry'));
    }

    /**
     * Handle the contact form submission.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255',
            'inquiry_type' => 'required|string|in:general,sales,support,partnership,enterprise',
            'message' => 'required|string',
        ]);

        // In a real application, you would send an email or store the contact request
        // For now, we'll just redirect with a success message
        
        return redirect()->route('marketing.contact')->with('success', 'Thank you for your message. We will get back to you soon!');
    }
}
