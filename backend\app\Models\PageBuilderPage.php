<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PageBuilderPage extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'title',
        'slug',
        'description',
        'meta',
        'content',
        'status',
        'is_homepage',
        'published_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'meta' => 'array',
        'content' => 'array',
        'is_homepage' => 'boolean',
        'published_at' => 'datetime',
    ];

    /**
     * Get the tenant that owns the page.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Scope a query to only include published pages.
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published')
            ->where(function($q) {
                $q->whereNull('published_at')
                  ->orWhere('published_at', '<=', now());
            });
    }

    /**
     * Scope a query to only include draft pages.
     */
    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    /**
     * Scope a query to only include the homepage.
     */
    public function scopeHomepage($query)
    {
        return $query->where('is_homepage', true);
    }

    /**
     * Publish the page.
     */
    public function publish(): void
    {
        $this->status = 'published';
        $this->published_at = now();
        $this->save();
    }

    /**
     * Unpublish the page.
     */
    public function unpublish(): void
    {
        $this->status = 'draft';
        $this->save();
    }

    /**
     * Set as homepage.
     */
    public function setAsHomepage(): void
    {
        // First, unset any existing homepage
        PageBuilderPage::where('tenant_id', $this->tenant_id)
            ->where('is_homepage', true)
            ->update(['is_homepage' => false]);

        // Then set this page as homepage
        $this->is_homepage = true;
        $this->save();
    }

    /**
     * Render the page content.
     */
    public function render(): array
    {
        // In a real implementation, this would process the content array
        // and render each section/component with the appropriate data
        return [
            'title' => $this->title,
            'meta' => $this->meta,
            'content' => $this->content,
        ];
    }
}
