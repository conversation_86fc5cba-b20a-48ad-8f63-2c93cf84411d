<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\MarketplaceCategory;
use App\Models\Theme;
use App\Models\Module;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class MarketplaceController extends Controller
{
    /**
     * Get featured themes and modules for the marketplace homepage.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function featured()
    {
        $featuredThemes = Theme::with('category')
            ->approved()
            ->featured()
            ->take(6)
            ->get();
            
        $featuredModules = Module::with('category')
            ->approved()
            ->featured()
            ->take(6)
            ->get();
            
        return response()->json([
            'success' => true,
            'data' => [
                'themes' => $featuredThemes,
                'modules' => $featuredModules
            ]
        ]);
    }
    
    /**
     * Get all marketplace categories.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function categories(Request $request)
    {
        $type = $request->input('type', 'both');
        
        $categories = MarketplaceCategory::active()
            ->ofType($type)
            ->orderBy('order')
            ->get();
            
        return response()->json([
            'success' => true,
            'data' => $categories
        ]);
    }
    
    /**
     * Search themes and modules.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function search(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'query' => 'required|string|min:2',
            'type' => 'nullable|in:theme,module,both',
            'category_id' => 'nullable|exists:marketplace_categories,id',
            'price' => 'nullable|in:free,paid,all',
            'sort' => 'nullable|in:newest,popular,rating',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:50',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
        
        $query = $request->input('query');
        $type = $request->input('type', 'both');
        $categoryId = $request->input('category_id');
        $price = $request->input('price', 'all');
        $sort = $request->input('sort', 'newest');
        $perPage = $request->input('per_page', 12);
        
        $results = [];
        
        // Search themes
        if ($type === 'theme' || $type === 'both') {
            $themesQuery = Theme::with('category')
                ->approved()
                ->where(function($q) use ($query) {
                    $q->where('name', 'like', "%{$query}%")
                      ->orWhere('description', 'like', "%{$query}%");
                });
                
            if ($categoryId) {
                $themesQuery->where('category_id', $categoryId);
            }
            
            if ($price === 'free') {
                $themesQuery->free();
            } elseif ($price === 'paid') {
                $themesQuery->paid();
            }
            
            switch ($sort) {
                case 'popular':
                    $themesQuery->orderBy('downloads', 'desc');
                    break;
                case 'rating':
                    // This is a simplification - in a real app you'd need a more sophisticated approach
                    // for sorting by average rating since it's a computed property
                    $themesQuery->orderBy('id', 'desc'); // Fallback to newest
                    break;
                case 'newest':
                default:
                    $themesQuery->orderBy('created_at', 'desc');
                    break;
            }
            
            $results['themes'] = $themesQuery->paginate($perPage);
        }
        
        // Search modules
        if ($type === 'module' || $type === 'both') {
            $modulesQuery = Module::with('category')
                ->approved()
                ->where(function($q) use ($query) {
                    $q->where('name', 'like', "%{$query}%")
                      ->orWhere('description', 'like', "%{$query}%");
                });
                
            if ($categoryId) {
                $modulesQuery->where('category_id', $categoryId);
            }
            
            if ($price === 'free') {
                $modulesQuery->free();
            } elseif ($price === 'paid') {
                $modulesQuery->paid();
            }
            
            switch ($sort) {
                case 'popular':
                    $modulesQuery->orderBy('downloads', 'desc');
                    break;
                case 'rating':
                    // Same simplification as above
                    $modulesQuery->orderBy('id', 'desc'); // Fallback to newest
                    break;
                case 'newest':
                default:
                    $modulesQuery->orderBy('created_at', 'desc');
                    break;
            }
            
            $results['modules'] = $modulesQuery->paginate($perPage);
        }
        
        return response()->json([
            'success' => true,
            'data' => $results
        ]);
    }
    
    /**
     * Get themes for the marketplace.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function themes(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'category_id' => 'nullable|exists:marketplace_categories,id',
            'price' => 'nullable|in:free,paid,all',
            'sort' => 'nullable|in:newest,popular,rating',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:50',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
        
        $categoryId = $request->input('category_id');
        $price = $request->input('price', 'all');
        $sort = $request->input('sort', 'newest');
        $perPage = $request->input('per_page', 12);
        
        $themesQuery = Theme::with('category')
            ->approved();
            
        if ($categoryId) {
            $themesQuery->where('category_id', $categoryId);
        }
        
        if ($price === 'free') {
            $themesQuery->free();
        } elseif ($price === 'paid') {
            $themesQuery->paid();
        }
        
        switch ($sort) {
            case 'popular':
                $themesQuery->orderBy('downloads', 'desc');
                break;
            case 'rating':
                // Simplification as noted above
                $themesQuery->orderBy('id', 'desc');
                break;
            case 'newest':
            default:
                $themesQuery->orderBy('created_at', 'desc');
                break;
        }
        
        $themes = $themesQuery->paginate($perPage);
        
        return response()->json([
            'success' => true,
            'data' => $themes
        ]);
    }
    
    /**
     * Get modules for the marketplace.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function modules(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'category_id' => 'nullable|exists:marketplace_categories,id',
            'price' => 'nullable|in:free,paid,all',
            'sort' => 'nullable|in:newest,popular,rating',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:50',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
        
        $categoryId = $request->input('category_id');
        $price = $request->input('price', 'all');
        $sort = $request->input('sort', 'newest');
        $perPage = $request->input('per_page', 12);
        
        $modulesQuery = Module::with('category')
            ->approved();
            
        if ($categoryId) {
            $modulesQuery->where('category_id', $categoryId);
        }
        
        if ($price === 'free') {
            $modulesQuery->free();
        } elseif ($price === 'paid') {
            $modulesQuery->paid();
        }
        
        switch ($sort) {
            case 'popular':
                $modulesQuery->orderBy('downloads', 'desc');
                break;
            case 'rating':
                // Simplification as noted above
                $modulesQuery->orderBy('id', 'desc');
                break;
            case 'newest':
            default:
                $modulesQuery->orderBy('created_at', 'desc');
                break;
        }
        
        $modules = $modulesQuery->paginate($perPage);
        
        return response()->json([
            'success' => true,
            'data' => $modules
        ]);
    }
}
