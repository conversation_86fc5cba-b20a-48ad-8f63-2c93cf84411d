<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class NotificationController extends Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        // Check if user is admin for all methods
        $this->middleware(function ($request, $next) {
            if (!Auth::check() || !Auth::user()->isAdmin()) {
                return redirect()->route('login')->with('error', 'You do not have permission to access this area.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of the notifications.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get notifications from database or use mock data if table doesn't exist yet
        try {
            $notifications = Notification::orderBy('created_at', 'desc')->paginate(10);
            
            if ($notifications->isEmpty()) {
                $notifications = $this->getMockNotifications();
            }
        } catch (\Exception $e) {
            $notifications = $this->getMockNotifications();
        }

        return view('admin.notifications.index', compact('notifications'));
    }

    /**
     * Show the form for creating a new notification.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        // Get tenants and users for recipient selection
        try {
            $tenants = Tenant::where('status', 'active')->get(['id', 'name']);
            $users = User::where('status', 'active')->get(['id', 'name', 'email', 'role']);
        } catch (\Exception $e) {
            $tenants = collect();
            $users = collect();
        }

        return view('admin.notifications.create', compact('tenants', 'users'));
    }

    /**
     * Store a newly created notification in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'type' => 'required|in:info,success,warning,error',
            'recipient_type' => 'required|in:all,tenant,user,role',
            'tenant_id' => 'required_if:recipient_type,tenant|nullable|exists:tenants,id',
            'user_id' => 'required_if:recipient_type,user|nullable|exists:users,id',
            'role' => 'required_if:recipient_type,role|nullable|in:admin,tenant,student',
            'is_important' => 'boolean',
            'send_email' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Create notification
            $notification = Notification::create([
                'title' => $request->title,
                'message' => $request->message,
                'type' => $request->type,
                'recipient_type' => $request->recipient_type,
                'tenant_id' => $request->tenant_id,
                'user_id' => $request->user_id,
                'role' => $request->role,
                'is_important' => $request->has('is_important'),
                'created_by' => Auth::id(),
            ]);

            // Send notification to recipients
            $this->sendNotification($notification, $request->has('send_email'));

            return redirect()->route('admin.notifications.index')
                ->with('success', 'Notification created and sent successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to create notification: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified notification.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        try {
            $notification = Notification::with(['creator', 'tenant', 'user'])->findOrFail($id);
        } catch (\Exception $e) {
            // Use mock data if notification not found
            $notification = collect($this->getMockNotifications())->firstWhere('id', $id);
            
            if (!$notification) {
                return redirect()->route('admin.notifications.index')
                    ->with('error', 'Notification not found.');
            }
        }

        return view('admin.notifications.show', compact('notification'));
    }

    /**
     * Remove the specified notification from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        try {
            $notification = Notification::findOrFail($id);
            
            $notification->delete();
            
            return redirect()->route('admin.notifications.index')
                ->with('success', 'Notification deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to delete notification: ' . $e->getMessage());
        }
    }

    /**
     * Send a notification to recipients.
     *
     * @param  \App\Models\Notification  $notification
     * @param  bool  $sendEmail
     * @return void
     */
    private function sendNotification($notification, $sendEmail = false)
    {
        // In a real implementation, this would send the notification to the recipients
        // For now, we'll just log it
        \Log::info("Notification sent: {$notification->title}");
        
        if ($sendEmail) {
            \Log::info("Email notification sent: {$notification->title}");
        }
    }

    /**
     * Get mock notifications data.
     *
     * @return \Illuminate\Support\Collection
     */
    private function getMockNotifications()
    {
        return collect([
            (object) [
                'id' => 1,
                'title' => 'System Maintenance',
                'message' => 'The system will be down for maintenance on Sunday, June 15, 2023, from 2:00 AM to 4:00 AM UTC.',
                'type' => 'warning',
                'recipient_type' => 'all',
                'tenant_id' => null,
                'user_id' => null,
                'role' => null,
                'is_important' => true,
                'created_by' => 1,
                'created_at' => now()->subDays(5),
                'updated_at' => now()->subDays(5),
                'creator' => (object) [
                    'id' => 1,
                    'name' => 'Admin User',
                ],
                'tenant' => null,
                'user' => null,
            ],
            (object) [
                'id' => 2,
                'title' => 'New Feature: Custom Domains',
                'message' => 'We are excited to announce that custom domains are now available for all premium plans. You can now use your own domain for your LMS platform.',
                'type' => 'info',
                'recipient_type' => 'tenant',
                'tenant_id' => 1,
                'user_id' => null,
                'role' => null,
                'is_important' => false,
                'created_by' => 1,
                'created_at' => now()->subDays(3),
                'updated_at' => now()->subDays(3),
                'creator' => (object) [
                    'id' => 1,
                    'name' => 'Admin User',
                ],
                'tenant' => (object) [
                    'id' => 1,
                    'name' => 'John\'s Academy',
                ],
                'user' => null,
            ],
            (object) [
                'id' => 3,
                'title' => 'Account Verification',
                'message' => 'Your account has been verified. You can now access all features of the platform.',
                'type' => 'success',
                'recipient_type' => 'user',
                'tenant_id' => null,
                'user_id' => 3,
                'role' => null,
                'is_important' => false,
                'created_by' => 1,
                'created_at' => now()->subDays(2),
                'updated_at' => now()->subDays(2),
                'creator' => (object) [
                    'id' => 1,
                    'name' => 'Admin User',
                ],
                'tenant' => null,
                'user' => (object) [
                    'id' => 3,
                    'name' => 'Jane Smith',
                ],
            ],
            (object) [
                'id' => 4,
                'title' => 'Important: Security Update',
                'message' => 'We have released a security update. Please make sure your system is up to date.',
                'type' => 'error',
                'recipient_type' => 'role',
                'tenant_id' => null,
                'user_id' => null,
                'role' => 'admin',
                'is_important' => true,
                'created_by' => 1,
                'created_at' => now()->subDay(),
                'updated_at' => now()->subDay(),
                'creator' => (object) [
                    'id' => 1,
                    'name' => 'Admin User',
                ],
                'tenant' => null,
                'user' => null,
            ],
            (object) [
                'id' => 5,
                'title' => 'Welcome to Naxofy',
                'message' => 'Welcome to Naxofy! We are excited to have you on board. Here are some tips to get you started...',
                'type' => 'info',
                'recipient_type' => 'user',
                'tenant_id' => null,
                'user_id' => 6,
                'role' => null,
                'is_important' => false,
                'created_by' => 1,
                'created_at' => now()->subHours(12),
                'updated_at' => now()->subHours(12),
                'creator' => (object) [
                    'id' => 1,
                    'name' => 'Admin User',
                ],
                'tenant' => null,
                'user' => (object) [
                    'id' => 6,
                    'name' => 'Charlie Brown',
                ],
            ],
        ]);
    }
}
