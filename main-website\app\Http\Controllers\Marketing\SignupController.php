<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class SignupController extends Controller
{
    /**
     * Display the signup page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $plans = [
            [
                'name' => 'Starter',
                'price' => 49,
                'billing_period' => 'month',
                'description' => 'Perfect for individuals just starting out',
                'features' => [
                    'Up to 5 courses',
                    'Up to 500 students',
                    'Basic theme customization',
                    'Email support',
                    'Standard video hosting',
                    'Basic analytics',
                ],
                'popular' => false,
                'cta_text' => 'Start with Starter',
            ],
            [
                'name' => 'Professional',
                'price' => 99,
                'billing_period' => 'month',
                'description' => 'For growing educational businesses',
                'features' => [
                    'Unlimited courses',
                    'Up to 5,000 students',
                    'Advanced theme customization',
                    'Priority email support',
                    'HD video hosting',
                    'Advanced analytics',
                    'Custom domain',
                    'Remove platform branding',
                    'Course certificates',
                ],
                'popular' => true,
                'cta_text' => 'Go Professional',
            ],
            [
                'name' => 'Enterprise',
                'price' => 299,
                'billing_period' => 'month',
                'description' => 'For established educational institutions',
                'features' => [
                    'Unlimited courses',
                    'Unlimited students',
                    'Complete theme customization',
                    '24/7 dedicated support',
                    '4K video hosting',
                    'Advanced analytics with custom reports',
                    'Custom domain',
                    'Remove platform branding',
                    'Course certificates',
                    'White-label mobile app',
                    'API access',
                    'Single Sign-On (SSO)',
                    'Custom integrations',
                ],
                'popular' => false,
                'cta_text' => 'Contact Sales',
            ],
        ];

        return view('marketing.signup', compact('plans'));
    }

    /**
     * Process the signup form.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        // Validate the request
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255',
            'password' => 'required|string|min:8|confirmed',
            'company_name' => 'required|string|max:255',
            'subdomain' => 'required|string|max:255|alpha_dash',
            'plan' => 'required|string|in:starter,professional,enterprise',
            'terms' => 'required|accepted',
        ]);

        // Create the tenant using the TenantController
        $tenantController = new \App\Http\Controllers\TenantController();
        return $tenantController->createFromSignup($request);
    }

    /**
     * Display the signup success page.
     *
     * @return \Illuminate\View\View
     */
    public function success()
    {
        // If there's no signup data in the session, redirect to the signup page
        if (!session()->has('signup_data')) {
            return redirect()->route('signup');
        }

        $signupData = session('signup_data');

        return view('marketing.signup-success', compact('signupData'));
    }
}
