<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ThemeTemplate extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'theme_id',
        'name',
        'slug',
        'type',
        'description',
        'content',
        'variables',
        'settings',
        'is_default',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'variables' => 'array',
        'settings' => 'array',
        'is_default' => 'boolean',
    ];

    /**
     * Get the theme that owns the template.
     */
    public function theme(): BelongsTo
    {
        return $this->belongsTo(Theme::class);
    }

    /**
     * Get the customizations based on this template.
     */
    public function customizations(): HasMany
    {
        return $this->hasMany(ThemeCustomization::class);
    }

    /**
     * Scope a query to only include templates of a specific type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query to only include default templates.
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Create a customization based on this template.
     */
    public function createCustomization(int $tenantId, array $overrides = []): ThemeCustomization
    {
        $customization = new ThemeCustomization();
        $customization->tenant_id = $tenantId;
        $customization->theme_id = $this->theme_id;
        $customization->theme_template_id = $this->id;
        $customization->name = $overrides['name'] ?? $this->name;
        $customization->slug = $overrides['slug'] ?? $this->slug;
        $customization->type = $this->type;
        $customization->description = $overrides['description'] ?? $this->description;
        $customization->content = $overrides['content'] ?? $this->content;
        $customization->variables = $overrides['variables'] ?? $this->variables;
        $customization->settings = $overrides['settings'] ?? $this->settings;
        $customization->is_active = $overrides['is_active'] ?? true;
        $customization->save();

        return $customization;
    }
}
