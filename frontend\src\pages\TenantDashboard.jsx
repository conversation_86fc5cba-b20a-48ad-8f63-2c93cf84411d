import { useState, useEffect, useContext } from 'react';
import { Routes, Route, Link as RouterLink, useNavigate, useLocation } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Grid,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Button,
  Card,
  CardContent,
  CardActions,
  CircularProgress,
  Alert,
  TextField,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle
} from '@mui/material';
import DashboardIcon from '@mui/icons-material/Dashboard';
import SchoolIcon from '@mui/icons-material/School';
import PeopleIcon from '@mui/icons-material/People';
import BarChartIcon from '@mui/icons-material/BarChart';
import SettingsIcon from '@mui/icons-material/Settings';
import PaletteIcon from '@mui/icons-material/Palette';
import ExtensionIcon from '@mui/icons-material/Extension';
import AddIcon from '@mui/icons-material/Add';
import { AuthContext } from '../context/AuthContext';
import { courseService, tenantService } from '../services/api';

// Dashboard Overview Component
const Overview = () => {
  const [stats, setStats] = useState({
    totalCourses: 0,
    totalStudents: 0,
    totalRevenue: 0,
    activeEnrollments: 0
  });
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const { user } = useContext(AuthContext);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      // In a real app, you would have specific API endpoints for these
      const coursesResponse = await courseService.getAllCourses();

      // Filter courses by instructor
      const instructorCourses = coursesResponse.data.data.filter(
        course => course.instructor_id === user.id
      );

      setCourses(instructorCourses);

      // Calculate stats
      const totalStudents = instructorCourses.reduce(
        (sum, course) => sum + (course.enrollments?.length || 0), 0
      );

      const totalRevenue = instructorCourses.reduce(
        (sum, course) => sum + (course.enrollments?.filter(e => e.payment_status === 'completed').length || 0) * course.price, 0
      );

      setStats({
        totalCourses: instructorCourses.length,
        totalStudents,
        totalRevenue,
        activeEnrollments: totalStudents
      });
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Dashboard Overview
      </Typography>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column', height: 140 }}>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Total Courses
            </Typography>
            <Typography component="p" variant="h4">
              {stats.totalCourses}
            </Typography>
            <Typography color="text.secondary" sx={{ flex: 1 }}>
              Active courses
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column', height: 140 }}>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Total Students
            </Typography>
            <Typography component="p" variant="h4">
              {stats.totalStudents}
            </Typography>
            <Typography color="text.secondary" sx={{ flex: 1 }}>
              Enrolled students
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column', height: 140 }}>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Total Revenue
            </Typography>
            <Typography component="p" variant="h4">
              ${stats.totalRevenue.toFixed(2)}
            </Typography>
            <Typography color="text.secondary" sx={{ flex: 1 }}>
              From all courses
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column', height: 140 }}>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Active Enrollments
            </Typography>
            <Typography component="p" variant="h4">
              {stats.activeEnrollments}
            </Typography>
            <Typography color="text.secondary" sx={{ flex: 1 }}>
              Current students
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* Recent Courses */}
      <Paper sx={{ p: 2, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">Recent Courses</Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => navigate('/tenant/dashboard/courses/create')}
          >
            Create Course
          </Button>
        </Box>
        <Divider sx={{ mb: 2 }} />

        {courses.length === 0 ? (
          <Typography variant="body1" sx={{ textAlign: 'center', py: 4 }}>
            You haven't created any courses yet.
          </Typography>
        ) : (
          <Grid container spacing={3}>
            {courses.slice(0, 3).map((course) => (
              <Grid item key={course.id} xs={12} md={4}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      {course.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      {course.description.substring(0, 100)}...
                    </Typography>
                    <Typography variant="body2">
                      <strong>Price:</strong> ${course.price}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Enrollments:</strong> {course.enrollments?.length || 0}
                    </Typography>
                  </CardContent>
                  <CardActions>
                    <Button
                      size="small"
                      onClick={() => navigate(`/tenant/dashboard/courses/${course.id}`)}
                    >
                      View Details
                    </Button>
                    <Button
                      size="small"
                      onClick={() => navigate(`/tenant/dashboard/courses/${course.id}/edit`)}
                    >
                      Edit
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}

        {courses.length > 3 && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
            <Button
              variant="outlined"
              onClick={() => navigate('/tenant/dashboard/courses')}
            >
              View All Courses
            </Button>
          </Box>
        )}
      </Paper>
    </Box>
  );
};

// Courses Management Component
const CoursesManagement = () => {
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();
  const { user } = useContext(AuthContext);

  useEffect(() => {
    fetchCourses();
  }, []);

  const fetchCourses = async () => {
    try {
      setLoading(true);
      const response = await courseService.getAllCourses();

      // Filter courses by instructor
      const instructorCourses = response.data.data.filter(
        course => course.instructor_id === user.id
      );

      setCourses(instructorCourses);
      setError(null);
    } catch (error) {
      console.error('Error fetching courses:', error);
      setError('Failed to load courses. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteCourse = async (courseId) => {
    try {
      await courseService.deleteCourse(courseId);
      setCourses(courses.filter(course => course.id !== courseId));
    } catch (error) {
      console.error('Error deleting course:', error);
      setError('Failed to delete course. Please try again later.');
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ my: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Courses Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => navigate('/tenant/dashboard/courses/create')}
        >
          Create Course
        </Button>
      </Box>

      {courses.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            No courses found
          </Typography>
          <Typography variant="body1" paragraph>
            You haven't created any courses yet. Start by creating your first course.
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => navigate('/tenant/dashboard/courses/create')}
          >
            Create Your First Course
          </Button>
        </Paper>
      ) : (
        <Grid container spacing={3}>
          {courses.map((course) => (
            <Grid item key={course.id} xs={12} md={6} lg={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    {course.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {course.description.substring(0, 100)}...
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2">
                        <strong>Price:</strong> ${course.price}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2">
                        <strong>Enrollments:</strong> {course.enrollments?.length || 0}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2">
                        <strong>Lessons:</strong> {course.lessons?.length || 0}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2">
                        <strong>Created:</strong> {new Date(course.created_at).toLocaleDateString()}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
                <CardActions>
                  <Button
                    size="small"
                    onClick={() => navigate(`/tenant/dashboard/courses/${course.id}`)}
                  >
                    View
                  </Button>
                  <Button
                    size="small"
                    onClick={() => navigate(`/tenant/dashboard/courses/${course.id}/edit`)}
                  >
                    Edit
                  </Button>
                  <Button
                    size="small"
                    color="error"
                    onClick={() => handleDeleteCourse(course.id)}
                  >
                    Delete
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
    </Box>
  );
};

// Course Form Component
const CourseForm = () => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    price: 0,
    thumbnail: null
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const isEditMode = location.pathname.includes('/edit');
  const courseId = isEditMode ? location.pathname.split('/')[4] : null;

  useEffect(() => {
    if (isEditMode && courseId) {
      fetchCourseDetails();
    }
  }, [isEditMode, courseId]);

  const fetchCourseDetails = async () => {
    try {
      setLoading(true);
      const response = await courseService.getCourseById(courseId);
      const course = response.data.course;

      setFormData({
        title: course.title,
        description: course.description,
        price: course.price,
        thumbnail: null
      });

      setError(null);
    } catch (error) {
      console.error('Error fetching course details:', error);
      setError('Failed to load course details. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prevState => ({
      ...prevState,
      [name]: name === 'price' ? parseFloat(value) : value
    }));
  };

  const handleFileChange = (e) => {
    setFormData(prevState => ({
      ...prevState,
      thumbnail: e.target.files[0]
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError(null);

      const formDataToSend = new FormData();
      formDataToSend.append('title', formData.title);
      formDataToSend.append('description', formData.description);
      formDataToSend.append('price', formData.price);

      if (formData.thumbnail) {
        formDataToSend.append('thumbnail', formData.thumbnail);
      }

      if (isEditMode) {
        await courseService.updateCourse(courseId, formDataToSend);
      } else {
        await courseService.createCourse(formDataToSend);
      }

      setSuccess(true);
      setTimeout(() => {
        navigate('/tenant/dashboard/courses');
      }, 2000);
    } catch (error) {
      console.error('Error saving course:', error);
      setError('Failed to save course. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  if (loading && isEditMode) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        {isEditMode ? 'Edit Course' : 'Create New Course'}
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          Course {isEditMode ? 'updated' : 'created'} successfully!
        </Alert>
      )}

      <Paper sx={{ p: 3 }}>
        <Box component="form" onSubmit={handleSubmit} noValidate>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                required
                fullWidth
                id="title"
                label="Course Title"
                name="title"
                value={formData.title}
                onChange={handleChange}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                required
                fullWidth
                id="description"
                label="Course Description"
                name="description"
                multiline
                rows={4}
                value={formData.description}
                onChange={handleChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                required
                fullWidth
                id="price"
                label="Price ($)"
                name="price"
                type="number"
                inputProps={{ min: 0, step: 0.01 }}
                value={formData.price}
                onChange={handleChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  Course Thumbnail
                </Typography>
                <input
                  accept="image/*"
                  id="thumbnail"
                  type="file"
                  onChange={handleFileChange}
                />
              </Box>
            </Grid>
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Button
                  variant="outlined"
                  onClick={() => navigate('/tenant/dashboard/courses')}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  disabled={loading}
                >
                  {loading ? 'Saving...' : isEditMode ? 'Update Course' : 'Create Course'}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Paper>
    </Box>
  );
};

// Students Management Component
const StudentsManagement = () => {
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Students Management
      </Typography>
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="body1">
          Student management features are not available in the preview.
        </Typography>
      </Paper>
    </Box>
  );
};

// Analytics Component
const Analytics = () => {
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Analytics
      </Typography>
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="body1">
          Analytics features are not available in the preview.
        </Typography>
      </Paper>
    </Box>
  );
};

// Settings Component
const Settings = () => {
  const [tenant, setTenant] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user } = useContext(AuthContext);

  useEffect(() => {
    fetchTenantDetails();
  }, []);

  const fetchTenantDetails = async () => {
    try {
      setLoading(true);
      // In a real app, you would have a specific endpoint to get tenant details
      const response = await tenantService.getTenantById(user.tenant_id);
      setTenant(response.data);
      setError(null);
    } catch (error) {
      console.error('Error fetching tenant details:', error);
      setError('Failed to load tenant details. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ my: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Settings
      </Typography>

      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          School/Institution Details
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="School Name"
              value={tenant?.name || ''}
              disabled
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Domain"
              value={tenant?.domain || ''}
              disabled
            />
          </Grid>
          <Grid item xs={12}>
            <Button variant="contained">
              Edit Details
            </Button>
          </Grid>
        </Grid>
      </Paper>

      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Account Settings
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Name"
              value={user?.name || ''}
              disabled
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Email"
              value={user?.email || ''}
              disabled
            />
          </Grid>
          <Grid item xs={12}>
            <Button variant="contained">
              Change Password
            </Button>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

// Main TenantDashboard Component
const TenantDashboard = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useContext(AuthContext);
  const [openDialog, setOpenDialog] = useState(false);

  useEffect(() => {
    if (user && user.role !== 'tenant') {
      setOpenDialog(true);
    }
  }, [user]);

  const handleDialogClose = () => {
    setOpenDialog(false);
    navigate('/dashboard');
  };

  const getActiveItem = () => {
    const path = location.pathname;
    if (path.includes('/courses')) return 1;
    if (path.includes('/students')) return 2;
    if (path.includes('/analytics')) return 3;
    if (path.includes('/settings')) return 4;
    return 0;
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Dialog
        open={openDialog}
        onClose={handleDialogClose}
      >
        <DialogTitle>Access Denied</DialogTitle>
        <DialogContent>
          <DialogContentText>
            You don't have permission to access the instructor dashboard. Only users with the tenant role can access this area.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDialogClose} autoFocus>
            Go to Dashboard
          </Button>
        </DialogActions>
      </Dialog>

      <Grid container spacing={3}>
        {/* Sidebar */}
        <Grid item xs={12} md={3} lg={2}>
          <Paper sx={{ height: '100%' }}>
            <List component="nav">
              <ListItem
                button
                component={RouterLink}
                to="/tenant/dashboard"
                selected={getActiveItem() === 0}
              >
                <ListItemIcon>
                  <DashboardIcon />
                </ListItemIcon>
                <ListItemText primary="Dashboard" />
              </ListItem>
              <ListItem
                button
                component={RouterLink}
                to="/tenant/dashboard/courses"
                selected={getActiveItem() === 1}
              >
                <ListItemIcon>
                  <SchoolIcon />
                </ListItemIcon>
                <ListItemText primary="Courses" />
              </ListItem>
              <ListItem
                button
                component={RouterLink}
                to="/tenant/dashboard/students"
                selected={getActiveItem() === 2}
              >
                <ListItemIcon>
                  <PeopleIcon />
                </ListItemIcon>
                <ListItemText primary="Students" />
              </ListItem>
              <ListItem
                button
                component={RouterLink}
                to="/tenant/dashboard/analytics"
                selected={getActiveItem() === 3}
              >
                <ListItemIcon>
                  <BarChartIcon />
                </ListItemIcon>
                <ListItemText primary="Analytics" />
              </ListItem>
              <Divider sx={{ my: 1 }} />
              <ListItem
                button
                component={RouterLink}
                to="/tenant/dashboard/settings"
                selected={getActiveItem() === 4}
              >
                <ListItemIcon>
                  <SettingsIcon />
                </ListItemIcon>
                <ListItemText primary="Settings" />
              </ListItem>
              <Divider sx={{ my: 1 }} />
              <ListItem
                button
                component={RouterLink}
                to="/themes"
                selected={false}
              >
                <ListItemIcon>
                  <PaletteIcon />
                </ListItemIcon>
                <ListItemText primary="Themes" />
              </ListItem>
              <ListItem
                button
                component={RouterLink}
                to="/modules"
                selected={false}
              >
                <ListItemIcon>
                  <ExtensionIcon />
                </ListItemIcon>
                <ListItemText primary="Modules" />
              </ListItem>
            </List>
          </Paper>
        </Grid>

        {/* Main Content */}
        <Grid item xs={12} md={9} lg={10}>
          <Paper sx={{ p: 3 }}>
            <Routes>
              <Route path="/" element={<Overview />} />
              <Route path="/courses" element={<CoursesManagement />} />
              <Route path="/courses/create" element={<CourseForm />} />
              <Route path="/courses/:id/edit" element={<CourseForm />} />
              <Route path="/students" element={<StudentsManagement />} />
              <Route path="/analytics" element={<Analytics />} />
              <Route path="/settings" element={<Settings />} />
            </Routes>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default TenantDashboard;
