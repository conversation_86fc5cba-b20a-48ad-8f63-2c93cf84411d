import { useContext } from 'react';
import { Navigate } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';
import { CircularProgress, Box } from '@mui/material';

const ProtectedRoute = ({ children, role }) => {
  const { isAuthenticated, hasRole, loading } = useContext(AuthContext);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!isAuthenticated()) {
    return <Navigate to="/login" />;
  }

  if (role) {
    // Handle array of roles
    if (Array.isArray(role)) {
      // Check if user has any of the required roles
      const hasAnyRole = role.some(r => hasRole(r));
      if (!hasAnyRole) {
        return <Navigate to="/dashboard" />;
      }
    } else if (!hasRole(role)) {
      // Handle single role
      return <Navigate to="/dashboard" />;
    }
  }

  return children;
};

export default ProtectedRoute;
