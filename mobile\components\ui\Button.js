import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { useTheme } from '../ThemeProvider';

/**
 * Custom Button component with consistent styling
 * 
 * @param {Object} props - Component props
 * @param {string} props.variant - Button variant (primary, secondary, outline, text)
 * @param {boolean} props.fullWidth - Whether the button should take full width
 * @param {boolean} props.loading - Whether to show loading indicator
 * @param {boolean} props.disabled - Whether the button is disabled
 * @param {function} props.onPress - Function to call when button is pressed
 * @param {Object} props.style - Additional style for the button
 * @param {Object} props.textStyle - Additional style for the button text
 */
const Button = ({ 
  children, 
  variant = 'primary', 
  fullWidth = false,
  loading = false,
  disabled = false,
  onPress,
  style,
  textStyle,
  ...props 
}) => {
  const { theme } = useTheme();
  
  // Determine button style based on variant
  const getButtonStyle = () => {
    switch (variant) {
      case 'secondary':
        return {
          backgroundColor: theme.secondary,
          borderColor: theme.secondary,
        };
      case 'outline':
        return {
          backgroundColor: 'transparent',
          borderColor: theme.primary,
          borderWidth: 1,
        };
      case 'text':
        return {
          backgroundColor: 'transparent',
          borderWidth: 0,
          paddingHorizontal: 8,
        };
      case 'danger':
        return {
          backgroundColor: theme.error,
          borderColor: theme.error,
        };
      case 'primary':
      default:
        return {
          backgroundColor: theme.primary,
          borderColor: theme.primary,
        };
    }
  };
  
  // Determine text style based on variant
  const getTextStyle = () => {
    switch (variant) {
      case 'outline':
        return {
          color: theme.primary,
        };
      case 'text':
        return {
          color: theme.primary,
        };
      case 'primary':
      case 'secondary':
      case 'danger':
      default:
        return {
          color: theme.textLight,
        };
    }
  };
  
  // Apply disabled styles
  const disabledStyle = disabled || loading ? {
    opacity: 0.6,
  } : {};
  
  return (
    <TouchableOpacity
      style={[
        styles.button,
        getButtonStyle(),
        fullWidth && styles.fullWidth,
        disabledStyle,
        style,
      ]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}
      {...props}
    >
      {loading ? (
        <ActivityIndicator 
          color={variant === 'outline' || variant === 'text' ? theme.primary : theme.textLight} 
          size="small" 
        />
      ) : (
        <Text style={[styles.text, getTextStyle(), textStyle]}>
          {children}
        </Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  fullWidth: {
    width: '100%',
  },
  text: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default Button;
