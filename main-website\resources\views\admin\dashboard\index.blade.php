@extends('admin.layouts.app')

@section('title', 'Admin Dashboard')
@section('header', 'Dashboard')

@section('content')
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <!-- Tenants Stats -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500">Total Tenants</p>
                    <p class="text-3xl font-bold text-gray-900">{{ $stats['tenants']['total'] }}</p>
                </div>
                <div class="bg-primary-100 p-3 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                </div>
            </div>
            <div class="mt-4 flex items-center justify-between text-sm">
                <div class="flex items-center">
                    <span class="inline-block h-2 w-2 rounded-full bg-green-500 mr-1"></span>
                    <span class="text-gray-500">Active: {{ $stats['tenants']['active'] }}</span>
                </div>
                <div class="flex items-center">
                    <span class="inline-block h-2 w-2 rounded-full bg-yellow-500 mr-1"></span>
                    <span class="text-gray-500">Pending: {{ $stats['tenants']['pending'] }}</span>
                </div>
                <div class="flex items-center">
                    <span class="inline-block h-2 w-2 rounded-full bg-red-500 mr-1"></span>
                    <span class="text-gray-500">Suspended: {{ $stats['tenants']['suspended'] }}</span>
                </div>
            </div>
        </div>

        <!-- Users Stats -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500">Total Users</p>
                    <p class="text-3xl font-bold text-gray-900">{{ $stats['users']['total'] }}</p>
                </div>
                <div class="bg-blue-100 p-3 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </div>
            </div>
            <div class="mt-4 flex items-center justify-between text-sm">
                <div class="flex items-center">
                    <span class="inline-block h-2 w-2 rounded-full bg-purple-500 mr-1"></span>
                    <span class="text-gray-500">Admins: {{ $stats['users']['admins'] }}</span>
                </div>
                <div class="flex items-center">
                    <span class="inline-block h-2 w-2 rounded-full bg-primary-500 mr-1"></span>
                    <span class="text-gray-500">Tenants: {{ $stats['users']['tenants'] }}</span>
                </div>
                <div class="flex items-center">
                    <span class="inline-block h-2 w-2 rounded-full bg-green-500 mr-1"></span>
                    <span class="text-gray-500">Students: {{ $stats['users']['students'] }}</span>
                </div>
            </div>
        </div>

        <!-- Subscriptions Stats -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500">Subscriptions</p>
                    <p class="text-3xl font-bold text-gray-900">{{ $stats['subscriptions']['total'] }}</p>
                </div>
                <div class="bg-green-100 p-3 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
                    </svg>
                </div>
            </div>
            <div class="mt-4 flex items-center justify-between text-sm">
                <div class="flex items-center">
                    <span class="inline-block h-2 w-2 rounded-full bg-green-500 mr-1"></span>
                    <span class="text-gray-500">Active: {{ $stats['subscriptions']['active'] }}</span>
                </div>
                <div class="flex items-center">
                    <span class="inline-block h-2 w-2 rounded-full bg-red-500 mr-1"></span>
                    <span class="text-gray-500">Canceled: {{ $stats['subscriptions']['canceled'] }}</span>
                </div>
                <div class="flex items-center">
                    <span class="inline-block h-2 w-2 rounded-full bg-blue-500 mr-1"></span>
                    <span class="text-gray-500">Trial: {{ $stats['subscriptions']['trial'] }}</span>
                </div>
            </div>
        </div>

        <!-- Revenue Stats -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500">Total Revenue</p>
                    <p class="text-3xl font-bold text-gray-900">${{ number_format($stats['payments']['revenue'], 2) }}</p>
                </div>
                <div class="bg-yellow-100 p-3 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                    </svg>
                </div>
            </div>
            <div class="mt-4 flex items-center justify-between text-sm">
                <div class="flex items-center">
                    <span class="inline-block h-2 w-2 rounded-full bg-green-500 mr-1"></span>
                    <span class="text-gray-500">Successful: {{ $stats['payments']['successful'] }}</span>
                </div>
                <div class="flex items-center">
                    <span class="inline-block h-2 w-2 rounded-full bg-yellow-500 mr-1"></span>
                    <span class="text-gray-500">Pending: {{ $stats['payments']['pending'] }}</span>
                </div>
                <div class="flex items-center">
                    <span class="inline-block h-2 w-2 rounded-full bg-red-500 mr-1"></span>
                    <span class="text-gray-500">Failed: {{ $stats['payments']['failed'] }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Tenant Signups Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Tenant Signups ({{ date('Y') }})</h2>
            <canvas id="tenantSignupsChart" height="300"></canvas>
        </div>

        <!-- Revenue Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Revenue ({{ date('Y') }})</h2>
            <canvas id="revenueChart" height="300"></canvas>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Recent Tenants -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Recent Tenants</h2>
            </div>
            <div class="divide-y divide-gray-200">
                @forelse($recentTenants as $tenant)
                    <div class="px-6 py-4">
                        <div class="flex items-center">
                            @if($tenant->logo)
                                <img src="{{ asset('storage/' . $tenant->logo) }}" alt="{{ $tenant->name }}" class="h-10 w-10 rounded-full object-cover">
                            @else
                                <div class="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                                    <span class="text-primary-500 font-semibold">{{ substr($tenant->name, 0, 1) }}</span>
                                </div>
                            @endif
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-900">{{ $tenant->name }}</p>
                                <p class="text-xs text-gray-500">{{ $tenant->domain }}</p>
                                <p class="text-xs text-gray-400">{{ $tenant->created_at->diffForHumans() }}</p>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="px-6 py-4 text-center text-gray-500">
                        No recent tenants.
                    </div>
                @endforelse
            </div>
            <div class="px-6 py-4 border-t border-gray-200 text-center">
                <span class="text-sm font-medium text-gray-500">View all tenants</span>
            </div>
        </div>

        <!-- Recent Payments -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Recent Payments</h2>
            </div>
            <div class="divide-y divide-gray-200">
                @forelse($recentPayments as $payment)
                    <div class="px-6 py-4">
                        <div class="flex items-center">
                            <div class="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-900">${{ number_format($payment->amount, 2) }}</p>
                                <p class="text-xs text-gray-500">{{ $payment->tenant->name }}</p>
                                <p class="text-xs text-gray-400">{{ $payment->created_at->diffForHumans() }}</p>
                            </div>
                            <div class="ml-auto">
                                @if($payment->status === 'succeeded')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Successful
                                    </span>
                                @elseif($payment->status === 'pending')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Pending
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Failed
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="px-6 py-4 text-center text-gray-500">
                        No recent payments.
                    </div>
                @endforelse
            </div>
            <div class="px-6 py-4 border-t border-gray-200 text-center">
                <span class="text-sm font-medium text-gray-500">View all payments</span>
            </div>
        </div>

        <!-- Recent Support Tickets -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Recent Support Tickets</h2>
            </div>
            <div class="divide-y divide-gray-200">
                @forelse($recentTickets as $ticket)
                    <div class="px-6 py-4">
                        <div class="flex items-center">
                            <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-900">{{ Str::limit($ticket->subject, 30) }}</p>
                                <p class="text-xs text-gray-500">{{ $ticket->tenant->name }}</p>
                                <p class="text-xs text-gray-400">{{ $ticket->created_at->diffForHumans() }}</p>
                            </div>
                            <div class="ml-auto">
                                @if($ticket->status === 'open')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Open
                                    </span>
                                @elseif($ticket->status === 'pending')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Pending
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Closed
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="px-6 py-4 text-center text-gray-500">
                        No recent support tickets.
                    </div>
                @endforelse
            </div>
            <div class="px-6 py-4 border-t border-gray-200 text-center">
                <span class="text-sm font-medium text-gray-500">View all tickets</span>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Tenant Signups Chart
        const tenantSignupsCtx = document.getElementById('tenantSignupsChart').getContext('2d');
        const tenantSignupsChart = new Chart(tenantSignupsCtx, {
            type: 'bar',
            data: {
                labels: @json($tenantSignups->pluck('month_name')),
                datasets: [{
                    label: 'Tenant Signups',
                    data: @json($tenantSignups->pluck('count')),
                    backgroundColor: '#ff7700',
                    borderColor: '#cc5c00',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });

        // Revenue Chart
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        const revenueChart = new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: @json($revenueByMonth->pluck('month_name')),
                datasets: [{
                    label: 'Revenue ($)',
                    data: @json($revenueByMonth->pluck('total')),
                    backgroundColor: 'rgba(3, 105, 161, 0.2)',
                    borderColor: '#0369a1',
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value;
                            }
                        }
                    }
                }
            }
        });
    });
</script>
@endpush
