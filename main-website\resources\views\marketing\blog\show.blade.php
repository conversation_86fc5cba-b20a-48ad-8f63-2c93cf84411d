@extends('marketing.layouts.app')

@section('title', $post['title'] . ' - Naxofy Blog')

@section('content')
    <!-- Blog Post Content -->
    <div class="bg-white py-12">
        <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="mb-8">
                <a href="{{ route('blog') }}" class="text-primary-500 hover:text-primary-600 flex items-center">
                    <svg class="h-5 w-5 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                    Back to Blog
                </a>
            </div>

            <div class="prose prose-lg max-w-none">
                <h1>{{ $post['title'] }}</h1>

                <div class="flex items-center mt-4 mb-8">
                    <div class="flex-shrink-0">
                        <img class="h-10 w-10 rounded-full" src="{{ $post['author_avatar'] }}" alt="{{ $post['author'] }}">
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-900">{{ $post['author'] }}</p>
                        <div class="flex space-x-1 text-sm text-gray-500">
                            <time datetime="{{ $post['published_at'] }}">{{ \Carbon\Carbon::parse($post['published_at'])->format('M d, Y') }}</time>
                            <span aria-hidden="true">&middot;</span>
                            <span>{{ $post['reading_time'] }} min read</span>
                        </div>
                    </div>
                </div>

                <img class="w-full h-auto rounded-lg mb-8" src="{{ $post['image'] }}" alt="{{ $post['title'] }}">

                {!! $post['content'] !!}
            </div>

            <!-- Author Bio -->
            <div class="mt-12 bg-gray-50 p-6 rounded-lg">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <img class="h-16 w-16 rounded-full" src="{{ $post['author_avatar'] }}" alt="{{ $post['author'] }}">
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900">About the Author</h3>
                        <p class="text-base text-gray-500 mt-1">
                            {{ $post['author_bio'] }}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Related Posts -->
            @if(count($relatedPosts) > 0)
            <div class="mt-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Related Posts</h2>
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    @foreach($relatedPosts as $relatedPost)
                    <div class="bg-white overflow-hidden shadow rounded-lg">
                        <img class="h-48 w-full object-cover" src="{{ $relatedPost['image'] }}" alt="{{ $relatedPost['title'] }}">
                        <div class="p-4">
                            <a href="{{ route('blog.show', $relatedPost['slug']) }}" class="block mt-1 text-lg leading-tight font-medium text-gray-900 hover:underline">{{ $relatedPost['title'] }}</a>
                            <p class="mt-2 text-gray-500">
                                {{ $relatedPost['excerpt'] }}
                            </p>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif
        </div>
    </div>
@endsection
