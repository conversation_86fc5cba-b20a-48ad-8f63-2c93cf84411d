<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class TestDatabaseConnections extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:test-connections';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test connections to all configured databases';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing database connections...');

        // Test default connection
        $this->testConnection('default', config('database.default'));

        // Test mysql_main connection
        $this->testConnection('mysql_main', 'mysql_main');

        // Test mysql_api connection
        $this->testConnection('mysql_api', 'mysql_api');

        $this->info('Database connection tests completed.');

        return Command::SUCCESS;
    }

    /**
     * Test a database connection.
     *
     * @param string $name
     * @param string $connection
     * @return void
     */
    protected function testConnection(string $name, string $connection)
    {
        $this->info("Testing {$name} connection ({$connection})...");

        try {
            // Test connection
            DB::connection($connection)->getPdo();
            $this->info("✓ Connection successful");

            // Get database name
            $database = DB::connection($connection)->getDatabaseName();
            $this->info("✓ Connected to database: {$database}");

            // List tables
            $tables = Schema::connection($connection)->getAllTables();
            $tableCount = count($tables);
            $this->info("✓ Found {$tableCount} tables");

            if ($tableCount > 0) {
                $tableNames = array_column($tables, 'Tables_in_' . $database);
                $this->info("Tables: " . implode(', ', $tableNames));

                // Sample data from a few tables
                foreach (array_slice($tableNames, 0, 3) as $table) {
                    $count = DB::connection($connection)->table($table)->count();
                    $this->info("  - {$table}: {$count} records");
                }
            }
        } catch (\Exception $e) {
            $this->error("✗ Connection failed: " . $e->getMessage());
        }

        $this->newLine();
    }
}
