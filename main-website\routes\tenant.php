<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Tenant\DashboardController;
use App\Http\Controllers\Tenant\CourseController;
use App\Http\Controllers\Tenant\StudentController;
use App\Http\Controllers\Tenant\SettingController;

/*
|--------------------------------------------------------------------------
| Tenant Routes
|--------------------------------------------------------------------------
|
| These routes are for tenant-specific dashboards and functionality.
| They can be accessed via subdomain or path-based routing.
|
*/

// Tenant Routes via Subdomain
Route::domain('{domain}.'.config('app.tenant_domain', 'localhost'))->middleware(['auth', 'role:tenant'])->group(function () {
    Route::get('/', [DashboardController::class, 'index'])->name('tenant.dashboard');
    Route::get('/courses', [CourseController::class, 'index'])->name('tenant.courses');
    Route::get('/students', [StudentController::class, 'index'])->name('tenant.students');
    Route::get('/settings', [SettingController::class, 'index'])->name('tenant.settings');
    
    // Course Management
    Route::get('/courses/create', [CourseController::class, 'create'])->name('tenant.courses.create');
    Route::post('/courses', [CourseController::class, 'store'])->name('tenant.courses.store');
    Route::get('/courses/{id}', [CourseController::class, 'show'])->name('tenant.courses.show');
    Route::get('/courses/{id}/edit', [CourseController::class, 'edit'])->name('tenant.courses.edit');
    Route::put('/courses/{id}', [CourseController::class, 'update'])->name('tenant.courses.update');
    Route::delete('/courses/{id}', [CourseController::class, 'destroy'])->name('tenant.courses.destroy');
    
    // Student Management
    Route::get('/students/create', [StudentController::class, 'create'])->name('tenant.students.create');
    Route::post('/students', [StudentController::class, 'store'])->name('tenant.students.store');
    Route::get('/students/{id}', [StudentController::class, 'show'])->name('tenant.students.show');
    Route::get('/students/{id}/edit', [StudentController::class, 'edit'])->name('tenant.students.edit');
    Route::put('/students/{id}', [StudentController::class, 'update'])->name('tenant.students.update');
    Route::delete('/students/{id}', [StudentController::class, 'destroy'])->name('tenant.students.destroy');
});

// Fallback for tenant routes when not using subdomains
Route::prefix('tenant/{domain}')->name('tenant.')->middleware(['auth', 'role:tenant'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/courses', [CourseController::class, 'index'])->name('courses');
    Route::get('/students', [StudentController::class, 'index'])->name('students');
    Route::get('/settings', [SettingController::class, 'index'])->name('settings');
    
    // Course Management
    Route::get('/courses/create', [CourseController::class, 'create'])->name('courses.create');
    Route::post('/courses', [CourseController::class, 'store'])->name('courses.store');
    Route::get('/courses/{id}', [CourseController::class, 'show'])->name('courses.show');
    Route::get('/courses/{id}/edit', [CourseController::class, 'edit'])->name('courses.edit');
    Route::put('/courses/{id}', [CourseController::class, 'update'])->name('courses.update');
    Route::delete('/courses/{id}', [CourseController::class, 'destroy'])->name('courses.destroy');
    
    // Student Management
    Route::get('/students/create', [StudentController::class, 'create'])->name('students.create');
    Route::post('/students', [StudentController::class, 'store'])->name('students.store');
    Route::get('/students/{id}', [StudentController::class, 'show'])->name('students.show');
    Route::get('/students/{id}/edit', [StudentController::class, 'edit'])->name('students.edit');
    Route::put('/students/{id}', [StudentController::class, 'update'])->name('students.update');
    Route::delete('/students/{id}', [StudentController::class, 'destroy'])->name('students.destroy');
});
