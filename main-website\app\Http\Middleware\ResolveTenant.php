<?php

namespace App\Http\Middleware;

use App\Services\DomainService;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ResolveTenant
{
    /**
     * The domain service instance.
     *
     * @var DomainService
     */
    protected $domainService;

    /**
     * Create a new middleware instance.
     *
     * @param DomainService $domainService
     * @return void
     */
    public function __construct(DomainService $domainService)
    {
        $this->domainService = $domainService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if this is the main website
        if ($this->domainService->isMainWebsite($request)) {
            return $next($request);
        }
        
        // Resolve the tenant from the request
        $tenant = $this->domainService->getTenantFromRequest($request);
        
        // If no tenant is found, redirect to the main website
        if (!$tenant) {
            return redirect(config('app.url'));
        }
        
        // Check if the tenant is active
        if (!$tenant->isActive()) {
            // If the tenant is suspended, show a suspended page
            if ($tenant->isSuspended()) {
                return response()->view('tenant.suspended', ['tenant' => $tenant], 403);
            }
            
            // If the tenant is pending, show a pending page
            if ($tenant->isPending()) {
                return response()->view('tenant.pending', ['tenant' => $tenant], 403);
            }
            
            // Otherwise, redirect to the main website
            return redirect(config('app.url'));
        }
        
        // Check if the tenant has expired
        if ($tenant->hasExpired()) {
            return response()->view('tenant.expired', ['tenant' => $tenant], 403);
        }
        
        // Share the tenant with all views
        view()->share('tenant', $tenant);
        
        // Add the tenant to the request
        $request->tenant = $tenant;
        
        return $next($request);
    }
}
