<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Tenant Domain
    |--------------------------------------------------------------------------
    |
    | This is the base domain used for tenant subdomains.
    | For example, if this is set to 'example.com', then tenant subdomains
    | will be 'tenant-name.example.com'.
    |
    */
    'domain' => env('APP_TENANT_DOMAIN', 'localhost'),

    /*
    |--------------------------------------------------------------------------
    | Database Prefix
    |--------------------------------------------------------------------------
    |
    | This prefix will be used when creating tenant-specific databases.
    | For example, if this is set to 'tenant_', then tenant databases
    | will be named 'tenant_1', 'tenant_2', etc.
    |
    */
    'database_prefix' => env('TENANT_DATABASE_PREFIX', 'tenant_'),

    /*
    |--------------------------------------------------------------------------
    | Default Theme
    |--------------------------------------------------------------------------
    |
    | The default theme ID to assign to new tenants.
    |
    */
    'default_theme_id' => env('TENANT_DEFAULT_THEME_ID', 1),

    /*
    |--------------------------------------------------------------------------
    | Default Plan
    |--------------------------------------------------------------------------
    |
    | The default plan ID to assign to new tenants.
    |
    */
    'default_plan_id' => env('TENANT_DEFAULT_PLAN_ID', 1),

    /*
    |--------------------------------------------------------------------------
    | Trial Period
    |--------------------------------------------------------------------------
    |
    | The number of days for the trial period for new tenants.
    |
    */
    'trial_days' => env('TENANT_TRIAL_DAYS', 14),

    /*
    |--------------------------------------------------------------------------
    | Tenant Settings
    |--------------------------------------------------------------------------
    |
    | Default settings for new tenants.
    |
    */
    'default_settings' => [
        'allow_registration' => true,
        'require_email_verification' => true,
        'allow_social_login' => true,
        'default_language' => 'en',
        'timezone' => 'UTC',
        'date_format' => 'Y-m-d',
        'time_format' => 'H:i',
        'currency' => 'USD',
        'max_file_size' => 10, // MB
        'allowed_file_types' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'zip'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Reserved Domains
    |--------------------------------------------------------------------------
    |
    | These domains cannot be used by tenants.
    |
    */
    'reserved_domains' => [
        'www',
        'admin',
        'api',
        'app',
        'mail',
        'smtp',
        'pop',
        'imap',
        'blog',
        'dev',
        'staging',
        'test',
        'demo',
        'support',
        'help',
        'docs',
        'documentation',
        'kb',
        'knowledgebase',
        'forum',
        'community',
        'status',
        'billing',
        'payment',
        'checkout',
        'cart',
        'store',
        'shop',
        'marketplace',
        'cdn',
        'assets',
        'static',
        'media',
        'images',
        'files',
        'uploads',
        'download',
        'downloads',
        'auth',
        'login',
        'register',
        'signup',
        'account',
        'profile',
        'dashboard',
        'admin',
        'administrator',
        'webmaster',
        'root',
        'system',
        'server',
        'host',
        'local',
        'localhost',
        'naxofy',
    ],
];
