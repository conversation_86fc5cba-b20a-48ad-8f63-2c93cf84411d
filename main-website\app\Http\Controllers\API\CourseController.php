<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Enrollment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class CourseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Course::query()->where('status', 'published');

        // Filter by tenant if provided
        if ($request->has('tenant_id')) {
            $query->where('tenant_id', $request->tenant_id);
        }

        // Filter by instructor if provided
        if ($request->has('instructor_id')) {
            $query->where('instructor_id', $request->instructor_id);
        }

        // Filter by category if provided
        if ($request->has('category_id')) {
            $query->whereHas('categories', function ($q) use ($request) {
                $q->where('categories.id', $request->category_id);
            });
        }

        // Filter by price range
        if ($request->has('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }
        if ($request->has('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        // Filter by level
        if ($request->has('level')) {
            $query->where('level', $request->level);
        }

        // Search by title or description
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Sort by
        $sortBy = $request->input('sort_by', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Paginate
        $perPage = $request->input('per_page', 10);
        $courses = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $courses,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'short_description' => 'nullable|string|max:255',
            'thumbnail' => 'nullable|string',
            'level' => 'nullable|string|in:beginner,intermediate,advanced',
            'price' => 'nullable|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0',
            'sale_ends_at' => 'nullable|date',
            'tenant_id' => 'required|exists:tenants,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        // Create slug from title
        $slug = Str::slug($request->title);
        $count = 1;

        // Ensure slug is unique
        while (Course::where('slug', $slug)->exists()) {
            $slug = Str::slug($request->title) . '-' . $count;
            $count++;
        }

        // Create course
        $course = Course::create(array_merge(
            $validator->validated(),
            [
                'slug' => $slug,
                'instructor_id' => auth()->id(),
                'status' => 'draft',
            ]
        ));

        return response()->json([
            'success' => true,
            'message' => 'Course created successfully',
            'data' => $course,
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $course = Course::with(['instructor', 'sections.lessons' => function ($query) {
            $query->where('is_published', true)->orderBy('sort_order');
        }])->findOrFail($id);

        // Check if the course is published or the user is the instructor
        if ($course->status !== 'published' && 
            (!auth()->check() || auth()->id() !== $course->instructor_id)) {
            return response()->json([
                'success' => false,
                'message' => 'Course not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $course,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $course = Course::findOrFail($id);

        // Check if the user is the instructor of the course
        if (auth()->id() !== $course->instructor_id) {
            return response()->json([
                'success' => false,
                'message' => 'You are not authorized to update this course',
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|string|max:255',
            'description' => 'nullable|string',
            'short_description' => 'nullable|string|max:255',
            'thumbnail' => 'nullable|string',
            'level' => 'nullable|string|in:beginner,intermediate,advanced',
            'status' => 'nullable|string|in:draft,published,archived',
            'price' => 'nullable|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0',
            'sale_ends_at' => 'nullable|date',
            'is_featured' => 'nullable|boolean',
            'duration' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        // Update slug if title is changed
        if ($request->has('title') && $request->title !== $course->title) {
            $slug = Str::slug($request->title);
            $count = 1;

            // Ensure slug is unique
            while (Course::where('slug', $slug)->where('id', '!=', $id)->exists()) {
                $slug = Str::slug($request->title) . '-' . $count;
                $count++;
            }

            $course->slug = $slug;
        }

        $course->update($validator->validated());

        return response()->json([
            'success' => true,
            'message' => 'Course updated successfully',
            'data' => $course,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $course = Course::findOrFail($id);

        // Check if the user is the instructor of the course
        if (auth()->id() !== $course->instructor_id) {
            return response()->json([
                'success' => false,
                'message' => 'You are not authorized to delete this course',
            ], 403);
        }

        $course->delete();

        return response()->json([
            'success' => true,
            'message' => 'Course deleted successfully',
        ]);
    }

    /**
     * Enroll the authenticated user in the course.
     */
    public function enroll(Request $request, string $id)
    {
        $course = Course::findOrFail($id);

        // Check if the course is published
        if ($course->status !== 'published') {
            return response()->json([
                'success' => false,
                'message' => 'Course is not available for enrollment',
            ], 400);
        }

        // Check if the user is already enrolled
        $existingEnrollment = Enrollment::where('user_id', auth()->id())
            ->where('course_id', $course->id)
            ->first();

        if ($existingEnrollment) {
            return response()->json([
                'success' => false,
                'message' => 'You are already enrolled in this course',
            ], 400);
        }

        // Create enrollment
        $enrollment = Enrollment::create([
            'user_id' => auth()->id(),
            'course_id' => $course->id,
            'progress' => 0,
            'price_paid' => $course->current_price,
            'payment_method' => $request->input('payment_method', 'free'),
            'payment_id' => $request->input('payment_id'),
            'status' => 'active',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Enrolled in course successfully',
            'data' => $enrollment,
        ], 201);
    }

    /**
     * Update the progress of the authenticated user in the course.
     */
    public function updateProgress(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'progress' => 'required|integer|min:0|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $enrollment = Enrollment::where('user_id', auth()->id())
            ->where('course_id', $id)
            ->firstOrFail();

        $enrollment->updateProgress($request->progress);

        return response()->json([
            'success' => true,
            'message' => 'Progress updated successfully',
            'data' => $enrollment,
        ]);
    }

    /**
     * Get courses for a specific tenant.
     */
    public function tenantCourses(Request $request)
    {
        $domain = $request->route('domain');
        $tenant = \App\Models\Tenant::where('domain', $domain)->firstOrFail();

        $courses = Course::where('tenant_id', $tenant->id)
            ->where('status', 'published')
            ->paginate(10);

        return response()->json([
            'success' => true,
            'data' => $courses,
        ]);
    }
}
