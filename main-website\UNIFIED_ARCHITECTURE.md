# Unified Architecture for Naxofy LMS Platform

This document outlines the unified architecture for the Naxofy LMS platform, which consolidates the previously separate main-website and backend Laravel applications into a single, cohesive application.

## Architecture Overview

The unified architecture follows a modular approach with clear separation of concerns:

1. **Marketing Website**: Public-facing website for potential tenants
2. **Admin Dashboard**: System administration for platform managers
3. **Tenant Dashboard**: Management interface for tenant administrators
4. **API**: RESTful API endpoints for frontend and mobile app

## Directory Structure

```
main-website/
├── app/
│   ├── Http/
│   │   ├── Controllers/
│   │   │   ├── API/           # API controllers
│   │   │   ├── Admin/         # Admin dashboard controllers
│   │   │   ├── Marketing/     # Marketing website controllers
│   │   │   └── Tenant/        # Tenant dashboard controllers
│   │   ├── Middleware/        # Application middleware
│   │   └── Requests/          # Form requests
│   ├── Models/                # Eloquent models
│   ├── Services/              # Business logic services
│   └── Providers/             # Service providers
├── config/                    # Configuration files
├── database/
│   ├── migrations/            # Database migrations
│   ├── factories/             # Model factories
│   └── seeders/               # Database seeders
├── resources/
│   ├── views/
│   │   ├── marketing/         # Marketing website views
│   │   ├── admin/             # Admin dashboard views
│   │   └── tenant/            # Tenant dashboard views
│   ├── js/                    # JavaScript assets
│   └── css/                   # CSS assets
└── routes/
    ├── web.php                # Marketing website routes
    ├── api.php                # API routes
    ├── admin.php              # Admin dashboard routes
    └── tenant.php             # Tenant dashboard routes
```

## Key Components

### 1. Routing

The application uses separate route files for different components:

- `web.php`: Marketing website routes
- `api.php`: API endpoints for frontend and mobile app
- `admin.php`: Admin dashboard routes
- `tenant.php`: Tenant dashboard routes

### 2. Authentication

The application uses a dual authentication system:

- **Session-based Authentication**: For web routes (marketing, admin, tenant)
- **JWT Authentication**: For API routes (frontend, mobile app)
- **OAuth Integration**: Google authentication support

### 3. Multi-tenancy

The application implements multi-tenancy through:

- **Subdomain Routing**: Tenant-specific subdomains (e.g., `tenant.naxofy.com`)
- **Path-based Routing**: Alternative access via path (e.g., `/tenant/{domain}`)
- **Data Isolation**: Tenant-specific data filtering

### 4. Database Schema

The unified database schema includes these key entities:

- **Users**: All system users with role-based access
- **Tenants**: Organizations using the platform
- **Courses**: Educational content created by tenants
- **Lessons**: Individual units of course content
- **Sections**: Course sections containing lessons
- **Enrollments**: Student enrollment in courses
- **Themes**: Visual themes for tenant websites
- **Plans**: Subscription plans for tenants

## Benefits of Unified Architecture

1. **Single Source of Truth**: Unified database schema eliminates data synchronization issues
2. **Consistent Authentication**: Single authentication system across all components
3. **Simplified Development**: One codebase to maintain instead of two
4. **Clear Separation of Concerns**: Proper namespacing for marketing, admin, API, and tenant components
5. **Improved Scalability**: Easier to scale a single application than multiple ones
6. **Reduced Complexity**: Simplified deployment, maintenance, and updates

## Implementation Steps

The unified architecture has been implemented through these steps:

1. **Directory Structure**: Created namespaced directories for different components
2. **Route Organization**: Separated routes into dedicated files
3. **Authentication**: Implemented JWT for API and session for web
4. **Models**: Updated models with proper relationships
5. **Controllers**: Organized controllers by component
6. **Middleware**: Added role-based access control
7. **Configuration**: Updated configuration for multi-tenancy

## Frontend Integration

The React frontend and React Native mobile app integrate with the unified backend through:

1. **API Endpoints**: Consistent API endpoints for all functionality
2. **JWT Authentication**: Secure authentication for API access
3. **Tenant Subdomains**: Support for tenant-specific subdomains

## Running the Application

1. **Install Dependencies**:
   ```bash
   composer install
   npm install
   ```

2. **Configure Environment**:
   ```bash
   cp .env.example .env
   php artisan key:generate
   php artisan jwt:secret
   ```

3. **Run Migrations**:
   ```bash
   php artisan migrate --seed
   ```

4. **Start the Server**:
   ```bash
   php artisan serve
   ```

## Next Steps

1. **Data Migration**: Migrate data from the old separate databases
2. **Testing**: Comprehensive testing of all components
3. **Documentation**: Update API documentation
4. **Deployment**: Deploy the unified application
