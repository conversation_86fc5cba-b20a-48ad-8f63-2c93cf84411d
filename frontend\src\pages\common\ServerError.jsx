import React from 'react';
import { Link as RouterLink } from 'react-router-dom';
import {
  Container,
  Box,
  Typography,
  Button,
  Paper
} from '@mui/material';
import ErrorIcon from '@mui/icons-material/Error';

const ServerError = () => {
  return (
    <Container maxWidth="md">
      <Box sx={{ mt: 8, mb: 4 }}>
        <Paper elevation={3} sx={{ p: 4, borderRadius: 2, textAlign: 'center' }}>
          <ErrorIcon sx={{ fontSize: 100, color: 'error.main', mb: 2 }} />
          <Typography variant="h1" component="h1" gutterBottom>
            500
          </Typography>
          <Typography variant="h4" component="h2" gutterBottom>
            Server Error
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Sorry, something went wrong on our server. We're working to fix the issue. Please try again later.
          </Typography>
          <Button
            variant="contained"
            component={RouterLink}
            to="/"
            size="large"
            sx={{ mt: 2 }}
          >
            Go to Homepage
          </Button>
        </Paper>
      </Box>
    </Container>
  );
};

export default ServerError;
