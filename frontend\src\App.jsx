import { Routes, Route } from 'react-router-dom';
import { Box } from '@mui/material';

// Pages
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import Dashboard from './pages/Dashboard';
import CourseDetails from './pages/CourseDetails';
import CoursePlayer from './pages/CoursePlayer';
import TenantRegister from './pages/TenantRegister';
import TenantDashboard from './pages/TenantDashboard';
import AdminDashboard from './pages/AdminDashboard';
import ThemeManagement from './pages/ThemeManagement';
import ModuleManagement from './pages/ModuleManagement';
import Marketplace from './pages/Marketplace';
import MarketplaceItemDetail from './pages/MarketplaceItemDetail';

// Components
import GoogleCallback from './components/GoogleCallback';

// Components
import Header from './components/Header';
import Footer from './components/Footer';
import ProtectedRoute from './components/ProtectedRoute';



function App() {
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      <Header />
      <Box component="main" sx={{ flexGrow: 1 }}>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/tenant/register" element={<TenantRegister />} />
          <Route path="/auth/google/callback" element={<GoogleCallback />} />
          <Route path="/course/:id" element={<CourseDetails />} />

          {/* Protected Routes */}
          <Route path="/dashboard" element={
            <ProtectedRoute>
              <Dashboard />
            </ProtectedRoute>
          } />
          <Route path="/course/:id/learn" element={
            <ProtectedRoute>
              <CoursePlayer />
            </ProtectedRoute>
          } />
          <Route path="/tenant/dashboard/*" element={
            <ProtectedRoute role="tenant">
              <TenantDashboard />
            </ProtectedRoute>
          } />
          <Route path="/admin/dashboard/*" element={
            <ProtectedRoute role="admin">
              <AdminDashboard />
            </ProtectedRoute>
          } />
          <Route path="/themes" element={
            <ProtectedRoute role={['admin', 'tenant']}>
              <ThemeManagement />
            </ProtectedRoute>
          } />
          <Route path="/modules" element={
            <ProtectedRoute role={['admin', 'tenant']}>
              <ModuleManagement />
            </ProtectedRoute>
          } />
          <Route path="/marketplace" element={<Marketplace />} />
          <Route path="/marketplace/:type/:id" element={<MarketplaceItemDetail />} />
        </Routes>
      </Box>
      <Footer />
    </Box>
  );
}

export default App
