<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Plan;
use App\Models\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class PlanController extends Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        // Check if user is admin for all methods
        $this->middleware(function ($request, $next) {
            if (!Auth::check() || !Auth::user()->isAdmin()) {
                return redirect()->route('login')->with('error', 'You do not have permission to access this area.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of the plans.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get plans from database or use mock data if table doesn't exist yet
        try {
            $plans = Plan::withCount('tenants')->get();
            
            if ($plans->isEmpty()) {
                $plans = $this->getMockPlans();
            }
        } catch (\Exception $e) {
            $plans = $this->getMockPlans();
        }

        return view('admin.plans.index', compact('plans'));
    }

    /**
     * Show the form for creating a new plan.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('admin.plans.create');
    }

    /**
     * Store a newly created plan in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:plans,name',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'interval' => 'required|in:monthly,yearly,lifetime',
            'features' => 'required|array',
            'features.*' => 'required|string',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'trial_days' => 'nullable|integer|min:0',
            'max_courses' => 'nullable|integer|min:0',
            'max_students' => 'nullable|integer|min:0',
            'max_storage' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $plan = Plan::create([
                'name' => $request->name,
                'description' => $request->description,
                'price' => $request->price,
                'interval' => $request->interval,
                'features' => $request->features,
                'is_active' => $request->has('is_active'),
                'is_featured' => $request->has('is_featured'),
                'trial_days' => $request->trial_days,
                'max_courses' => $request->max_courses,
                'max_students' => $request->max_students,
                'max_storage' => $request->max_storage,
            ]);

            return redirect()->route('admin.plans.index')
                ->with('success', 'Plan created successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to create plan: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified plan.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        try {
            $plan = Plan::with('tenants')->findOrFail($id);
        } catch (\Exception $e) {
            // Use mock data if plan not found
            $plan = collect($this->getMockPlans())->firstWhere('id', $id);
            
            if (!$plan) {
                return redirect()->route('admin.plans.index')
                    ->with('error', 'Plan not found.');
            }
        }

        return view('admin.plans.show', compact('plan'));
    }

    /**
     * Show the form for editing the specified plan.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        try {
            $plan = Plan::findOrFail($id);
        } catch (\Exception $e) {
            // Use mock data if plan not found
            $plan = collect($this->getMockPlans())->firstWhere('id', $id);
            
            if (!$plan) {
                return redirect()->route('admin.plans.index')
                    ->with('error', 'Plan not found.');
            }
        }

        return view('admin.plans.edit', compact('plan'));
    }

    /**
     * Update the specified plan in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:plans,name,' . $id,
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'interval' => 'required|in:monthly,yearly,lifetime',
            'features' => 'required|array',
            'features.*' => 'required|string',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'trial_days' => 'nullable|integer|min:0',
            'max_courses' => 'nullable|integer|min:0',
            'max_students' => 'nullable|integer|min:0',
            'max_storage' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $plan = Plan::findOrFail($id);
            
            $plan->update([
                'name' => $request->name,
                'description' => $request->description,
                'price' => $request->price,
                'interval' => $request->interval,
                'features' => $request->features,
                'is_active' => $request->has('is_active'),
                'is_featured' => $request->has('is_featured'),
                'trial_days' => $request->trial_days,
                'max_courses' => $request->max_courses,
                'max_students' => $request->max_students,
                'max_storage' => $request->max_storage,
            ]);

            return redirect()->route('admin.plans.show', $plan->id)
                ->with('success', 'Plan updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update plan: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified plan from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        try {
            $plan = Plan::findOrFail($id);
            
            // Check if plan has tenants
            if ($plan->tenants()->count() > 0) {
                return redirect()->back()
                    ->with('error', 'Cannot delete plan with active tenants.');
            }
            
            $plan->delete();
            
            return redirect()->route('admin.plans.index')
                ->with('success', 'Plan deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to delete plan: ' . $e->getMessage());
        }
    }

    /**
     * Get mock plans data.
     *
     * @return \Illuminate\Support\Collection
     */
    private function getMockPlans()
    {
        return collect([
            (object) [
                'id' => 1,
                'name' => 'Basic',
                'price' => 9.99,
                'interval' => 'monthly',
                'features' => ['5 courses', '100 students', '1 GB storage', 'Email support'],
                'is_active' => true,
                'is_featured' => false,
                'tenants_count' => 25,
                'trial_days' => 14,
                'max_courses' => 5,
                'max_students' => 100,
                'max_storage' => 1,
                'description' => 'Perfect for individuals or small organizations just getting started with online learning.',
            ],
            (object) [
                'id' => 2,
                'name' => 'Standard',
                'price' => 29.99,
                'interval' => 'monthly',
                'features' => ['20 courses', '500 students', '5 GB storage', 'Priority support', 'Custom domain'],
                'is_active' => true,
                'is_featured' => true,
                'tenants_count' => 42,
                'trial_days' => 14,
                'max_courses' => 20,
                'max_students' => 500,
                'max_storage' => 5,
                'description' => 'Ideal for growing organizations with a moderate number of courses and students.',
            ],
            (object) [
                'id' => 3,
                'name' => 'Premium',
                'price' => 99.99,
                'interval' => 'monthly',
                'features' => ['Unlimited courses', 'Unlimited students', '20 GB storage', 'Premium support', 'Custom domain', 'White label', 'API access'],
                'is_active' => true,
                'is_featured' => false,
                'tenants_count' => 18,
                'trial_days' => 7,
                'max_courses' => null,
                'max_students' => null,
                'max_storage' => 20,
                'description' => 'For established educational institutions and businesses with extensive online learning needs.',
            ],
        ]);
    }
}
