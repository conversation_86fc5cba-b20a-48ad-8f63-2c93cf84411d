@extends('admin.layouts.app')

@section('title', 'Analytics Dashboard')

@section('styles')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.css">
<style>
    .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
    }
</style>
@endsection

@section('content')
<div class="container mx-auto px-6 py-8">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h3 class="text-3xl font-medium text-gray-700">Analytics Dashboard</h3>
            <p class="mt-1 text-sm text-gray-500">Overview of your platform's performance</p>
        </div>
        <div class="mt-4 md:mt-0 flex space-x-3">
            <a href="{{ route('admin.analytics.export') }}" class="bg-primary hover:bg-primary-dark text-white font-bold py-2 px-4 rounded">
                <i class="fas fa-download mr-2"></i> Export Data
            </a>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-primary rounded-md p-3">
                        <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">
                                Total Tenants
                            </dt>
                            <dd>
                                <div class="text-lg font-medium text-gray-900">
                                    {{ $data['tenant_count'] }}
                                </div>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-4 sm:px-6">
                <div class="text-sm">
                    <a href="{{ route('admin.analytics.tenants') }}" class="font-medium text-primary hover:text-primary-dark">
                        View all<span class="sr-only"> tenants</span>
                    </a>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-blue-500 rounded-md p-3">
                        <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">
                                Total Users
                            </dt>
                            <dd>
                                <div class="text-lg font-medium text-gray-900">
                                    {{ $data['user_count'] }}
                                </div>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-4 sm:px-6">
                <div class="text-sm">
                    <a href="{{ route('admin.analytics.users') }}" class="font-medium text-primary hover:text-primary-dark">
                        View all<span class="sr-only"> users</span>
                    </a>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-green-500 rounded-md p-3">
                        <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">
                                Active Tenants
                            </dt>
                            <dd>
                                <div class="text-lg font-medium text-gray-900">
                                    {{ $data['active_tenants_count'] }}
                                </div>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-4 sm:px-6">
                <div class="text-sm">
                    <a href="{{ route('admin.tenants.index') }}" class="font-medium text-primary hover:text-primary-dark">
                        View details
                    </a>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-yellow-500 rounded-md p-3">
                        <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">
                                Pending Tenants
                            </dt>
                            <dd>
                                <div class="text-lg font-medium text-gray-900">
                                    {{ $data['pending_tenants_count'] }}
                                </div>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-4 sm:px-6">
                <div class="text-sm">
                    <a href="{{ route('admin.tenants.index') }}" class="font-medium text-primary hover:text-primary-dark">
                        View details
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts -->
    <div class="mt-8 grid grid-cols-1 gap-5 lg:grid-cols-2">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">New Tenants</h3>
                <div class="mt-2 chart-container">
                    <canvas id="newTenantsChart"></canvas>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">New Users</h3>
                <div class="mt-2 chart-container">
                    <canvas id="newUsersChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Distribution Charts -->
    <div class="mt-8 grid grid-cols-1 gap-5 lg:grid-cols-2">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Tenants by Plan</h3>
                <div class="mt-2 chart-container">
                    <canvas id="tenantsByPlanChart"></canvas>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Users by Role</h3>
                <div class="mt-2 chart-container">
                    <canvas id="usersByRoleChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        
        // New Tenants Chart
        const newTenantsCtx = document.getElementById('newTenantsChart').getContext('2d');
        const newTenantsChart = new Chart(newTenantsCtx, {
            type: 'line',
            data: {
                labels: months,
                datasets: [{
                    label: 'New Tenants',
                    data: [
                        @foreach($data['new_tenants_by_month'] as $month => $count)
                            {{ $count }},
                        @endforeach
                    ],
                    backgroundColor: 'rgba(255, 119, 0, 0.2)',
                    borderColor: 'rgba(255, 119, 0, 1)',
                    borderWidth: 2,
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });
        
        // New Users Chart
        const newUsersCtx = document.getElementById('newUsersChart').getContext('2d');
        const newUsersChart = new Chart(newUsersCtx, {
            type: 'line',
            data: {
                labels: months,
                datasets: [{
                    label: 'New Users',
                    data: [
                        @foreach($data['new_users_by_month'] as $month => $count)
                            {{ $count }},
                        @endforeach
                    ],
                    backgroundColor: 'rgba(59, 130, 246, 0.2)',
                    borderColor: 'rgba(59, 130, 246, 1)',
                    borderWidth: 2,
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });
        
        // Tenants by Plan Chart
        const tenantsByPlanCtx = document.getElementById('tenantsByPlanChart').getContext('2d');
        const tenantsByPlanChart = new Chart(tenantsByPlanCtx, {
            type: 'doughnut',
            data: {
                labels: [
                    @foreach($data['tenants_by_plan'] as $plan => $count)
                        '{{ $plan }}',
                    @endforeach
                ],
                datasets: [{
                    data: [
                        @foreach($data['tenants_by_plan'] as $plan => $count)
                            {{ $count }},
                        @endforeach
                    ],
                    backgroundColor: [
                        'rgba(255, 119, 0, 0.8)',
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(16, 185, 129, 0.8)',
                        'rgba(245, 158, 11, 0.8)',
                        'rgba(139, 92, 246, 0.8)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        });
        
        // Users by Role Chart
        const usersByRoleCtx = document.getElementById('usersByRoleChart').getContext('2d');
        const usersByRoleChart = new Chart(usersByRoleCtx, {
            type: 'doughnut',
            data: {
                labels: [
                    @foreach($data['users_by_role'] as $role => $count)
                        '{{ ucfirst($role) }}',
                    @endforeach
                ],
                datasets: [{
                    data: [
                        @foreach($data['users_by_role'] as $role => $count)
                            {{ $count }},
                        @endforeach
                    ],
                    backgroundColor: [
                        'rgba(139, 92, 246, 0.8)',
                        'rgba(255, 119, 0, 0.8)',
                        'rgba(16, 185, 129, 0.8)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        });
    });
</script>
@endsection
