import { useState, useEffect, useContext } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Grid, 
  Card, 
  CardContent, 
  CardMedia, 
  CardActions, 
  Button, 
  Chip, 
  Dialog, 
  DialogActions, 
  DialogContent, 
  DialogContentText, 
  DialogTitle, 
  TextField, 
  CircularProgress, 
  Alert, 
  Divider,
  Paper,
  Tabs,
  Tab,
  IconButton,
  Tooltip
} from '@mui/material';
import { styled } from '@mui/material/styles';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import { themeService } from '../services/api';
import { AuthContext } from '../context/AuthContext';

const StyledCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: '0 12px 20px rgba(0, 0, 0, 0.1)',
  },
  position: 'relative',
  overflow: 'hidden',
  borderRadius: theme.shape.borderRadius * 2,
}));

const ThemeManagement = () => {
  const { user } = useContext(AuthContext);
  const [themes, setThemes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [themeFile, setThemeFile] = useState(null);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [uploadError, setUploadError] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedTheme, setSelectedTheme] = useState(null);

  useEffect(() => {
    fetchThemes();
  }, []);

  const fetchThemes = async () => {
    try {
      setLoading(true);
      const params = {};
      
      // If user is a tenant, only fetch their themes and global themes
      if (user.role === 'tenant') {
        params.tenant_id = user.tenant_id;
      }
      
      const response = await themeService.getAllThemes(params);
      setThemes(response.data.data);
      setError(null);
    } catch (error) {
      console.error('Error fetching themes:', error);
      setError('Failed to load themes. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleUploadDialogOpen = () => {
    setUploadDialogOpen(true);
    setThemeFile(null);
    setUploadError(null);
  };

  const handleUploadDialogClose = () => {
    setUploadDialogOpen(false);
  };

  const handleFileChange = (event) => {
    setThemeFile(event.target.files[0]);
  };

  const handleUploadTheme = async () => {
    if (!themeFile) {
      setUploadError('Please select a theme file to upload.');
      return;
    }

    try {
      setUploadLoading(true);
      setUploadError(null);

      const formData = new FormData();
      formData.append('theme_file', themeFile);
      
      // If user is a tenant, add tenant_id
      if (user.role === 'tenant') {
        formData.append('tenant_id', user.tenant_id);
      }

      // In a real implementation, you would have an endpoint to upload and install the theme
      // For now, we'll simulate it with a timeout
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Refresh the themes list
      await fetchThemes();
      
      setUploadDialogOpen(false);
    } catch (error) {
      console.error('Error uploading theme:', error);
      setUploadError('Failed to upload theme. Please try again later.');
    } finally {
      setUploadLoading(false);
    }
  };

  const handleDeleteDialogOpen = (theme) => {
    setSelectedTheme(theme);
    setDeleteDialogOpen(true);
  };

  const handleDeleteDialogClose = () => {
    setDeleteDialogOpen(false);
    setSelectedTheme(null);
  };

  const handleDeleteTheme = async () => {
    if (!selectedTheme) return;

    try {
      await themeService.deleteTheme(selectedTheme.id);
      
      // Update the themes list
      setThemes(themes.filter(theme => theme.id !== selectedTheme.id));
      
      setDeleteDialogOpen(false);
      setSelectedTheme(null);
    } catch (error) {
      console.error('Error deleting theme:', error);
      setError('Failed to delete theme. Please try again later.');
    }
  };

  const handleActivateTheme = async (theme) => {
    try {
      await themeService.activateTheme(theme.id);
      
      // Update the themes list
      setThemes(themes.map(t => ({
        ...t,
        is_active: t.id === theme.id ? true : (t.tenant_id === theme.tenant_id ? false : t.is_active)
      })));
    } catch (error) {
      console.error('Error activating theme:', error);
      setError('Failed to activate theme. Please try again later.');
    }
  };

  const filteredThemes = themes.filter(theme => {
    if (tabValue === 0) return true; // All themes
    if (tabValue === 1) return theme.tenant_id === null; // Global themes
    if (tabValue === 2) return theme.tenant_id !== null; // Tenant themes
    return true;
  });

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Theme Management
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<UploadFileIcon />}
          onClick={handleUploadDialogOpen}
        >
          Upload Theme
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ mb: 4 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          centered
        >
          <Tab label="All Themes" />
          <Tab label="Global Themes" />
          <Tab label="Tenant Themes" />
        </Tabs>
      </Paper>

      {filteredThemes.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            No themes found
          </Typography>
          <Typography variant="body1">
            {tabValue === 0 ? 'There are no themes installed.' : 
             tabValue === 1 ? 'There are no global themes installed.' : 
             'There are no tenant-specific themes installed.'}
          </Typography>
        </Paper>
      ) : (
        <Grid container spacing={4}>
          {filteredThemes.map((theme) => (
            <Grid item key={theme.id} xs={12} sm={6} md={4} lg={3}>
              <StyledCard>
                {theme.is_active && (
                  <Chip
                    label="Active"
                    color="primary"
                    size="small"
                    sx={{
                      position: 'absolute',
                      top: 16,
                      right: 16,
                      zIndex: 1,
                    }}
                  />
                )}
                <CardMedia
                  component="img"
                  height="180"
                  image={theme.thumbnail || `https://source.unsplash.com/random?design&sig=${theme.id}`}
                  alt={theme.name}
                />
                <CardContent sx={{ flexGrow: 1 }}>
                  <Typography variant="h6" component="div" gutterBottom>
                    {theme.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Version: {theme.version}
                  </Typography>
                  {theme.author && (
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      By: {theme.author}
                    </Typography>
                  )}
                  <Divider sx={{ my: 1.5 }} />
                  <Typography variant="body2" color="text.secondary">
                    {theme.description || 'No description available.'}
                  </Typography>
                </CardContent>
                <CardActions sx={{ p: 2, pt: 0 }}>
                  {!theme.is_active && (
                    <Button
                      size="small"
                      variant="contained"
                      color="primary"
                      onClick={() => handleActivateTheme(theme)}
                      startIcon={<CheckCircleIcon />}
                    >
                      Activate
                    </Button>
                  )}
                  <Box sx={{ ml: 'auto', display: 'flex', gap: 1 }}>
                    <Tooltip title="Edit">
                      <IconButton size="small" color="primary">
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    {!theme.is_active && !theme.is_system && (
                      <Tooltip title="Delete">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteDialogOpen(theme)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                  </Box>
                </CardActions>
              </StyledCard>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Upload Theme Dialog */}
      <Dialog open={uploadDialogOpen} onClose={handleUploadDialogClose}>
        <DialogTitle>Upload Theme</DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ mb: 2 }}>
            Upload a theme package (.zip) to install it. The theme package should contain a theme.json file with metadata and all necessary assets.
          </DialogContentText>
          {uploadError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {uploadError}
            </Alert>
          )}
          <Button
            variant="outlined"
            component="label"
            startIcon={<UploadFileIcon />}
            sx={{ mb: 2 }}
          >
            Select Theme File
            <input
              type="file"
              accept=".zip"
              hidden
              onChange={handleFileChange}
            />
          </Button>
          {themeFile && (
            <Typography variant="body2" sx={{ mt: 1 }}>
              Selected file: {themeFile.name}
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleUploadDialogClose} disabled={uploadLoading}>
            Cancel
          </Button>
          <Button
            onClick={handleUploadTheme}
            variant="contained"
            color="primary"
            disabled={uploadLoading || !themeFile}
            startIcon={uploadLoading ? <CircularProgress size={20} /> : null}
          >
            {uploadLoading ? 'Uploading...' : 'Upload'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Theme Dialog */}
      <Dialog open={deleteDialogOpen} onClose={handleDeleteDialogClose}>
        <DialogTitle>Delete Theme</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the theme "{selectedTheme?.name}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteDialogClose}>
            Cancel
          </Button>
          <Button
            onClick={handleDeleteTheme}
            variant="contained"
            color="error"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default ThemeManagement;
