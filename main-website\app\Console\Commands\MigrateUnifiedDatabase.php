<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Artisan;

class MigrateUnifiedDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:migrate-unified';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate data from old databases to the unified database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting unified database migration...');

        // Step 1: Create the unified database if it doesn't exist
        $this->createUnifiedDatabase();

        // Step 2: Run migrations on the unified database
        $this->runMigrations();

        // Step 3: Migrate data from old databases
        $this->migrateData();

        $this->info('Unified database migration completed successfully!');

        return Command::SUCCESS;
    }

    /**
     * Create the unified database if it doesn't exist.
     */
    protected function createUnifiedDatabase()
    {
        $this->info('Creating unified database if it doesn\'t exist...');

        $database = env('DB_DATABASE', 'lms_unified');

        try {
            $pdo = new \PDO(
                'mysql:host=' . env('DB_HOST', '127.0.0.1') . ';port=' . env('DB_PORT', '3306'),
                env('DB_USERNAME', 'root'),
                env('DB_PASSWORD', '')
            );

            $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$database}`");

            $this->info("Database '{$database}' created or already exists.");
        } catch (\Exception $e) {
            $this->error("Failed to create database: " . $e->getMessage());
            exit(1);
        }
    }

    /**
     * Run migrations on the unified database.
     */
    protected function runMigrations()
    {
        $this->info('Running migrations on the unified database...');

        try {
            Artisan::call('migrate:fresh', [
                '--force' => true,
            ]);

            $this->info('Migrations completed successfully.');
        } catch (\Exception $e) {
            $this->error("Failed to run migrations: " . $e->getMessage());
            exit(1);
        }
    }

    /**
     * Migrate data from old databases to the unified database.
     */
    protected function migrateData()
    {
        $this->info('Migrating data from old databases...');

        // Migrate users
        $this->migrateUsers();

        // Migrate tenants
        $this->migrateTenants();

        // Migrate plans
        $this->migratePlans();

        // Migrate themes
        $this->migrateThemes();

        // Migrate modules
        $this->migrateModules();

        // Migrate courses
        $this->migrateCourses();

        // Migrate lessons
        $this->migrateLessons();

        // Migrate sections
        $this->migrateSections();

        // Migrate enrollments
        $this->migrateEnrollments();

        // Migrate payments
        $this->migratePayments();

        // Migrate reviews
        $this->migrateReviews();

        $this->info('Data migration completed successfully.');
    }

    /**
     * Migrate users from old databases.
     */
    protected function migrateUsers()
    {
        $this->info('Migrating users...');

        // Get users from main database
        $mainUsers = DB::connection('mysql_main')->table('users')->get();

        // Get users from API database
        $apiUsers = DB::connection('mysql_api')->table('users')->get();

        // Merge users (avoid duplicates by email)
        $mergedUsers = collect();
        $emails = [];

        foreach ($mainUsers as $user) {
            $mergedUsers->push($user);
            $emails[] = $user->email;
        }

        foreach ($apiUsers as $user) {
            if (!in_array($user->email, $emails)) {
                $mergedUsers->push($user);
                $emails[] = $user->email;
            }
        }

        // Insert merged users into unified database
        foreach ($mergedUsers as $user) {
            // Convert to array and remove any database-specific fields
            $userData = (array) $user;
            unset($userData['id']);

            // Insert user
            DB::table('users')->insert($userData);
        }

        $this->info('Users migrated: ' . $mergedUsers->count());
    }

    /**
     * Migrate tenants from old databases.
     */
    protected function migrateTenants()
    {
        $this->info('Migrating tenants...');

        // Check if tenants table exists in main database
        if (Schema::connection('mysql_main')->hasTable('tenants')) {
            $mainTenants = DB::connection('mysql_main')->table('tenants')->get();

            foreach ($mainTenants as $tenant) {
                // Convert to array and remove any database-specific fields
                $tenantData = (array) $tenant;
                unset($tenantData['id']);

                // Insert tenant
                DB::table('tenants')->insert($tenantData);
            }

            $this->info('Tenants migrated from main database: ' . $mainTenants->count());
        }

        // Check if tenants table exists in API database
        if (Schema::connection('mysql_api')->hasTable('tenants')) {
            $apiTenants = DB::connection('mysql_api')->table('tenants')->get();

            foreach ($apiTenants as $tenant) {
                // Check if tenant already exists by domain
                $exists = DB::table('tenants')->where('domain', $tenant->domain)->exists();

                if (!$exists) {
                    // Convert to array and remove any database-specific fields
                    $tenantData = (array) $tenant;
                    unset($tenantData['id']);

                    // Insert tenant
                    DB::table('tenants')->insert($tenantData);
                }
            }

            $this->info('Tenants migrated from API database: ' . $apiTenants->count());
        }
    }

    /**
     * Migrate plans from old databases.
     */
    protected function migratePlans()
    {
        $this->info('Migrating plans...');

        // Check if plans table exists in main database
        if (Schema::connection('mysql_main')->hasTable('plans')) {
            $mainPlans = DB::connection('mysql_main')->table('plans')->get();

            foreach ($mainPlans as $plan) {
                // Convert to array and remove any database-specific fields
                $planData = (array) $plan;
                unset($planData['id']);

                // Insert plan
                DB::table('plans')->insert($planData);
            }

            $this->info('Plans migrated from main database: ' . $mainPlans->count());
        }

        // Check if plans table exists in API database
        if (Schema::connection('mysql_api')->hasTable('plans')) {
            $apiPlans = DB::connection('mysql_api')->table('plans')->get();

            foreach ($apiPlans as $plan) {
                // Check if plan already exists by name
                $exists = DB::table('plans')->where('name', $plan->name)->exists();

                if (!$exists) {
                    // Convert to array and remove any database-specific fields
                    $planData = (array) $plan;
                    unset($planData['id']);

                    // Insert plan
                    DB::table('plans')->insert($planData);
                }
            }

            $this->info('Plans migrated from API database: ' . $apiPlans->count());
        }
    }

    /**
     * Migrate themes from old databases.
     */
    protected function migrateThemes()
    {
        $this->info('Migrating themes...');

        // Check if themes table exists in main database
        if (Schema::connection('mysql_main')->hasTable('themes')) {
            $mainThemes = DB::connection('mysql_main')->table('themes')->get();

            foreach ($mainThemes as $theme) {
                // Convert to array and remove any database-specific fields
                $themeData = (array) $theme;
                unset($themeData['id']);

                // Insert theme
                DB::table('themes')->insert($themeData);
            }

            $this->info('Themes migrated from main database: ' . $mainThemes->count());
        }

        // Check if themes table exists in API database
        if (Schema::connection('mysql_api')->hasTable('themes')) {
            $apiThemes = DB::connection('mysql_api')->table('themes')->get();

            foreach ($apiThemes as $theme) {
                // Check if theme already exists by slug
                $exists = DB::table('themes')->where('slug', $theme->slug)->exists();

                if (!$exists) {
                    // Convert to array and remove any database-specific fields
                    $themeData = (array) $theme;
                    unset($themeData['id']);

                    // Insert theme
                    DB::table('themes')->insert($themeData);
                }
            }

            $this->info('Themes migrated from API database: ' . $apiThemes->count());
        }
    }

    /**
     * Migrate modules from old databases.
     */
    protected function migrateModules()
    {
        $this->info('Migrating modules...');

        // Check if modules table exists in main database
        if (Schema::connection('mysql_main')->hasTable('modules')) {
            $mainModules = DB::connection('mysql_main')->table('modules')->get();

            foreach ($mainModules as $module) {
                // Convert to array and remove any database-specific fields
                $moduleData = (array) $module;
                unset($moduleData['id']);

                // Insert module
                DB::table('modules')->insert($moduleData);
            }

            $this->info('Modules migrated from main database: ' . $mainModules->count());
        }

        // Check if modules table exists in API database
        if (Schema::connection('mysql_api')->hasTable('modules')) {
            $apiModules = DB::connection('mysql_api')->table('modules')->get();

            foreach ($apiModules as $module) {
                // Check if module already exists by slug
                $exists = DB::table('modules')->where('slug', $module->slug)->exists();

                if (!$exists) {
                    // Convert to array and remove any database-specific fields
                    $moduleData = (array) $module;
                    unset($moduleData['id']);

                    // Insert module
                    DB::table('modules')->insert($moduleData);
                }
            }

            $this->info('Modules migrated from API database: ' . $apiModules->count());
        }

        // Migrate module_tenant pivot table if it exists
        if (Schema::connection('mysql_api')->hasTable('module_tenant')) {
            $apiModuleTenant = DB::connection('mysql_api')->table('module_tenant')->get();

            foreach ($apiModuleTenant as $pivot) {
                // Get the new IDs for the module and tenant
                $moduleId = DB::table('modules')->where('slug', function($query) use ($pivot) {
                    $query->select('slug')
                        ->from('modules')
                        ->where('id', $pivot->module_id)
                        ->limit(1)
                        ->connection('mysql_api');
                })->value('id');

                $tenantId = DB::table('tenants')->where('domain', function($query) use ($pivot) {
                    $query->select('domain')
                        ->from('tenants')
                        ->where('id', $pivot->tenant_id)
                        ->limit(1)
                        ->connection('mysql_api');
                })->value('id');

                if ($moduleId && $tenantId) {
                    // Convert to array and remove any database-specific fields
                    $pivotData = (array) $pivot;
                    $pivotData['module_id'] = $moduleId;
                    $pivotData['tenant_id'] = $tenantId;
                    unset($pivotData['id']);

                    // Insert pivot
                    DB::table('module_tenant')->insert($pivotData);
                }
            }

            $this->info('Module-tenant relationships migrated: ' . $apiModuleTenant->count());
        }
    }

    /**
     * Migrate courses from old databases.
     */
    protected function migrateCourses()
    {
        $this->info('Migrating courses...');

        // Check if courses table exists in API database
        if (Schema::connection('mysql_api')->hasTable('courses')) {
            $apiCourses = DB::connection('mysql_api')->table('courses')->get();

            foreach ($apiCourses as $course) {
                // Get the new tenant ID
                $tenantId = DB::table('tenants')->where('domain', function($query) use ($course) {
                    $query->select('domain')
                        ->from('tenants')
                        ->where('id', $course->tenant_id)
                        ->limit(1)
                        ->connection('mysql_api');
                })->value('id');

                // Get the new instructor ID
                $instructorId = DB::table('users')->where('email', function($query) use ($course) {
                    $query->select('email')
                        ->from('users')
                        ->where('id', $course->instructor_id)
                        ->limit(1)
                        ->connection('mysql_api');
                })->value('id');

                if ($tenantId && $instructorId) {
                    // Convert to array and remove any database-specific fields
                    $courseData = (array) $course;
                    $courseData['tenant_id'] = $tenantId;
                    $courseData['instructor_id'] = $instructorId;
                    unset($courseData['id']);

                    // Insert course
                    DB::table('courses')->insert($courseData);
                }
            }

            $this->info('Courses migrated: ' . $apiCourses->count());
        }
    }

    /**
     * Migrate sections from old databases.
     */
    protected function migrateSections()
    {
        $this->info('Migrating sections...');

        // Check if sections table exists in API database
        if (Schema::connection('mysql_api')->hasTable('sections')) {
            $apiSections = DB::connection('mysql_api')->table('sections')->get();

            foreach ($apiSections as $section) {
                // Get the new course ID
                $courseId = DB::table('courses')->where('slug', function($query) use ($section) {
                    $query->select('slug')
                        ->from('courses')
                        ->where('id', $section->course_id)
                        ->limit(1)
                        ->connection('mysql_api');
                })->value('id');

                if ($courseId) {
                    // Convert to array and remove any database-specific fields
                    $sectionData = (array) $section;
                    $sectionData['course_id'] = $courseId;
                    unset($sectionData['id']);

                    // Insert section
                    DB::table('sections')->insert($sectionData);
                }
            }

            $this->info('Sections migrated: ' . $apiSections->count());
        }
    }

    /**
     * Migrate lessons from old databases.
     */
    protected function migrateLessons()
    {
        $this->info('Migrating lessons...');

        // Check if lessons table exists in API database
        if (Schema::connection('mysql_api')->hasTable('lessons')) {
            $apiLessons = DB::connection('mysql_api')->table('lessons')->get();

            foreach ($apiLessons as $lesson) {
                // Get the new course ID
                $courseId = DB::table('courses')->where('slug', function($query) use ($lesson) {
                    $query->select('slug')
                        ->from('courses')
                        ->where('id', $lesson->course_id)
                        ->limit(1)
                        ->connection('mysql_api');
                })->value('id');

                // Get the new section ID if applicable
                $sectionId = null;
                if ($lesson->section_id) {
                    $sectionId = DB::table('sections')->where('title', function($query) use ($lesson) {
                        $query->select('title')
                            ->from('sections')
                            ->where('id', $lesson->section_id)
                            ->limit(1)
                            ->connection('mysql_api');
                    })->where('course_id', $courseId)->value('id');
                }

                if ($courseId) {
                    // Convert to array and remove any database-specific fields
                    $lessonData = (array) $lesson;
                    $lessonData['course_id'] = $courseId;
                    $lessonData['section_id'] = $sectionId;
                    unset($lessonData['id']);

                    // Insert lesson
                    DB::table('lessons')->insert($lessonData);
                }
            }

            $this->info('Lessons migrated: ' . $apiLessons->count());
        }
    }

    /**
     * Migrate enrollments from old databases.
     */
    protected function migrateEnrollments()
    {
        $this->info('Migrating enrollments...');

        // Check if enrollments table exists in API database
        if (Schema::connection('mysql_api')->hasTable('enrollments')) {
            $apiEnrollments = DB::connection('mysql_api')->table('enrollments')->get();

            foreach ($apiEnrollments as $enrollment) {
                // Get the new user ID
                $userId = DB::table('users')->where('email', function($query) use ($enrollment) {
                    $query->select('email')
                        ->from('users')
                        ->where('id', $enrollment->user_id)
                        ->limit(1)
                        ->connection('mysql_api');
                })->value('id');

                // Get the new course ID
                $courseId = DB::table('courses')->where('slug', function($query) use ($enrollment) {
                    $query->select('slug')
                        ->from('courses')
                        ->where('id', $enrollment->course_id)
                        ->limit(1)
                        ->connection('mysql_api');
                })->value('id');

                if ($userId && $courseId) {
                    // Convert to array and remove any database-specific fields
                    $enrollmentData = (array) $enrollment;
                    $enrollmentData['user_id'] = $userId;
                    $enrollmentData['course_id'] = $courseId;
                    unset($enrollmentData['id']);

                    // Insert enrollment
                    DB::table('enrollments')->insert($enrollmentData);
                }
            }

            $this->info('Enrollments migrated: ' . $apiEnrollments->count());
        }
    }

    /**
     * Migrate payments from old databases.
     */
    protected function migratePayments()
    {
        $this->info('Migrating payments...');

        // Check if payments table exists in API database
        if (Schema::connection('mysql_api')->hasTable('payments')) {
            $apiPayments = DB::connection('mysql_api')->table('payments')->get();

            foreach ($apiPayments as $payment) {
                // Get the new user ID
                $userId = DB::table('users')->where('email', function($query) use ($payment) {
                    $query->select('email')
                        ->from('users')
                        ->where('id', $payment->user_id)
                        ->limit(1)
                        ->connection('mysql_api');
                })->value('id');

                // Get the new course ID if applicable
                $courseId = null;
                if (isset($payment->course_id)) {
                    $courseId = DB::table('courses')->where('slug', function($query) use ($payment) {
                        $query->select('slug')
                            ->from('courses')
                            ->where('id', $payment->course_id)
                            ->limit(1)
                            ->connection('mysql_api');
                    })->value('id');
                }

                // Get the new tenant ID if applicable
                $tenantId = null;
                if (isset($payment->tenant_id)) {
                    $tenantId = DB::table('tenants')->where('domain', function($query) use ($payment) {
                        $query->select('domain')
                            ->from('tenants')
                            ->where('id', $payment->tenant_id)
                            ->limit(1)
                            ->connection('mysql_api');
                    })->value('id');
                }

                if ($userId) {
                    // Convert to array and remove any database-specific fields
                    $paymentData = (array) $payment;
                    $paymentData['user_id'] = $userId;
                    if ($courseId) $paymentData['course_id'] = $courseId;
                    if ($tenantId) $paymentData['tenant_id'] = $tenantId;
                    unset($paymentData['id']);

                    // Insert payment
                    DB::table('payments')->insert($paymentData);
                }
            }

            $this->info('Payments migrated: ' . $apiPayments->count());
        }

        // Check if payments table exists in main database
        if (Schema::connection('mysql_main')->hasTable('payments')) {
            $mainPayments = DB::connection('mysql_main')->table('payments')->get();

            foreach ($mainPayments as $payment) {
                // Get the new tenant ID if applicable
                $tenantId = null;
                if (isset($payment->tenant_id)) {
                    $tenantId = DB::table('tenants')->where('domain', function($query) use ($payment) {
                        $query->select('domain')
                            ->from('tenants')
                            ->where('id', $payment->tenant_id)
                            ->limit(1)
                            ->connection('mysql_main');
                    })->value('id');
                }

                // Convert to array and remove any database-specific fields
                $paymentData = (array) $payment;
                if ($tenantId) $paymentData['tenant_id'] = $tenantId;
                unset($paymentData['id']);

                // Insert payment
                DB::table('payments')->insert($paymentData);
            }

            $this->info('Payments migrated from main database: ' . $mainPayments->count());
        }
    }

    /**
     * Migrate reviews from old databases.
     */
    protected function migrateReviews()
    {
        $this->info('Migrating reviews...');

        // Check if reviews table exists in API database
        if (Schema::connection('mysql_api')->hasTable('reviews')) {
            $apiReviews = DB::connection('mysql_api')->table('reviews')->get();

            foreach ($apiReviews as $review) {
                // Get the new user ID
                $userId = DB::table('users')->where('email', function($query) use ($review) {
                    $query->select('email')
                        ->from('users')
                        ->where('id', $review->user_id)
                        ->limit(1)
                        ->connection('mysql_api');
                })->value('id');

                if ($userId) {
                    // Convert to array and remove any database-specific fields
                    $reviewData = (array) $review;
                    $reviewData['user_id'] = $userId;
                    unset($reviewData['id']);

                    // Insert review
                    DB::table('reviews')->insert($reviewData);
                }
            }

            $this->info('Reviews migrated: ' . $apiReviews->count());
        }
    }
}
