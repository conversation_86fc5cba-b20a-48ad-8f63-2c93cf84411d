# Database Migration Guide

This guide explains how to migrate data from the separate `lms_main` and `lms_api` databases to the unified `lms_unified` database.

## Prerequisites

1. Make sure both the `lms_main` and `lms_api` databases exist and contain data.
2. Ensure you have proper database credentials in your `.env` file.

## Configuration

The `.env` file should contain the following database configuration:

```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=lms_unified
DB_USERNAME=root
DB_PASSWORD=

# Old database connections for migration
DB_MAIN_CONNECTION=mysql
DB_MAIN_HOST=127.0.0.1
DB_MAIN_PORT=3306
DB_MAIN_DATABASE=lms_main
DB_MAIN_USERNAME=root
DB_MAIN_PASSWORD=

DB_API_CONNECTION=mysql
DB_API_HOST=127.0.0.1
DB_API_PORT=3306
DB_API_DATABASE=lms_api
DB_API_USERNAME=root
DB_API_PASSWORD=
```

## Migration Process

The migration process consists of the following steps:

1. Create the unified database if it doesn't exist
2. Run migrations on the unified database
3. Migrate data from old databases to the unified database

### Running the Migration

To run the migration, execute the following command:

```bash
php artisan db:migrate-unified
```

This command will:

1. Create the `lms_unified` database if it doesn't exist
2. Run all migrations on the unified database
3. Migrate data from the old databases to the unified database

### Data Migration Details

The migration process migrates the following data:

- Users
- Tenants
- Plans
- Themes
- Modules
- Courses
- Sections
- Lessons
- Enrollments
- Payments
- Reviews

For each entity, the migration process:

1. Retrieves data from both old databases
2. Resolves any conflicts (e.g., duplicate emails for users)
3. Inserts the data into the unified database
4. Updates relationships between entities

## Troubleshooting

### Database Connection Issues

If you encounter database connection issues, check the following:

1. Make sure the database credentials in `.env` are correct
2. Ensure the databases exist
3. Verify that the MySQL server is running

### Migration Errors

If you encounter errors during migration, check the following:

1. Make sure all required tables exist in the old databases
2. Ensure the database schema is compatible with the migration
3. Check for any foreign key constraints that might be violated

### Data Integrity Issues

If you encounter data integrity issues after migration, check the following:

1. Make sure all relationships are properly migrated
2. Verify that all required data is present in the unified database
3. Check for any duplicate or missing records

## Post-Migration Steps

After the migration is complete, you should:

1. Verify that all data has been migrated correctly
2. Update your application to use the unified database
3. Run tests to ensure everything works as expected
4. Consider backing up the old databases before removing them

## Reverting the Migration

If you need to revert the migration, you can:

1. Drop the unified database
2. Continue using the old databases

```sql
DROP DATABASE lms_unified;
```

## Additional Notes

- The migration process is designed to be idempotent, meaning you can run it multiple times without duplicating data
- The migration process preserves relationships between entities
- The migration process handles conflicts by preferring data from the main database over the API database
