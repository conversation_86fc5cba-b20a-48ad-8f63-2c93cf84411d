import React, { useState, useEffect, useContext } from 'react';
import { FlatList, View, StyleSheet, Image, TextInput, TouchableOpacity, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

import Container from '@/components/ui/Container';
import Text from '@/components/ui/Text';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import CourseCard from '@/components/CourseCard';
import { AuthContext } from '@/context/AuthContext';
import { courseService } from '@/services/api';
import { useTheme } from '@/components/ThemeProvider';

export default function HomeScreen() {
  const [courses, setCourses] = useState([]);
  const [featuredCourses, setFeaturedCourses] = useState([]);
  const [popularCategories, setPopularCategories] = useState([
    { id: 1, name: 'Web Development', icon: 'code-outline', color: '#3b82f6' },
    { id: 2, name: 'Data Science', icon: 'bar-chart-outline', color: '#10b981' },
    { id: 3, name: 'Design', icon: 'color-palette-outline', color: '#f59e0b' },
    { id: 4, name: 'Marketing', icon: 'megaphone-outline', color: '#ef4444' },
    { id: 5, name: 'Business', icon: 'briefcase-outline', color: '#8b5cf6' },
    { id: 6, name: 'IT & Software', icon: 'desktop-outline', color: '#6366f1' },
  ]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const { isAuthenticated, user } = useContext(AuthContext);
  const { theme } = useTheme();

  useEffect(() => {
    fetchCourses();
  }, []);

  const fetchCourses = async () => {
    try {
      setLoading(true);
      // Fetch all courses
      const response = await courseService.getAllCourses();
      const allCourses = response.data;

      // Set all courses
      setCourses(allCourses);

      // Set featured courses (for demo, we'll just use the first 3)
      setFeaturedCourses(allCourses.slice(0, 3));

      setError(null);
    } catch (error) {
      console.error('Error fetching courses:', error);
      setError('Failed to load courses. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      router.push({
        pathname: '/explore',
        params: { search: searchQuery }
      });
    }
  };

  const handleCategoryPress = (category) => {
    router.push({
      pathname: '/explore',
      params: { category: category.name }
    });
  };

  const renderFeaturedCourse = ({ item }) => (
    <View style={styles.featuredCourseContainer}>
      <CourseCard course={item} />
    </View>
  );

  const renderCategory = ({ item }) => (
    <TouchableOpacity
      style={[styles.categoryCard, { backgroundColor: theme.card, borderColor: theme.border }]}
      onPress={() => handleCategoryPress(item)}
      activeOpacity={0.7}
    >
      <View style={[styles.iconContainer, { backgroundColor: item.color + '20' }]}>
        <Ionicons name={item.icon} size={28} color={item.color} />
      </View>
      <Text variant="caption" style={styles.categoryName}>{item.name}</Text>
    </TouchableOpacity>
  );

  const renderWelcomeSection = () => (
    <View style={styles.welcomeSection}>
      <View style={styles.welcomeHeader}>
        {isAuthenticated ? (
          <>
            <View style={styles.welcomeTextContainer}>
              <Text variant="h3" style={styles.welcomeTitle}>Welcome back,</Text>
              <Text variant="h2" color="primary" style={styles.userName}>{user?.name?.split(' ')[0] || 'Student'}</Text>
            </View>
            {user?.avatar ? (
              <Image source={{ uri: user.avatar }} style={styles.userAvatar} />
            ) : (
              <View style={[styles.avatarPlaceholder, { backgroundColor: theme.primary }]}>
                <Text style={styles.avatarText}>{user?.name?.charAt(0) || 'S'}</Text>
              </View>
            )}
          </>
        ) : (
          <View style={styles.welcomeTextContainer}>
            <Text variant="h3" style={styles.welcomeTitle}>Welcome to</Text>
            <Text variant="h2" color="primary" style={styles.platformName}>Naxofy</Text>
          </View>
        )}
      </View>

      <View style={styles.searchContainer}>
        <TextInput
          style={[styles.searchInput, { backgroundColor: theme.backgroundSecondary, borderColor: theme.border }]}
          placeholder="Search for courses..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          onSubmitEditing={handleSearch}
        />
        <TouchableOpacity
          style={[styles.searchButton, { backgroundColor: theme.primary }]}
          onPress={handleSearch}
        >
          <Ionicons name="search" size={20} color="white" />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <Container scroll refreshing={loading} onRefresh={fetchCourses}>
      {renderWelcomeSection()}

      {error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <Button onPress={fetchCourses}>Try Again</Button>
        </View>
      ) : (
        <>
          <View style={styles.sectionHeader}>
            <Text variant="h4">Featured Courses</Text>
            <TouchableOpacity onPress={() => router.push('/explore')}>
              <Text color="primary">See All</Text>
            </TouchableOpacity>
          </View>

          <FlatList
            data={featuredCourses}
            renderItem={renderFeaturedCourse}
            keyExtractor={(item) => item.id.toString()}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.featuredList}
            snapToAlignment="start"
            decelerationRate="fast"
            snapToInterval={320} // Adjust based on your card width + padding
            ListEmptyComponent={
              loading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color={theme.primary} />
                </View>
              ) : (
                <View style={styles.emptyContainer}>
                  <Ionicons name="school-outline" size={48} color={theme.textSecondary} />
                  <Text style={styles.emptyText}>No featured courses available</Text>
                </View>
              )
            }
          />

          <Text variant="h4" style={styles.categoriesTitle}>Popular Categories</Text>

          <FlatList
            data={popularCategories}
            renderItem={renderCategory}
            keyExtractor={(item) => item.id.toString()}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesList}
          />

          {isAuthenticated ? (
            <>
              <View style={styles.sectionHeader}>
                <Text variant="h4">Continue Learning</Text>
                <TouchableOpacity onPress={() => router.push('/my-courses')}>
                  <Text color="primary">My Courses</Text>
                </TouchableOpacity>
              </View>

              <Card style={styles.continueCard}>
                <View style={styles.continueContent}>
                  <View style={styles.continueInfo}>
                    <Ionicons name="school-outline" size={32} color={theme.primary} style={styles.continueIcon} />
                    <View>
                      <Text variant="subtitle">No active courses</Text>
                      <Text variant="caption" color="muted">
                        Enroll in a course to start learning
                      </Text>
                    </View>
                  </View>
                  <Button
                    variant="outline"
                    onPress={() => router.push('/explore')}
                  >
                    Find Courses
                  </Button>
                </View>
              </Card>
            </>
          ) : (
            <Card style={styles.authCard}>
              <View style={styles.authCardContent}>
                <View style={styles.authIconContainer}>
                  <Ionicons name="person-circle-outline" size={48} color={theme.primary} />
                </View>
                <Text variant="subtitle" style={styles.authTitle}>Sign in to track your progress</Text>
                <Text variant="caption" color="muted" style={styles.authText}>
                  Create an account to enroll in courses, track your progress, and earn certificates.
                </Text>
                <View style={styles.authButtons}>
                  <Button
                    onPress={() => router.push('/login')}
                    style={styles.authButton}
                  >
                    Log In
                  </Button>
                  <Button
                    variant="outline"
                    onPress={() => router.push('/register')}
                    style={styles.authButton}
                  >
                    Register
                  </Button>
                </View>
              </View>
            </Card>
          )}
        </>
      )}
    </Container>
  );
}

const styles = StyleSheet.create({
  welcomeSection: {
    marginBottom: 28,
  },
  welcomeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  welcomeTextContainer: {
    flex: 1,
  },
  welcomeTitle: {
    fontWeight: '400',
    marginBottom: 4,
  },
  userName: {
    fontWeight: '700',
  },
  platformName: {
    fontWeight: '700',
  },
  userAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  avatarPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  searchContainer: {
    flexDirection: 'row',
    marginTop: 16,
    alignItems: 'center',
  },
  searchInput: {
    flex: 1,
    height: 50,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  searchButton: {
    width: 50,
    height: 50,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    marginTop: 8,
  },
  featuredCourseContainer: {
    width: 320,
    paddingRight: 16,
  },
  featuredList: {
    paddingLeft: 16,
  },
  categoriesTitle: {
    marginTop: 28,
    marginBottom: 16,
  },
  categoriesList: {
    paddingLeft: 16,
    paddingRight: 16,
  },
  categoryCard: {
    width: 110,
    height: 110,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    borderWidth: 1,
    padding: 8,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryName: {
    textAlign: 'center',
    fontWeight: '500',
  },
  continueCard: {
    marginTop: 8,
    marginBottom: 24,
    borderRadius: 16,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  continueContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 4,
  },
  continueInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  continueIcon: {
    marginRight: 12,
  },
  authCard: {
    marginTop: 24,
    marginBottom: 24,
    borderRadius: 16,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  authCardContent: {
    alignItems: 'center',
    padding: 8,
  },
  authIconContainer: {
    marginBottom: 16,
  },
  authTitle: {
    fontWeight: '600',
    marginBottom: 8,
  },
  authText: {
    marginBottom: 20,
    textAlign: 'center',
  },
  authButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  authButton: {
    flex: 1,
    marginHorizontal: 8,
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    height: 200,
    width: 300,
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    height: 200,
    width: 300,
  },
  errorContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    height: 200,
  },
  errorText: {
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyText: {
    marginTop: 16,
    textAlign: 'center',
    width: 300,
  },
});
