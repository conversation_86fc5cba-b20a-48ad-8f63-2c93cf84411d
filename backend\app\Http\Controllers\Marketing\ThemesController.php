<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ThemesController extends Controller
{
    /**
     * Display the themes page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $themes = [
            [
                'name' => 'Academy',
                'description' => 'A professional theme designed for educational institutions and academies.',
                'thumbnail' => 'img/marketing/themes/academy.jpg',
                'preview_url' => '/themes/preview/academy',
                'features' => [
                    'Clean, professional design',
                    'Optimized for educational content',
                    'Multiple color schemes',
                    'Responsive design',
                ],
                'price' => 0,
                'popular' => true,
            ],
            [
                'name' => 'Corporate',
                'description' => 'Perfect for corporate training and professional development platforms.',
                'thumbnail' => 'img/marketing/themes/corporate.jpg',
                'preview_url' => '/themes/preview/corporate',
                'features' => [
                    'Business-oriented design',
                    'Employee dashboard',
                    'Training progress tracking',
                    'Certificate templates',
                ],
                'price' => 49,
                'popular' => false,
            ],
            [
                'name' => 'Creative',
                'description' => 'A vibrant theme for creative courses and artistic education.',
                'thumbnail' => 'img/marketing/themes/creative.jpg',
                'preview_url' => '/themes/preview/creative',
                'features' => [
                    'Bold, artistic design',
                    'Portfolio showcase',
                    'Project gallery',
                    'Student work highlights',
                ],
                'price' => 49,
                'popular' => false,
            ],
            [
                'name' => 'Minimal',
                'description' => 'A clean, distraction-free theme focused on content delivery.',
                'thumbnail' => 'img/marketing/themes/minimal.jpg',
                'preview_url' => '/themes/preview/minimal',
                'features' => [
                    'Minimalist design',
                    'Content-focused layout',
                    'Fast loading times',
                    'Reading-optimized typography',
                ],
                'price' => 29,
                'popular' => true,
            ],
        ];

        return view('marketing.themes', compact('themes'));
    }
}
