<?php

use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\CourseController;
use App\Http\Controllers\API\LessonController;
use App\Http\Controllers\API\PaymentController;
use App\Http\Controllers\API\TenantController;
use App\Http\Controllers\Auth\GoogleController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Cookie;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// CSRF Token route
Route::get('/csrf-token', function (Request $request) {
    $token = csrf_token();
    return response()->json(['token' => $token]);
});

// Auth routes
Route::group([
    'prefix' => 'auth'
], function () {
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/register-tenant', [AuthController::class, 'registerTenant']);
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::post('/refresh', [AuthController::class, 'refresh']);
    Route::get('/profile', [AuthController::class, 'userProfile']);

    // Google OAuth routes
    Route::get('/google', [GoogleController::class, 'redirectToGoogle']);
    Route::get('/google/callback', [GoogleController::class, 'handleGoogleCallback']);
});

// Course routes
Route::get('/courses', [CourseController::class, 'index']);
Route::get('/courses/{id}', [CourseController::class, 'show']);

// Protected course routes
Route::middleware('auth:api')->group(function () {
    Route::post('/courses', [CourseController::class, 'store']);
    Route::put('/courses/{id}', [CourseController::class, 'update']);
    Route::delete('/courses/{id}', [CourseController::class, 'destroy']);
    Route::post('/courses/{id}/enroll', [CourseController::class, 'enroll']);
    Route::post('/courses/{id}/progress', [CourseController::class, 'updateProgress']);
});

// Lesson routes
Route::apiResource('lessons', LessonController::class);

// Tenant routes
Route::apiResource('tenants', TenantController::class);
Route::post('/tenants/{id}/approve', [TenantController::class, 'approve']);

// Payment routes
Route::post('/payments/create-order', [PaymentController::class, 'createOrder']);
Route::post('/payments/verify', [PaymentController::class, 'verifyPayment']);
Route::get('/payments/history', [PaymentController::class, 'history']);
