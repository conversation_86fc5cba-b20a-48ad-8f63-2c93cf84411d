import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { Tabs } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from './ThemeProvider';
import Text from './ui/Text';

/**
 * Custom Bottom Tab Bar component
 */
const BottomTabBar = () => {
  const { theme } = useTheme();

  // Tab configuration
  const tabs = [
    {
      name: 'index',
      label: 'Home',
      icon: 'home-outline',
      activeIcon: 'home',
    },
    {
      name: 'explore',
      label: 'Explore',
      icon: 'search-outline',
      activeIcon: 'search',
    },
    {
      name: 'marketplace',
      label: 'Marketplace',
      icon: 'storefront-outline',
      activeIcon: 'storefront',
    },
    {
      name: 'my-courses',
      label: 'My Courses',
      icon: 'book-outline',
      activeIcon: 'book',
    },
    {
      name: 'profile',
      label: 'Profile',
      icon: 'person-outline',
      activeIcon: 'person',
    },
  ];

  // Custom tab bar
  const TabBar = ({ state, descriptors, navigation }) => {
    return (
      <View style={[
        styles.tabBar,
        {
          backgroundColor: theme.tabBackground,
          borderTopColor: theme.border,
        }
      ]}>
        {state.routes.map((route, index) => {
          const { options } = descriptors[route.key];
          const label = options.tabBarLabel || options.title || route.name;

          const isFocused = state.index === index;

          // Find the tab configuration
          const tab = tabs.find(t => t.name === route.name) || {
            icon: 'help-outline',
            activeIcon: 'help',
            label: label,
          };

          const onPress = () => {
            const event = navigation.emit({
              type: 'tabPress',
              target: route.key,
              canPreventDefault: true,
            });

            if (!isFocused && !event.defaultPrevented) {
              navigation.navigate(route.name);
            }
          };

          return (
            <TouchableOpacity
              key={route.key}
              accessibilityRole="button"
              accessibilityState={isFocused ? { selected: true } : {}}
              accessibilityLabel={options.tabBarAccessibilityLabel}
              testID={options.tabBarTestID}
              onPress={onPress}
              style={styles.tabItem}
              activeOpacity={0.7}
            >
              <Ionicons
                name={isFocused ? tab.activeIcon : tab.icon}
                size={24}
                color={isFocused ? theme.tabIconSelected : theme.tabIconDefault}
              />
              <Text
                style={[
                  styles.tabLabel,
                  { color: isFocused ? theme.tabIconSelected : theme.tabIconDefault }
                ]}
              >
                {tab.label}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    );
  };

  return (
    <Tabs
      screenOptions={{
        headerShown: false,
      }}
      tabBar={props => <TabBar {...props} />}
    >
      <Tabs.Screen name="index" />
      <Tabs.Screen name="explore" />
      <Tabs.Screen name="marketplace" />
      <Tabs.Screen name="my-courses" />
      <Tabs.Screen name="profile" />
    </Tabs>
  );
};

const styles = StyleSheet.create({
  tabBar: {
    flexDirection: 'row',
    height: 60,
    borderTopWidth: 1,
  },
  tabItem: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabLabel: {
    fontSize: 12,
    marginTop: 2,
  },
});

export default BottomTabBar;
