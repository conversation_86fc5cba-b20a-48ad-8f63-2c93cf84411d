<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Module extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'version',
        'description',
        'author',
        'author_url',
        'thumbnail',
        'settings',
        'permissions',
        'hooks',
        'dependencies',
        'is_active',
        'is_system',
        'tenant_id',
        'category_id',
        'price',
        'downloads',
        'is_featured',
        'screenshots',
        'demo_url',
        'status',
        'rejection_reason',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'settings' => 'array',
        'permissions' => 'array',
        'hooks' => 'array',
        'dependencies' => 'array',
        'screenshots' => 'array',
        'is_active' => 'boolean',
        'is_system' => 'boolean',
        'is_featured' => 'boolean',
        'price' => 'decimal:2',
        'downloads' => 'integer',
    ];

    /**
     * Get the tenant that owns the module.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the category that the module belongs to.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(MarketplaceCategory::class, 'category_id');
    }

    /**
     * Get the reviews for the module.
     */
    public function reviews(): MorphMany
    {
        return $this->morphMany(MarketplaceReview::class, 'reviewable');
    }

    /**
     * Get the tenants that have installed this module.
     */
    public function tenants(): BelongsToMany
    {
        return $this->belongsToMany(Tenant::class)
            ->withPivot('is_active', 'settings')
            ->withTimestamps();
    }

    /**
     * Activate this module.
     */
    public function activate(): bool
    {
        $this->is_active = true;
        return $this->save();
    }

    /**
     * Deactivate this module.
     */
    public function deactivate(): bool
    {
        $this->is_active = false;
        return $this->save();
    }

    /**
     * Check if this module has a specific hook.
     */
    public function hasHook(string $hook): bool
    {
        return isset($this->hooks[$hook]);
    }

    /**
     * Get the hook handler for a specific hook.
     */
    public function getHookHandler(string $hook): ?string
    {
        return $this->hooks[$hook] ?? null;
    }

    /**
     * Check if all dependencies are satisfied.
     */
    public function checkDependencies(): bool
    {
        if (empty($this->dependencies)) {
            return true;
        }

        foreach ($this->dependencies as $dependency => $version) {
            $module = self::where('slug', $dependency)->first();

            if (!$module || !$module->is_active) {
                return false;
            }

            // Simple version check (could be more sophisticated)
            if (version_compare($module->version, $version, '<')) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get the average rating for this module.
     */
    public function getAverageRatingAttribute()
    {
        return $this->reviews()->approved()->avg('rating') ?: 0;
    }

    /**
     * Get the total number of reviews for this module.
     */
    public function getReviewsCountAttribute()
    {
        return $this->reviews()->approved()->count();
    }

    /**
     * Scope a query to only include approved modules.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope a query to only include featured modules.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to only include free modules.
     */
    public function scopeFree($query)
    {
        return $query->where('price', 0);
    }

    /**
     * Scope a query to only include paid modules.
     */
    public function scopePaid($query)
    {
        return $query->where('price', '>', 0);
    }

    /**
     * Increment the download count for this module.
     */
    public function incrementDownloads(): bool
    {
        $this->downloads += 1;
        return $this->save();
    }
}
