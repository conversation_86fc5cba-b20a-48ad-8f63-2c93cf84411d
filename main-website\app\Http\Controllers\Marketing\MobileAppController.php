<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class MobileAppController extends Controller
{
    /**
     * Display the mobile app page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $features = [
            [
                'name' => 'White-Label Branding',
                'description' => 'Customize the app with your logo, colors, and branding.',
                'icon' => 'color-swatch',
            ],
            [
                'name' => 'Offline Access',
                'description' => 'Download courses for offline viewing, perfect for learning on-the-go.',
                'icon' => 'download',
            ],
            [
                'name' => 'Push Notifications',
                'description' => 'Send targeted push notifications to keep students engaged and informed.',
                'icon' => 'bell',
            ],
            [
                'name' => 'Native Video Playback',
                'description' => 'Smooth video playback with speed control and background audio.',
                'icon' => 'play',
            ],
            [
                'name' => 'Interactive Quizzes',
                'description' => 'Engage students with interactive quizzes and assessments.',
                'icon' => 'clipboard-check',
            ],
            [
                'name' => 'Progress Tracking',
                'description' => 'Track course progress and completion across devices.',
                'icon' => 'chart-bar',
            ],
        ];

        $testimonials = [
            [
                'name' => '<PERSON>',
                'role' => 'Founder, CodeAcademy',
                'content' => 'The white-label mobile app has been a game-changer for our business. Our students love being able to learn on-the-go, and we\'ve seen a 40% increase in course completion rates.',
                'avatar' => 'https://source.unsplash.com/random/100x100/?woman',
            ],
            [
                'name' => 'David Wilson',
                'role' => 'Director, TechEd Institute',
                'content' => 'Having our own branded mobile app has given us a competitive edge in the market. The offline access feature is particularly popular with our students who travel frequently.',
                'avatar' => 'https://source.unsplash.com/random/100x100/?man',
            ],
            [
                'name' => 'Emily Rodriguez',
                'role' => 'CEO, SkillMaster',
                'content' => 'The mobile app has helped us reach a whole new audience of learners who prefer mobile-first experiences. The seamless integration with our web platform makes it easy to manage content across all platforms.',
                'avatar' => 'https://source.unsplash.com/random/100x100/?woman,business',
            ],
        ];

        $faqs = [
            [
                'question' => 'How long does it take to get my branded mobile app?',
                'answer' => 'Once you\'ve submitted your branding assets and app configuration, it typically takes 2-3 weeks for your app to be built, tested, and submitted to the app stores. App store approval can take an additional 1-2 weeks.',
            ],
            [
                'question' => 'Can I publish the app under my own developer account?',
                'answer' => 'Yes, you can publish the app under your own Apple Developer and Google Play developer accounts. We also offer the option to publish under our developer account if you prefer.',
            ],
            [
                'question' => 'How are app updates handled?',
                'answer' => 'We handle all technical updates to ensure your app stays compatible with the latest iOS and Android versions. Content updates are automatically synced from your web platform to the mobile app.',
            ],
            [
                'question' => 'Can students access the same courses on both web and mobile?',
                'answer' => 'Yes, students can seamlessly switch between web and mobile. Their progress, enrollments, and account information are synchronized across all platforms.',
            ],
            [
                'question' => 'Is the mobile app included in all plans?',
                'answer' => 'The white-label mobile app is included in our Enterprise plan. It can be added to other plans for an additional fee.',
            ],
        ];

        return view('marketing.mobile-app', compact('features', 'testimonials', 'faqs'));
    }
}
