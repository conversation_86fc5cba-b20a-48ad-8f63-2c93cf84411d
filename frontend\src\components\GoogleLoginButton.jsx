import { useState } from 'react';
import { Button, CircularProgress } from '@mui/material';
import GoogleIcon from '@mui/icons-material/Google';
import api from '../services/api';

const GoogleLoginButton = () => {
  const [loading, setLoading] = useState(false);

  const handleGoogleLogin = async () => {
    try {
      setLoading(true);

      // Get the Google OAuth URL from the backend
      const response = await api.get('/auth/google');
      const data = response.data;

      // Redirect to Google's OAuth page
      window.location.href = data.url;
    } catch (error) {
      console.error('Google login error:', error);
      setLoading(false);
    }
  };

  return (
    <Button
      variant="outlined"
      fullWidth
      startIcon={loading ? <CircularProgress size={20} /> : <GoogleIcon />}
      onClick={handleGoogleLogin}
      disabled={loading}
      sx={{
        mt: 2,
        mb: 2,
        py: 1.2,
        color: '#757575',
        borderColor: '#DADCE0',
        '&:hover': {
          borderColor: '#DADCE0',
          backgroundColor: 'rgba(0, 0, 0, 0.05)',
        },
      }}
    >
      {loading ? 'Connecting...' : 'Continue with Google'}
    </Button>
  );
};

export default GoogleLoginButton;
