<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Tenant extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'domain',
        'logo',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the users for the tenant.
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the courses for the tenant.
     */
    public function courses(): HasMany
    {
        return $this->hasMany(Course::class);
    }

    /**
     * Get the themes for the tenant.
     */
    public function themes(): HasMany
    {
        return $this->hasMany(Theme::class);
    }

    /**
     * Get the active theme for the tenant.
     */
    public function activeTheme()
    {
        return $this->themes()->where('is_active', true)->first();
    }

    /**
     * Get the modules owned by the tenant.
     */
    public function ownedModules(): HasMany
    {
        return $this->hasMany(Module::class);
    }

    /**
     * Get the modules installed by the tenant.
     */
    public function installedModules(): BelongsToMany
    {
        return $this->belongsToMany(Module::class)
            ->withPivot('is_active', 'settings')
            ->withTimestamps();
    }

    /**
     * Get the active modules for the tenant.
     */
    public function activeModules()
    {
        return $this->installedModules()->wherePivot('is_active', true)->get();
    }
}
