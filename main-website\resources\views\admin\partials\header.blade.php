<header class="bg-white shadow">
    <div class="px-6 py-4 flex items-center justify-between">
        <div class="flex items-center">
            <button @click="$store.sidebar.toggle()" class="mr-4 text-gray-500 hover:text-gray-700 focus:outline-none">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
            </button>
            <h1 class="text-xl font-semibold text-gray-800">@yield('header', 'Dashboard')</h1>
        </div>
        <div class="flex items-center">
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" class="flex items-center text-gray-500 hover:text-gray-700 focus:outline-none">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                    </svg>
                    @php
                        // Initialize notifications in session if not exists
                        if (!Session::has('notifications')) {
                            // This is a simple way to initialize notifications in the view
                            // In a real application, you would use a middleware or service provider
                            $notifications = [];
                            $notificationTypes = ['tenant_created', 'payment_received', 'subscription_canceled', 'support_ticket', 'system_alert', 'payment_failed', 'new_feature', 'security_alert', 'storage_alert', 'tenant_updated'];
                            $notificationTitles = ['New Tenant Registered', 'Payment Received', 'Subscription Canceled', 'New Support Ticket', 'System Update Required', 'Payment Failed', 'New Feature Available', 'Security Alert', 'Storage Limit Warning', 'Tenant Updated Plan'];
                            $notificationMessages = [
                                'Acme Inc. has registered as a new tenant.',
                                'Payment of $99.99 received from XYZ Corp.',
                                'ABC Ltd has canceled their Premium subscription.',
                                'Tech Solutions has opened a new support ticket: "Cannot access course content".',
                                'A new system update is available. Please update to the latest version.',
                                'Payment from Tech Solutions failed due to expired card.',
                                'A new feature "Advanced Analytics" is now available for all tenants.',
                                'Multiple failed login attempts detected for admin account.',
                                'Acme Inc. is approaching their storage limit (85% used).',
                                'Global Education has upgraded to Premium plan.'
                            ];

                            for ($i = 1; $i <= 10; $i++) {
                                $notifications[] = (object) [
                                    'id' => $i,
                                    'type' => $notificationTypes[$i-1],
                                    'title' => $notificationTitles[$i-1],
                                    'message' => $notificationMessages[$i-1],
                                    'is_read' => !in_array($i, [1, 3, 4, 6, 8, 10]),
                                    'created_at' => now()->subHours($i * 2),
                                ];
                            }
                            Session::put('notifications', $notifications);
                        }

                        // Get unread notifications count
                        $unreadCount = collect(Session::get('notifications'))->where('is_read', false)->count();
                    @endphp
                    <span class="ml-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">{{ $unreadCount }}</span>
                </button>
                <div x-show="open" @click.away="open = false" class="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg z-10">
                    <div class="py-2 px-4 border-b border-gray-200">
                        <h3 class="text-sm font-semibold text-gray-700">Notifications</h3>
                    </div>
                    <div class="max-h-64 overflow-y-auto">
                        @php
                            // Get notifications from session
                            $headerNotifications = collect(Session::get('notifications'))->take(5);
                        @endphp

                        @forelse($headerNotifications as $notification)
                            <a href="{{ route('admin.notifications.show', $notification->id) }}" class="block px-4 py-3 hover:bg-gray-50 border-b border-gray-200 {{ $notification->is_read ? 'bg-white' : 'bg-blue-50' }}">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        @if($notification->type === 'tenant_created')
                                        <span class="inline-block h-8 w-8 rounded-full bg-blue-100 text-blue-500 flex items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                            </svg>
                                        </span>
                                        @elseif($notification->type === 'payment_received')
                                        <span class="inline-block h-8 w-8 rounded-full bg-green-100 text-green-500 flex items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                                            </svg>
                                        </span>
                                        @elseif($notification->type === 'subscription_canceled')
                                        <span class="inline-block h-8 w-8 rounded-full bg-red-100 text-red-500 flex items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
                                            </svg>
                                        </span>
                                        @elseif($notification->type === 'support_ticket')
                                        <span class="inline-block h-8 w-8 rounded-full bg-purple-100 text-purple-500 flex items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                                            </svg>
                                        </span>
                                        @elseif($notification->type === 'system_alert')
                                        <span class="inline-block h-8 w-8 rounded-full bg-yellow-100 text-yellow-500 flex items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                            </svg>
                                        </span>
                                        @elseif($notification->type === 'payment_failed')
                                        <span class="inline-block h-8 w-8 rounded-full bg-red-100 text-red-500 flex items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </span>
                                        @else
                                        <span class="inline-block h-8 w-8 rounded-full bg-gray-100 text-gray-500 flex items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </span>
                                        @endif
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900">{{ $notification->title ?? 'Notification' }}</p>
                                        <p class="text-xs text-gray-500">{{ $notification->message ?? 'You have a new notification' }}</p>
                                        <p class="text-xs text-gray-400 mt-1">{{ isset($notification->created_at) ? $notification->created_at->diffForHumans() : now()->diffForHumans() }}</p>
                                    </div>
                                </div>
                            </a>
                        @empty
                            <div class="px-4 py-3 text-center text-gray-500">
                                No notifications found.
                            </div>
                        @endforelse
                    </div>
                    <div class="py-2 px-4 border-t border-gray-200 text-center">
                        <a href="{{ route('admin.notifications.index') }}" class="text-sm font-medium text-primary-500 hover:text-primary-700">View all notifications</a>
                    </div>
                </div>
            </div>

            <div class="ml-4 relative" x-data="{ open: false }">
                <button @click="open = !open" class="flex items-center focus:outline-none">
                    <img class="h-8 w-8 rounded-full object-cover" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="Admin profile">
                    <span class="ml-2 text-gray-700">{{ Auth::user()->name ?? 'Admin' }}</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="ml-1 h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </button>
                <div x-show="open" @click.away="open = false" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Your Profile</a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                        <form action="{{ route('logout') }}" method="POST">
                            @csrf
                            <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign out</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>
