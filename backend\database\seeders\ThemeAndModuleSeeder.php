<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Theme;
use App\Models\Module;
use App\Models\Tenant;
use App\Models\MarketplaceCategory;
use Illuminate\Support\Str;

class ThemeAndModuleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default themes
        $themes = [
            [
                'name' => 'Default Light',
                'slug' => 'default-light',
                'version' => '1.0.0',
                'description' => 'The default light theme for the LMS platform.',
                'author' => 'LMS Platform',
                'author_url' => 'https://lmsplatform.com',
                'thumbnail' => 'https://source.unsplash.com/random?light',
                'colors' => json_encode([
                    'primary' => '#2563eb',
                    'secondary' => '#f43f5e',
                    'background' => '#f8fafc',
                    'text' => '#1e293b',
                ]),
                'fonts' => json_encode([
                    'heading' => 'Inter, sans-serif',
                    'body' => 'Inter, sans-serif',
                ]),
                'is_active' => true,
                'is_system' => true,
                'status' => 'approved',
                'is_featured' => true,
                'price' => 0.00,
            ],
            [
                'name' => 'Dark Mode',
                'slug' => 'dark-mode',
                'version' => '1.0.0',
                'description' => 'A dark theme for the LMS platform.',
                'author' => 'LMS Platform',
                'author_url' => 'https://lmsplatform.com',
                'thumbnail' => 'https://source.unsplash.com/random?dark',
                'colors' => json_encode([
                    'primary' => '#3b82f6',
                    'secondary' => '#f43f5e',
                    'background' => '#1e293b',
                    'text' => '#f8fafc',
                ]),
                'fonts' => json_encode([
                    'heading' => 'Inter, sans-serif',
                    'body' => 'Inter, sans-serif',
                ]),
                'is_active' => false,
                'is_system' => true,
                'status' => 'approved',
                'is_featured' => false,
                'price' => 0.00,
            ],
            [
                'name' => 'Colorful',
                'slug' => 'colorful',
                'version' => '1.0.0',
                'description' => 'A colorful theme for the LMS platform.',
                'author' => 'LMS Platform',
                'author_url' => 'https://lmsplatform.com',
                'thumbnail' => 'https://source.unsplash.com/random?colorful',
                'colors' => json_encode([
                    'primary' => '#8b5cf6',
                    'secondary' => '#ec4899',
                    'background' => '#ffffff',
                    'text' => '#1e293b',
                ]),
                'fonts' => json_encode([
                    'heading' => 'Poppins, sans-serif',
                    'body' => 'Roboto, sans-serif',
                ]),
                'is_active' => false,
                'is_system' => true,
            ],
        ];

        // Get categories for themes
        $educationCategory = MarketplaceCategory::where('slug', 'education')->first();
        $businessCategory = MarketplaceCategory::where('slug', 'business')->first();
        $creativeCategory = MarketplaceCategory::where('slug', 'creative')->first();

        // Assign categories to themes
        $themes[0]['category_id'] = $educationCategory->id; // Default Light -> Education
        $themes[1]['category_id'] = $businessCategory->id; // Dark Mode -> Business
        $themes[2]['category_id'] = $creativeCategory->id; // Colorful -> Creative

        foreach ($themes as $themeData) {
            Theme::firstOrCreate(
                ['slug' => $themeData['slug']],
                $themeData
            );
        }

        // Create default modules
        $modules = [
            [
                'name' => 'Discussion Forum',
                'slug' => 'discussion-forum',
                'version' => '1.0.0',
                'description' => 'Adds discussion forum functionality to courses.',
                'author' => 'LMS Platform',
                'author_url' => 'https://lmsplatform.com',
                'thumbnail' => 'https://source.unsplash.com/random?forum',
                'hooks' => json_encode([
                    'course.view' => 'DiscussionForumHook@renderCourseTab',
                    'lesson.view' => 'DiscussionForumHook@renderLessonComments',
                ]),
                'is_active' => true,
                'is_system' => true,
                'status' => 'approved',
                'is_featured' => true,
                'price' => 0.00,
            ],
            [
                'name' => 'Certificate Generator',
                'slug' => 'certificate-generator',
                'version' => '1.0.0',
                'description' => 'Generates certificates for completed courses.',
                'author' => 'LMS Platform',
                'author_url' => 'https://lmsplatform.com',
                'thumbnail' => 'https://source.unsplash.com/random?certificate',
                'hooks' => json_encode([
                    'course.completed' => 'CertificateGeneratorHook@generateCertificate',
                    'dashboard.view' => 'CertificateGeneratorHook@renderCertificatesTab',
                ]),
                'is_active' => true,
                'is_system' => true,
                'status' => 'approved',
                'is_featured' => true,
                'price' => 0.00,
            ],
            [
                'name' => 'Progress Tracker',
                'slug' => 'progress-tracker',
                'version' => '1.0.0',
                'description' => 'Tracks and visualizes student progress through courses.',
                'author' => 'LMS Platform',
                'author_url' => 'https://lmsplatform.com',
                'thumbnail' => 'https://source.unsplash.com/random?progress',
                'hooks' => json_encode([
                    'dashboard.view' => 'ProgressTrackerHook@renderProgressWidget',
                    'course.view' => 'ProgressTrackerHook@renderCourseProgress',
                ]),
                'dependencies' => json_encode([
                    'certificate-generator' => '1.0.0',
                ]),
                'is_active' => true,
                'is_system' => true,
                'status' => 'approved',
                'is_featured' => false,
                'price' => 0.00,
            ],
            [
                'name' => 'Social Sharing',
                'slug' => 'social-sharing',
                'version' => '1.0.0',
                'description' => 'Adds social sharing buttons to courses and lessons.',
                'author' => 'LMS Platform',
                'author_url' => 'https://lmsplatform.com',
                'thumbnail' => 'https://source.unsplash.com/random?social',
                'hooks' => json_encode([
                    'course.view' => 'SocialSharingHook@renderSharingButtons',
                    'lesson.view' => 'SocialSharingHook@renderSharingButtons',
                ]),
                'is_active' => false,
                'is_system' => true,
                'status' => 'approved',
                'is_featured' => false,
                'price' => 0.00,
            ],
        ];

        // Get categories for modules
        $communicationCategory = MarketplaceCategory::where('slug', 'communication')->first();
        $assessmentCategory = MarketplaceCategory::where('slug', 'assessment')->first();
        $engagementCategory = MarketplaceCategory::where('slug', 'engagement')->first();
        $integrationCategory = MarketplaceCategory::where('slug', 'integration')->first();

        // Assign categories to modules
        $modules[0]['category_id'] = $communicationCategory->id; // Discussion Forum -> Communication
        $modules[1]['category_id'] = $assessmentCategory->id; // Certificate Generator -> Assessment
        $modules[2]['category_id'] = $engagementCategory->id; // Progress Tracker -> Engagement
        $modules[3]['category_id'] = $integrationCategory->id; // Social Sharing -> Integration

        foreach ($modules as $moduleData) {
            Module::firstOrCreate(
                ['slug' => $moduleData['slug']],
                $moduleData
            );
        }

        // Create tenant-specific themes and modules if tenants exist
        $tenants = Tenant::where('is_active', true)->get();

        foreach ($tenants as $tenant) {
            // Create a tenant-specific theme if it doesn't exist
            $tenantThemeSlug = Str::slug($tenant->name) . '-theme';
            Theme::firstOrCreate(
                ['slug' => $tenantThemeSlug],
                [
                    'name' => $tenant->name . ' Theme',
                    'slug' => $tenantThemeSlug,
                    'version' => '1.0.0',
                    'description' => 'Custom theme for ' . $tenant->name,
                    'author' => $tenant->name,
                    'thumbnail' => 'https://source.unsplash.com/random?brand&sig=' . $tenant->id,
                    'colors' => json_encode([
                        'primary' => '#' . substr(md5($tenant->name), 0, 6),
                        'secondary' => '#' . substr(md5($tenant->name . 'secondary'), 0, 6),
                        'background' => '#ffffff',
                        'text' => '#1e293b',
                    ]),
                    'fonts' => json_encode([
                        'heading' => 'Inter, sans-serif',
                        'body' => 'Inter, sans-serif',
                    ]),
                    'is_active' => false,
                    'tenant_id' => $tenant->id,
                    'status' => 'approved',
                    'price' => 0.00,
                ]
            );

            // Install some modules for the tenant if not already installed
            $discussionModule = Module::where('slug', 'discussion-forum')->first();
            if ($discussionModule) {
                // Check if module is already installed for this tenant
                if (!$tenant->installedModules()->where('module_id', $discussionModule->id)->exists()) {
                    $tenant->installedModules()->attach(
                        $discussionModule->id,
                        ['is_active' => true, 'settings' => json_encode([])]
                    );
                }
            }

            $certificateModule = Module::where('slug', 'certificate-generator')->first();
            if ($certificateModule) {
                // Check if module is already installed for this tenant
                if (!$tenant->installedModules()->where('module_id', $certificateModule->id)->exists()) {
                    $tenant->installedModules()->attach(
                        $certificateModule->id,
                        ['is_active' => true, 'settings' => json_encode([])]
                    );
                }
            }
        }
    }
}
