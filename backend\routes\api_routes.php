<?php

use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\CourseController;
use App\Http\Controllers\API\LessonController;
use App\Http\Controllers\API\PaymentController;
use App\Http\Controllers\API\TenantController;
use App\Http\Controllers\Auth\GoogleController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// Auth routes
Route::group([
    'prefix' => 'auth'
], function () {
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/register-tenant', [AuthController::class, 'registerTenant']);
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::post('/refresh', [AuthController::class, 'refresh']);
    Route::get('/profile', [AuthController::class, 'userProfile']);

    // Google OAuth routes
    Route::get('/google', [GoogleController::class, 'redirectToGoogle']);
    Route::get('/google/callback', [GoogleController::class, 'handleGoogleCallback']);
});

// Course routes
Route::apiResource('courses', CourseController::class);
Route::post('/courses/{id}/enroll', [CourseController::class, 'enroll']);
Route::post('/courses/{id}/progress', [CourseController::class, 'updateProgress']);

// Lesson routes
Route::apiResource('lessons', LessonController::class);

// Tenant routes
Route::apiResource('tenants', TenantController::class);
Route::post('/tenants/{id}/approve', [TenantController::class, 'approve']);

// Payment routes
Route::post('/payments/create-order', [PaymentController::class, 'createOrder']);
Route::post('/payments/verify', [PaymentController::class, 'verifyPayment']);
Route::get('/payments/history', [PaymentController::class, 'history']);

// Test route
Route::get('/test', function () {
    return response()->json(['message' => 'API is working!']);
});
