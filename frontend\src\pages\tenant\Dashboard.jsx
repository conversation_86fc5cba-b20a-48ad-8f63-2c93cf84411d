import React, { useState, useEffect, useContext } from 'react';
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  Divider,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  CircularProgress,
  Chip
} from '@mui/material';
import {
  PeopleAlt as PeopleIcon,
  School as SchoolIcon,
  AttachMoney as MoneyIcon,
  TrendingUp as TrendingUpIcon,
  Person as PersonIcon,
  PlayCircleOutline as PlayCircleOutlineIcon
} from '@mui/icons-material';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';
import { AuthContext } from '../../context/AuthContext';
import { courseService, userService, paymentService } from '../../services/api';
import { Link as RouterLink } from 'react-router-dom';

// Sample data for charts
const revenueData = [
  { name: 'Jan', revenue: 2500 },
  { name: 'Feb', revenue: 1800 },
  { name: 'Mar', revenue: 3000 },
  { name: 'Apr', revenue: 4500 },
  { name: 'May', revenue: 3800 },
  { name: 'Jun', revenue: 5000 },
];

const enrollmentData = [
  { name: 'Jan', enrollments: 25 },
  { name: 'Feb', enrollments: 18 },
  { name: 'Mar', enrollments: 30 },
  { name: 'Apr', enrollments: 45 },
  { name: 'May', enrollments: 38 },
  { name: 'Jun', enrollments: 50 },
];

const courseTypeData = [
  { name: 'Technology', value: 40 },
  { name: 'Business', value: 30 },
  { name: 'Design', value: 20 },
  { name: 'Marketing', value: 10 },
];

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

const TenantDashboard = () => {
  const { user } = useContext(AuthContext);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalStudents: 0,
    totalCourses: 0,
    totalRevenue: 0,
    activeEnrollments: 0,
    completionRate: 0,
    recentEnrollments: [],
    recentTransactions: [],
    popularCourses: []
  });

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        
        // In a real implementation, you would fetch actual data from your API
        // const coursesResponse = await courseService.getStats();
        // const studentsResponse = await userService.getStudents();
        // const revenueResponse = await paymentService.getStats();
        
        // Simulate API response
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setStats({
          totalStudents: 245,
          totalCourses: 12,
          totalRevenue: 28500,
          activeEnrollments: 180,
          completionRate: 68,
          recentEnrollments: [
            { id: 1, student_name: 'John Doe', course_name: 'Advanced Web Development', date: '2025-04-20' },
            { id: 2, student_name: 'Jane Smith', course_name: 'Digital Marketing Basics', date: '2025-04-19' },
            { id: 3, student_name: 'Robert Johnson', course_name: 'Data Science Masterclass', date: '2025-04-18' },
            { id: 4, student_name: 'Emily Davis', course_name: 'UI/UX Design Principles', date: '2025-04-17' },
          ],
          recentTransactions: [
            { id: 1, amount: 99.99, course_name: 'Advanced Web Development', student_name: 'John Doe', date: '2025-04-20' },
            { id: 2, amount: 49.99, course_name: 'Digital Marketing Basics', student_name: 'Jane Smith', date: '2025-04-19' },
            { id: 3, amount: 149.99, course_name: 'Data Science Masterclass', student_name: 'Robert Johnson', date: '2025-04-18' },
            { id: 4, amount: 29.99, course_name: 'UI/UX Design Principles', student_name: 'Emily Davis', date: '2025-04-17' },
          ],
          popularCourses: [
            { id: 1, name: 'Advanced Web Development', enrollments: 45, rating: 4.8 },
            { id: 2, name: 'Data Science Masterclass', enrollments: 38, rating: 4.7 },
            { id: 3, name: 'Digital Marketing Basics', enrollments: 32, rating: 4.5 },
            { id: 4, name: 'UI/UX Design Principles', enrollments: 28, rating: 4.6 },
          ]
        });
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Welcome, {user?.name || 'Instructor'}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Here's an overview of your learning platform
        </Typography>
      </Box>
      
      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4} lg={2}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <PeopleIcon />
                </Avatar>
                <Typography variant="h6" component="div">
                  Students
                </Typography>
              </Box>
              <Typography variant="h3" component="div" sx={{ fontWeight: 'bold' }}>
                {stats.totalStudents}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total enrolled students
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={4} lg={2}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}>
                  <SchoolIcon />
                </Avatar>
                <Typography variant="h6" component="div">
                  Courses
                </Typography>
              </Box>
              <Typography variant="h3" component="div" sx={{ fontWeight: 'bold' }}>
                {stats.totalCourses}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Published courses
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={4} lg={2}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                  <MoneyIcon />
                </Avatar>
                <Typography variant="h6" component="div">
                  Revenue
                </Typography>
              </Box>
              <Typography variant="h3" component="div" sx={{ fontWeight: 'bold' }}>
                ${stats.totalRevenue.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total earnings
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={4} lg={2}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                  <PlayCircleOutlineIcon />
                </Avatar>
                <Typography variant="h6" component="div">
                  Active
                </Typography>
              </Box>
              <Typography variant="h3" component="div" sx={{ fontWeight: 'bold' }}>
                {stats.activeEnrollments}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Active enrollments
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={4} lg={2}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                  <TrendingUpIcon />
                </Avatar>
                <Typography variant="h6" component="div">
                  Completion
                </Typography>
              </Box>
              <Typography variant="h3" component="div" sx={{ fontWeight: 'bold' }}>
                {stats.completionRate}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Average completion rate
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={4} lg={2}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Button
                variant="contained"
                color="primary"
                component={RouterLink}
                to="/tenant/courses/create"
                sx={{ mb: 1 }}
              >
                Create Course
              </Button>
              <Typography variant="body2" color="text.secondary">
                Add a new course to your platform
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      {/* Charts */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Revenue & Enrollments
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart
                data={revenueData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
                <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
                <Tooltip />
                <Legend />
                <Line yAxisId="left" type="monotone" dataKey="revenue" stroke="#8884d8" name="Revenue ($)" />
                <Line yAxisId="right" type="monotone" dataKey="enrollments" stroke="#82ca9d" name="Enrollments" data={enrollmentData} />
              </LineChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Course Categories
            </Typography>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={courseTypeData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {courseTypeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value, name) => [value, name]} />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>
      
      {/* Popular Courses */}
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Popular Courses
        </Typography>
        <Grid container spacing={3}>
          {stats.popularCourses.map((course) => (
            <Grid item xs={12} sm={6} md={3} key={course.id}>
              <Card>
                <CardContent>
                  <Typography variant="h6" component="div" noWrap>
                    {course.name}
                  </Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                    <Chip 
                      icon={<PeopleIcon />} 
                      label={`${course.enrollments} students`} 
                      size="small" 
                      color="primary"
                    />
                    <Chip 
                      icon={<PersonIcon />} 
                      label={`${course.rating}/5`} 
                      size="small" 
                      color="secondary"
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Paper>
      
      {/* Recent Activity */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recent Enrollments
            </Typography>
            <List>
              {stats.recentEnrollments.map((enrollment) => (
                <React.Fragment key={enrollment.id}>
                  <ListItem alignItems="flex-start">
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        <PersonIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={enrollment.student_name}
                      secondary={
                        <>
                          <Typography component="span" variant="body2" color="text.primary">
                            {enrollment.course_name}
                          </Typography>
                          {` — ${enrollment.date}`}
                        </>
                      }
                    />
                  </ListItem>
                  <Divider variant="inset" component="li" />
                </React.Fragment>
              ))}
            </List>
            <Box sx={{ mt: 2, textAlign: 'center' }}>
              <Button variant="text" component={RouterLink} to="/tenant/students">
                View All Students
              </Button>
            </Box>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recent Transactions
            </Typography>
            <List>
              {stats.recentTransactions.map((transaction) => (
                <React.Fragment key={transaction.id}>
                  <ListItem alignItems="flex-start">
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'success.main' }}>
                        <MoneyIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={`$${transaction.amount} - ${transaction.course_name}`}
                      secondary={
                        <>
                          <Typography component="span" variant="body2" color="text.primary">
                            {transaction.student_name}
                          </Typography>
                          {` — ${transaction.date}`}
                        </>
                      }
                    />
                  </ListItem>
                  <Divider variant="inset" component="li" />
                </React.Fragment>
              ))}
            </List>
            <Box sx={{ mt: 2, textAlign: 'center' }}>
              <Button variant="text" component={RouterLink} to="/tenant/payments">
                View All Transactions
              </Button>
            </Box>
          </Paper>
        </Grid>
      </Grid>
      
      {/* App Suggestions */}
      <Paper sx={{ p: 3, mt: 4 }}>
        <Typography variant="h6" gutterBottom>
          Recommended Apps for Your Platform
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" component="div">
                  Certificate Generator
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Create beautiful certificates for your students upon course completion.
                </Typography>
                <Button
                  variant="outlined"
                  color="primary"
                  component={RouterLink}
                  to="/modules"
                  sx={{ mt: 2 }}
                  fullWidth
                >
                  Install
                </Button>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" component="div">
                  Discussion Forum
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Add interactive discussion forums to your courses to increase engagement.
                </Typography>
                <Button
                  variant="outlined"
                  color="primary"
                  component={RouterLink}
                  to="/modules"
                  sx={{ mt: 2 }}
                  fullWidth
                >
                  Install
                </Button>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" component="div">
                  Live Sessions
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Host live video sessions with your students using Zoom integration.
                </Typography>
                <Button
                  variant="outlined"
                  color="primary"
                  component={RouterLink}
                  to="/modules"
                  sx={{ mt: 2 }}
                  fullWidth
                >
                  Install
                </Button>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" component="div">
                  Analytics Pro
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Get detailed insights into student behavior and course performance.
                </Typography>
                <Button
                  variant="outlined"
                  color="primary"
                  component={RouterLink}
                  to="/modules"
                  sx={{ mt: 2 }}
                  fullWidth
                >
                  Install
                </Button>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  );
};

export default TenantDashboard;
