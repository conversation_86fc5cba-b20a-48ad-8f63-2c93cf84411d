# Advanced Customization Tools

The Advanced Customization Tools provide tenants with powerful capabilities to customize their LMS platform's appearance and functionality. These tools include a theme editor, template system, and page builder, allowing tenants to create a unique and branded experience for their users.

## Features

- Theme templates and customizations
- Visual theme editor with live preview
- Drag-and-drop page builder
- Component customization
- Variable substitution
- Template inheritance
- Custom page creation and management

## Implementation Details

### Database Structure

The Advanced Customization Tools are built on the following database tables:

- **ThemeTemplate**: Stores theme templates that can be customized
- **ThemeCustomization**: Stores tenant-specific customizations of theme templates
- **PageBuilderPage**: Stores pages created with the page builder

### API Endpoints

#### Theme Templates Endpoints

- `GET /api/themes/{themeId}/templates`: Get templates for a theme
- `POST /api/themes/{themeId}/templates`: Create a new template
- `GET /api/themes/{themeId}/templates/{id}`: Get a specific template
- `PUT /api/themes/{themeId}/templates/{id}`: Update a template
- `DELETE /api/themes/{themeId}/templates/{id}`: Delete a template

#### Theme Customizations Endpoints

- `GET /api/customizations`: Get customizations for the authenticated tenant
- `POST /api/customizations`: Create a new customization
- `GET /api/customizations/{id}`: Get a specific customization
- `PUT /api/customizations/{id}`: Update a customization
- `DELETE /api/customizations/{id}`: Delete a customization
- `POST /api/customizations/{id}/reset`: Reset a customization to the original template
- `POST /api/customizations/create-from-template`: Create a customization from a template

#### Page Builder Endpoints

- `GET /api/pages`: Get pages for the authenticated tenant
- `POST /api/pages`: Create a new page
- `GET /api/pages/{id}`: Get a specific page
- `PUT /api/pages/{id}`: Update a page
- `DELETE /api/pages/{id}`: Delete a page
- `POST /api/pages/{id}/publish`: Publish a page
- `POST /api/pages/{id}/unpublish`: Unpublish a page
- `POST /api/pages/{id}/set-as-homepage`: Set a page as the homepage
- `GET /api/homepage`: Get the homepage for the authenticated tenant

### Theme Customization System

The theme customization system allows tenants to customize their themes without modifying the original theme files. The system works as follows:

1. **Theme Templates**: Theme developers create templates for different parts of the theme (pages, sections, components, etc.)
2. **Customizations**: Tenants create customizations based on these templates
3. **Variable Substitution**: Templates can include variables that are replaced with tenant-specific values
4. **Rendering**: When a page is requested, the system renders the customized templates with the tenant's variables

### Page Builder

The page builder allows tenants to create and manage custom pages for their LMS platform. The system includes:

1. **Page Creation**: Tenants can create new pages with custom content
2. **Content Blocks**: Pages are built using content blocks (text, images, videos, etc.)
3. **Drag-and-Drop Interface**: Tenants can arrange content blocks using a drag-and-drop interface
4. **Publishing**: Pages can be published or unpublished
5. **Homepage**: Tenants can set a custom page as their homepage

## Usage

### For Theme Developers

1. Create theme templates for different parts of the theme
2. Define variables that can be customized by tenants
3. Create default settings for each template

### For Tenants

1. Customize theme templates to match their branding
2. Create custom pages using the page builder
3. Arrange content blocks to create unique layouts
4. Publish pages and set a custom homepage

## Future Enhancements

- Advanced CSS and JavaScript customization
- Custom component creation
- Template versioning and updates
- Import/export functionality
- More content block types
- Advanced layout options
