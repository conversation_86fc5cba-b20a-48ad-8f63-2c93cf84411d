<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Tenant;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class SignupController extends Controller
{
    /**
     * Display the signup page.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $plan = $request->query('plan', 'basic');
        
        return view('marketing.signup', compact('plan'));
    }

    /**
     * Handle the signup request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'institute_name' => 'required|string|max:255',
            'subdomain' => 'required|string|max:50|unique:tenants,domain|alpha_dash',
            'plan' => 'required|string|in:basic,pro,enterprise',
        ]);

        // Create user
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => 'tenant',
        ]);

        // Create tenant
        $tenant = Tenant::create([
            'name' => $request->institute_name,
            'domain' => $request->subdomain,
            'is_active' => true,
            'plan' => $request->plan,
        ]);

        // Associate user with tenant
        $user->tenant_id = $tenant->id;
        $user->save();

        // Log the user in
        auth()->login($user);

        // Redirect to onboarding
        return redirect()->route('tenant.onboarding');
    }
}
