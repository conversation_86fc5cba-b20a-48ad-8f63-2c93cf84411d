<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Marketing\HomeController;
use App\Http\Controllers\Marketing\PricingController;
use App\Http\Controllers\Marketing\FeaturesController;
use App\Http\Controllers\Marketing\ThemesController;
use App\Http\Controllers\Marketing\ExtensionsController;
use App\Http\Controllers\Marketing\MobileAppController;
use App\Http\Controllers\Marketing\SignupController;
use App\Http\Controllers\Marketing\DocsController;
use App\Http\Controllers\Marketing\AboutController;
use App\Http\Controllers\Marketing\ContactController;
use App\Http\Controllers\Marketing\BlogController;

// Admin Controllers
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\TenantController;
use App\Http\Controllers\Admin\SubscriptionController;
use App\Http\Controllers\Admin\ThemeController;
use App\Http\Controllers\Admin\SettingController;
use App\Http\Controllers\Admin\AnalyticsController;
use App\Http\Controllers\Admin\ExtensionController;
use App\Http\Controllers\Admin\SupportController;
use App\Http\Controllers\Auth\LoginController;

// Marketing Routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/pricing', [PricingController::class, 'index'])->name('pricing');
Route::get('/features', [FeaturesController::class, 'index'])->name('features');
Route::get('/themes', [ThemesController::class, 'index'])->name('themes');
Route::get('/extensions', [ExtensionsController::class, 'index'])->name('extensions');
Route::get('/mobile-app', [MobileAppController::class, 'index'])->name('mobile-app');
Route::get('/signup', [SignupController::class, 'index'])->name('signup');
Route::post('/signup', [SignupController::class, 'store'])->name('signup.store');
Route::get('/signup/success', [SignupController::class, 'success'])->name('signup.success');
Route::get('/docs', [DocsController::class, 'index'])->name('docs');
Route::get('/about', [AboutController::class, 'index'])->name('about');
Route::get('/contact', [ContactController::class, 'index'])->name('contact');
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');
Route::get('/blog', [BlogController::class, 'index'])->name('blog');
Route::get('/blog/{slug}', [BlogController::class, 'show'])->name('blog.show');

// Authentication Routes
Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [LoginController::class, 'login']);
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

// Admin Routes
Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {
    // Dashboard
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

    // Analytics
    Route::prefix('analytics')->name('analytics.')->group(function () {
        Route::get('/', [AnalyticsController::class, 'index'])->name('index');
        Route::get('/tenants', [AnalyticsController::class, 'tenants'])->name('tenants');
        Route::get('/users', [AnalyticsController::class, 'users'])->name('users');
        Route::get('/revenue', [AnalyticsController::class, 'revenue'])->name('revenue');
        Route::get('/usage', [AnalyticsController::class, 'usage'])->name('usage');
        Route::get('/export', [AnalyticsController::class, 'export'])->name('export');
    });

    // User Management
    Route::prefix('users')->name('users.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\UserController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\Admin\UserController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\Admin\UserController::class, 'store'])->name('store');
        Route::get('/{id}', [App\Http\Controllers\Admin\UserController::class, 'show'])->name('show');
        Route::get('/{id}/edit', [App\Http\Controllers\Admin\UserController::class, 'edit'])->name('edit');
        Route::put('/{id}', [App\Http\Controllers\Admin\UserController::class, 'update'])->name('update');
        Route::delete('/{id}', [App\Http\Controllers\Admin\UserController::class, 'destroy'])->name('destroy');
        Route::post('/{id}/activate', [App\Http\Controllers\Admin\UserController::class, 'activate'])->name('activate');
        Route::post('/{id}/deactivate', [App\Http\Controllers\Admin\UserController::class, 'deactivate'])->name('deactivate');
    });

    // Tenant Management
    Route::prefix('tenants')->name('tenants.')->group(function () {
        Route::get('/', [TenantController::class, 'index'])->name('index');
        Route::get('/create', [TenantController::class, 'create'])->name('create');
        Route::post('/', [TenantController::class, 'store'])->name('store');
        Route::get('/{tenant}', [TenantController::class, 'show'])->name('show');
        Route::get('/{tenant}/edit', [TenantController::class, 'edit'])->name('edit');
        Route::put('/{tenant}', [TenantController::class, 'update'])->name('update');
        Route::delete('/{tenant}', [TenantController::class, 'destroy'])->name('destroy');
        Route::post('/{tenant}/approve', [TenantController::class, 'approve'])->name('approve');
        Route::post('/{tenant}/suspend', [TenantController::class, 'suspend'])->name('suspend');
        Route::post('/{tenant}/restore', [TenantController::class, 'restore'])->name('restore');
    });

    // Subscription & Plans
    Route::prefix('plans')->name('plans.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\PlanController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\Admin\PlanController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\Admin\PlanController::class, 'store'])->name('store');
        Route::get('/{id}', [App\Http\Controllers\Admin\PlanController::class, 'show'])->name('show');
        Route::get('/{id}/edit', [App\Http\Controllers\Admin\PlanController::class, 'edit'])->name('edit');
        Route::put('/{id}', [App\Http\Controllers\Admin\PlanController::class, 'update'])->name('update');
        Route::delete('/{id}', [App\Http\Controllers\Admin\PlanController::class, 'destroy'])->name('destroy');
    });

    Route::prefix('subscriptions')->name('subscriptions.')->group(function () {
        Route::get('/', [SubscriptionController::class, 'index'])->name('index');
        Route::get('/{id}', [SubscriptionController::class, 'show'])->name('show');
        Route::post('/{id}/cancel', [SubscriptionController::class, 'cancel'])->name('cancel');
        Route::post('/{id}/resume', [SubscriptionController::class, 'resume'])->name('resume');
    });

    // Payments
    Route::prefix('payments')->name('payments.')->group(function () {
        Route::get('/', [DashboardController::class, 'payments'])->name('index');
        Route::get('/{id}', [DashboardController::class, 'showPayment'])->name('show');
        Route::get('/settings', [DashboardController::class, 'paymentSettings'])->name('settings');
    });

    // Themes & Templates
    Route::prefix('themes')->name('themes.')->group(function () {
        Route::get('/', [ThemeController::class, 'index'])->name('index');
        Route::get('/create', [ThemeController::class, 'create'])->name('create');
        Route::post('/', [ThemeController::class, 'store'])->name('store');
        Route::get('/{id}', [ThemeController::class, 'show'])->name('show');
        Route::get('/{id}/edit', [ThemeController::class, 'edit'])->name('edit');
        Route::put('/{id}', [ThemeController::class, 'update'])->name('update');
        Route::delete('/{id}', [ThemeController::class, 'destroy'])->name('destroy');
    });

    Route::prefix('templates')->name('templates.')->group(function () {
        Route::get('/', [DashboardController::class, 'templates'])->name('index');
        Route::get('/create', [DashboardController::class, 'createTemplate'])->name('create');
        Route::get('/{id}', [DashboardController::class, 'showTemplate'])->name('show');
        Route::get('/{id}/edit', [DashboardController::class, 'editTemplate'])->name('edit');
    });

    // Extensions
    Route::prefix('extensions')->name('extensions.')->group(function () {
        Route::get('/', [ExtensionController::class, 'index'])->name('index');
        Route::get('/create', [ExtensionController::class, 'create'])->name('create');
        Route::post('/', [ExtensionController::class, 'store'])->name('store');
        Route::get('/{id}', [ExtensionController::class, 'show'])->name('show');
        Route::get('/{id}/edit', [ExtensionController::class, 'edit'])->name('edit');
        Route::put('/{id}', [ExtensionController::class, 'update'])->name('update');
        Route::post('/{id}/enable', [ExtensionController::class, 'enable'])->name('enable');
        Route::post('/{id}/disable', [ExtensionController::class, 'disable'])->name('disable');
        Route::post('/{id}/plans', [ExtensionController::class, 'updatePlans'])->name('plans.update');
        Route::get('/marketplace', [ExtensionController::class, 'marketplace'])->name('marketplace');
        Route::get('/requests', [ExtensionController::class, 'requests'])->name('requests');
        Route::post('/requests/{id}/approve', [ExtensionController::class, 'approveRequest'])->name('requests.approve');
        Route::post('/requests/{id}/reject', [ExtensionController::class, 'rejectRequest'])->name('requests.reject');
    });

    // Settings
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\SettingsController::class, 'index'])->name('index');
        Route::post('/general', [App\Http\Controllers\Admin\SettingsController::class, 'updateGeneral'])->name('update-general');
        Route::post('/email', [App\Http\Controllers\Admin\SettingsController::class, 'updateEmail'])->name('update-email');
        Route::post('/payment', [App\Http\Controllers\Admin\SettingsController::class, 'updatePayment'])->name('update-payment');
        Route::post('/social-login', [App\Http\Controllers\Admin\SettingsController::class, 'updateSocialLogin'])->name('update-social-login');
        Route::post('/recaptcha', [App\Http\Controllers\Admin\SettingsController::class, 'updateRecaptcha'])->name('update-recaptcha');
        Route::get('/storage', [SettingController::class, 'storage'])->name('storage');
        Route::get('/security', [SettingController::class, 'security'])->name('security');
        Route::get('/api', [SettingController::class, 'api'])->name('api');
    });

    // Support & Tickets
    Route::prefix('support')->name('support.')->group(function () {
        Route::get('/', [SupportController::class, 'index'])->name('index');
        Route::get('/{id}', [SupportController::class, 'show'])->name('show');
        Route::post('/{id}/status', [SupportController::class, 'updateStatus'])->name('update-status');
        Route::post('/{id}/priority', [SupportController::class, 'updatePriority'])->name('update-priority');
        Route::post('/{id}/assign', [SupportController::class, 'assign'])->name('assign');
        Route::post('/{id}/reply', [SupportController::class, 'reply'])->name('reply');
        Route::get('/email-campaigns', [DashboardController::class, 'emailCampaigns'])->name('email-campaigns');
        Route::get('/email-campaigns/create', [DashboardController::class, 'createEmailCampaign'])->name('email-campaigns.create');
        Route::get('/email-campaigns/{id}', [DashboardController::class, 'showEmailCampaign'])->name('email-campaigns.show');
    });

    // Notifications
    Route::prefix('notifications')->name('notifications.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\NotificationController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\Admin\NotificationController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\Admin\NotificationController::class, 'store'])->name('store');
        Route::get('/{id}', [App\Http\Controllers\Admin\NotificationController::class, 'show'])->name('show');
        Route::delete('/{id}', [App\Http\Controllers\Admin\NotificationController::class, 'destroy'])->name('destroy');
        Route::post('/{id}/mark-as-read', [DashboardController::class, 'markNotificationAsRead'])->name('mark-as-read');
        Route::post('/mark-all-as-read', [DashboardController::class, 'markAllNotificationsAsRead'])->name('mark-all-as-read');
    });
});

// Test Route
Route::get('/test', function () {
    return view('test');
});

// Admin Login Info Route
Route::get('/admin-login-info', function () {
    return view('admin-login-info');
})->name('admin.login.info');

// Tenant Routes
Route::prefix('tenant')->name('tenant.')->group(function () {
    Route::get('{domain}/dashboard', [App\Http\Controllers\TenantController::class, 'dashboard'])->name('dashboard');
    Route::get('{domain}/courses', [App\Http\Controllers\TenantController::class, 'courses'])->name('courses');
    Route::get('{domain}/students', [App\Http\Controllers\TenantController::class, 'students'])->name('students');
    Route::get('{domain}/settings', [App\Http\Controllers\TenantController::class, 'settings'])->name('settings');

    // Custom Domain Settings
    Route::get('{domain}/settings/custom-domain', [App\Http\Controllers\TenantController::class, 'customDomain'])->name('settings.custom-domain');
    Route::post('{domain}/settings/custom-domain', [App\Http\Controllers\TenantController::class, 'updateCustomDomain'])->name('settings.custom-domain.update');
    Route::post('{domain}/settings/custom-domain/verify', [App\Http\Controllers\TenantController::class, 'verifyCustomDomain'])->name('settings.custom-domain.verify');
});

// Domain Availability Check
Route::post('/check-domain', [App\Http\Controllers\TenantController::class, 'checkDomain'])->name('check-domain');

// Tenant Frontend Routes (for custom domains)
Route::middleware(['tenant'])->group(function () {
    // These routes will be accessible via custom domains or subdomains
    Route::get('/', function (Request $request) {
        $tenant = $request->tenant;

        if (!$tenant) {
            return redirect(config('app.url'));
        }

        return view('tenant.frontend.home', compact('tenant'));
    })->name('tenant.frontend.home');

    Route::get('/courses', function (Request $request) {
        $tenant = $request->tenant;

        if (!$tenant) {
            return redirect(config('app.url'));
        }

        return view('tenant.frontend.courses', compact('tenant'));
    })->name('tenant.frontend.courses');

    Route::get('/dashboard', function (Request $request) {
        $tenant = $request->tenant;

        if (!$tenant) {
            return redirect(config('app.url'));
        }

        return view('tenant.dashboard', compact('tenant'));
    })->name('tenant.frontend.dashboard');
});
