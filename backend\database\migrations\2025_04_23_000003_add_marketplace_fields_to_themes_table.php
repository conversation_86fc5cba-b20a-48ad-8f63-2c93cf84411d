<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('themes', function (Blueprint $table) {
            $table->foreignId('category_id')->nullable()->constrained('marketplace_categories')->nullOnDelete();
            $table->decimal('price', 8, 2)->default(0.00);
            $table->integer('downloads')->default(0);
            $table->boolean('is_featured')->default(false);
            $table->json('screenshots')->nullable();
            $table->string('demo_url')->nullable();
            $table->enum('status', ['pending', 'approved', 'rejected'])->default('pending');
            $table->text('rejection_reason')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('themes', function (Blueprint $table) {
            $table->dropForeign(['category_id']);
            $table->dropColumn([
                'category_id',
                'price',
                'downloads',
                'is_featured',
                'screenshots',
                'demo_url',
                'status',
                'rejection_reason'
            ]);
        });
    }
};
