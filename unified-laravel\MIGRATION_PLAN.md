# Migration Plan: Consolidating Laravel Applications

This document outlines the step-by-step process for migrating from the current architecture (separate main-website and backend Laravel applications) to a unified Laravel application.

## 1. Preparation

- [x] Create a new Laravel project structure
- [x] Merge composer.json dependencies
- [x] Create unified route structure
- [x] Create unified models
- [x] Create unified database migrations

## 2. Database Migration

1. **Export Current Database Schemas**
   ```bash
   # From main-website directory
   php artisan schema:dump
   
   # From backend directory
   php artisan schema:dump
   ```

2. **Create a New Database**
   ```sql
   CREATE DATABASE lms_unified CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

3. **Run Migrations on the New Database**
   ```bash
   # From unified-laravel directory
   php artisan migrate
   ```

4. **Export Data from Existing Databases**
   ```bash
   # Export tenants from main-website
   mysqldump -u root -p lms_main_website --tables tenants > tenants_main.sql
   
   # Export tenants from backend
   mysqldump -u root -p lms_backend --tables tenants > tenants_backend.sql
   
   # Export other tables as needed
   ```

5. **Import and Merge Data**
   - Write a data migration script to merge tenant data
   - Resolve any conflicts in tenant domains or IDs
   - Import other data tables

## 3. Code Migration

### 3.1 Controllers

1. **Copy and Namespace Controllers**
   - Copy controllers from main-website to `app/Http/Controllers/Marketing/`
   - Copy controllers from backend to `app/Http/Controllers/API/`
   - Update namespaces and imports

2. **Resolve Controller Conflicts**
   - Identify controllers with the same name
   - Rename or merge as appropriate
   - Update route references

### 3.2 Models

1. **Merge Model Definitions**
   - Compare model attributes and merge them
   - Update relationships to reflect the unified database schema
   - Add any missing methods

### 3.3 Views

1. **Copy View Files**
   - Copy marketing views from main-website to `resources/views/marketing/`
   - Copy admin views from main-website to `resources/views/admin/`
   - Create tenant views in `resources/views/tenant/`

### 3.4 Assets

1. **Copy and Organize Assets**
   - Copy CSS, JavaScript, and image files
   - Organize into appropriate directories
   - Update asset references in views

## 4. Configuration

1. **Merge Configuration Files**
   - Compare and merge app.php configurations
   - Update database.php to use the unified database
   - Configure auth.php for JWT and session authentication
   - Set up proper filesystem configuration

2. **Environment Variables**
   - Create a comprehensive .env.example file
   - Document all required environment variables

## 5. Testing

1. **Test Core Functionality**
   - Test marketing website routes
   - Test API endpoints
   - Test admin dashboard
   - Test tenant dashboard

2. **Test Authentication Flows**
   - Test user registration and login
   - Test tenant registration
   - Test API authentication
   - Test role-based access control

3. **Test Database Operations**
   - Test CRUD operations on key models
   - Test relationships and queries

## 6. Deployment

1. **Prepare Production Environment**
   - Set up production server
   - Configure web server (Nginx/Apache)
   - Set up SSL certificates

2. **Deploy Unified Application**
   - Clone repository to production server
   - Install dependencies
   - Run migrations
   - Build frontend assets
   - Configure environment variables

3. **Migrate Production Data**
   - Export data from production databases
   - Import into the unified database
   - Verify data integrity

4. **Switch DNS Records**
   - Update DNS records to point to the new server
   - Set up proper redirects for any changed URLs

## 7. Post-Migration Tasks

1. **Monitor Application Performance**
   - Set up monitoring tools
   - Watch for errors or performance issues

2. **Update Documentation**
   - Update developer documentation
   - Update user guides
   - Document the new architecture

3. **Clean Up Old Resources**
   - Archive old repositories
   - Decommission old servers
   - Back up old databases

## Timeline

| Phase | Estimated Duration | Dependencies |
|-------|-------------------|--------------|
| Preparation | 1 week | None |
| Database Migration | 1 week | Preparation |
| Code Migration | 2 weeks | Database Migration |
| Configuration | 1 week | Code Migration |
| Testing | 2 weeks | Configuration |
| Deployment | 1 week | Testing |
| Post-Migration Tasks | 1 week | Deployment |

## Rollback Plan

In case of critical issues during migration:

1. Keep the old applications running in parallel during migration
2. Maintain database backups at each step
3. Prepare DNS rollback procedure
4. Document all changes for easy reversal

## Conclusion

This migration will result in a more maintainable, efficient, and scalable application architecture. By consolidating the Laravel applications, we eliminate data synchronization issues, simplify deployment, and create a more consistent user experience.
