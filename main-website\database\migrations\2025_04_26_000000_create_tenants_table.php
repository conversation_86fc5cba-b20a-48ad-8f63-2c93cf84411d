<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('tenants')) {
            Schema::create('tenants', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('domain')->unique();
                $table->text('description')->nullable();
                $table->string('logo')->nullable();
                $table->string('status')->default('active');
                $table->unsignedBigInteger('plan_id')->nullable();
                $table->unsignedBigInteger('owner_id')->nullable();
                $table->json('settings')->nullable();
                $table->unsignedBigInteger('theme_id')->nullable();
                $table->timestamp('expires_at')->nullable();
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tenants');
    }
};
