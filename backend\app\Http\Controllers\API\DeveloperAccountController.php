<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\DeveloperAccount;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class DeveloperAccountController extends Controller
{
    /**
     * Display the developer account for the authenticated user.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function show()
    {
        $user = Auth::user();
        $developerAccount = DeveloperAccount::where('user_id', $user->id)->first();

        if (!$developerAccount) {
            return response()->json([
                'success' => false,
                'message' => 'Developer account not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $developerAccount
        ]);
    }

    /**
     * Register a new developer account.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'company_name' => 'required|string|max:255',
            'website' => 'nullable|url|max:255',
            'bio' => 'nullable|string|max:1000',
            'logo' => 'nullable|string|max:255',
            'has_accepted_agreement' => 'required|boolean|accepted',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();

        // Check if user already has a developer account
        $existingAccount = DeveloperAccount::where('user_id', $user->id)->first();
        if ($existingAccount) {
            return response()->json([
                'success' => false,
                'message' => 'You already have a developer account'
            ], 400);
        }

        // Create developer account
        $developerAccount = new DeveloperAccount();
        $developerAccount->user_id = $user->id;
        $developerAccount->company_name = $request->input('company_name');
        $developerAccount->website = $request->input('website');
        $developerAccount->bio = $request->input('bio');
        $developerAccount->logo = $request->input('logo');
        $developerAccount->has_accepted_agreement = $request->input('has_accepted_agreement');
        $developerAccount->agreement_accepted_at = now();
        $developerAccount->status = 'pending';
        $developerAccount->save();

        // Generate API credentials
        $developerAccount->generateApiCredentials();

        return response()->json([
            'success' => true,
            'message' => 'Developer account registered successfully and pending approval',
            'data' => $developerAccount
        ], 201);
    }

    /**
     * Update the developer account for the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'company_name' => 'required|string|max:255',
            'website' => 'nullable|url|max:255',
            'bio' => 'nullable|string|max:1000',
            'logo' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $developerAccount = DeveloperAccount::where('user_id', $user->id)->first();

        if (!$developerAccount) {
            return response()->json([
                'success' => false,
                'message' => 'Developer account not found'
            ], 404);
        }

        // Update developer account
        $developerAccount->company_name = $request->input('company_name');
        $developerAccount->website = $request->input('website');
        $developerAccount->bio = $request->input('bio');
        $developerAccount->logo = $request->input('logo');
        $developerAccount->save();

        return response()->json([
            'success' => true,
            'message' => 'Developer account updated successfully',
            'data' => $developerAccount
        ]);
    }

    /**
     * Regenerate API credentials for the developer account.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function regenerateApiCredentials()
    {
        $user = Auth::user();
        $developerAccount = DeveloperAccount::where('user_id', $user->id)->first();

        if (!$developerAccount) {
            return response()->json([
                'success' => false,
                'message' => 'Developer account not found'
            ], 404);
        }

        // Check if developer account is approved
        if ($developerAccount->status !== 'approved') {
            return response()->json([
                'success' => false,
                'message' => 'Your developer account must be approved to generate API credentials'
            ], 400);
        }

        // Regenerate API credentials
        $developerAccount->generateApiCredentials();

        return response()->json([
            'success' => true,
            'message' => 'API credentials regenerated successfully',
            'data' => [
                'api_key' => $developerAccount->api_key,
                'api_secret' => $developerAccount->api_secret,
            ]
        ]);
    }

    /**
     * Approve a developer account (admin only).
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function approve($id)
    {
        $user = Auth::user();

        // Check if user is admin
        if ($user->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $developerAccount = DeveloperAccount::find($id);
        if (!$developerAccount) {
            return response()->json([
                'success' => false,
                'message' => 'Developer account not found'
            ], 404);
        }

        // Approve the developer account
        $developerAccount->status = 'approved';
        $developerAccount->is_verified = true;
        $developerAccount->save();

        return response()->json([
            'success' => true,
            'message' => 'Developer account approved successfully',
            'data' => $developerAccount
        ]);
    }

    /**
     * Reject a developer account (admin only).
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function reject(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'rejection_reason' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();

        // Check if user is admin
        if ($user->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $developerAccount = DeveloperAccount::find($id);
        if (!$developerAccount) {
            return response()->json([
                'success' => false,
                'message' => 'Developer account not found'
            ], 404);
        }

        // Reject the developer account
        $developerAccount->status = 'rejected';
        $developerAccount->rejection_reason = $request->input('rejection_reason');
        $developerAccount->save();

        return response()->json([
            'success' => true,
            'message' => 'Developer account rejected successfully',
            'data' => $developerAccount
        ]);
    }

    /**
     * Suspend a developer account (admin only).
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function suspend(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'rejection_reason' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();

        // Check if user is admin
        if ($user->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $developerAccount = DeveloperAccount::find($id);
        if (!$developerAccount) {
            return response()->json([
                'success' => false,
                'message' => 'Developer account not found'
            ], 404);
        }

        // Suspend the developer account
        $developerAccount->status = 'suspended';
        $developerAccount->rejection_reason = $request->input('rejection_reason');
        $developerAccount->save();

        return response()->json([
            'success' => true,
            'message' => 'Developer account suspended successfully',
            'data' => $developerAccount
        ]);
    }

    /**
     * List all developer accounts (admin only).
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        // Check if user is admin
        if ($user->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $status = $request->input('status');
        $query = DeveloperAccount::with('user');

        if ($status) {
            $query->where('status', $status);
        }

        $developerAccounts = $query->paginate(10);

        return response()->json([
            'success' => true,
            'data' => $developerAccounts
        ]);
    }
}
