import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

import Container from '@/components/ui/Container';
import Text from '@/components/ui/Text';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Card from '@/components/ui/Card';
import { useTheme } from '@/components/ThemeProvider';

// Mock FAQ data
const faqs = [
  {
    id: 1,
    question: 'How do I enroll in a course?',
    answer: 'To enroll in a course, navigate to the course details page and click the "Enroll" button. If it\'s a paid course, you\'ll be prompted to complete the payment process.'
  },
  {
    id: 2,
    question: 'How do I track my progress?',
    answer: 'Your course progress is automatically tracked as you complete lessons. You can view your overall progress on the "My Courses" page and detailed progress within each course.'
  },
  {
    id: 3,
    question: 'How do I get a certificate?',
    answer: 'Certificates are awarded automatically upon successful completion of a course. You can view and download your certificates from the "Certificates" page in your profile.'
  },
  {
    id: 4,
    question: 'Can I access courses offline?',
    answer: 'Currently, our platform requires an internet connection to access course content. We\'re working on an offline mode for future updates.'
  },
  {
    id: 5,
    question: 'How do I change my password?',
    answer: 'You can change your password in the Account Settings page. Click on "Change Password" and follow the instructions.'
  },
];

export default function HelpSupportScreen() {
  const { theme } = useTheme();
  const [expandedFaq, setExpandedFaq] = useState(null);
  const [contactSubject, setContactSubject] = useState('');
  const [contactMessage, setContactMessage] = useState('');
  const [loading, setLoading] = useState(false);

  const handleBack = () => {
    router.back();
  };

  const toggleFaq = (id) => {
    setExpandedFaq(expandedFaq === id ? null : id);
  };

  const handleSubmitContact = () => {
    if (!contactSubject.trim() || !contactMessage.trim()) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      setContactSubject('');
      setContactMessage('');
      Alert.alert(
        'Message Sent',
        'Thank you for contacting us. We\'ll get back to you as soon as possible.',
        [{ text: 'OK' }]
      );
    }, 1000);
  };

  return (
    <Container style={styles.container}>
      <TouchableOpacity style={styles.backButton} onPress={handleBack}>
        <Ionicons name="arrow-back" size={24} color={theme.text} />
      </TouchableOpacity>
      
      <Text variant="h2" style={styles.title}>Help & Support</Text>
      
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Text variant="h4" style={styles.sectionTitle}>Frequently Asked Questions</Text>
        
        {faqs.map((faq) => (
          <Card key={faq.id} style={styles.faqCard}>
            <TouchableOpacity 
              style={styles.faqHeader}
              onPress={() => toggleFaq(faq.id)}
              activeOpacity={0.7}
            >
              <Text variant="subtitle" style={styles.faqQuestion}>{faq.question}</Text>
              <Ionicons 
                name={expandedFaq === faq.id ? "chevron-up" : "chevron-down"} 
                size={20} 
                color={theme.text} 
              />
            </TouchableOpacity>
            
            {expandedFaq === faq.id && (
              <View style={styles.faqAnswer}>
                <Text variant="body">{faq.answer}</Text>
              </View>
            )}
          </Card>
        ))}
        
        <Text variant="h4" style={styles.sectionTitle}>Contact Support</Text>
        
        <Card style={styles.contactCard}>
          <Text variant="body" style={styles.contactText}>
            Need more help? Send us a message and we'll get back to you as soon as possible.
          </Text>
          
          <Input
            label="Subject"
            value={contactSubject}
            onChangeText={setContactSubject}
            placeholder="What do you need help with?"
            fullWidth
          />
          
          <Input
            label="Message"
            value={contactMessage}
            onChangeText={setContactMessage}
            placeholder="Describe your issue in detail"
            multiline
            numberOfLines={4}
            fullWidth
            style={styles.messageInput}
          />
          
          <Button 
            onPress={handleSubmitContact}
            loading={loading}
            style={styles.submitButton}
          >
            Send Message
          </Button>
        </Card>
        
        <Card style={styles.contactInfoCard}>
          <Text variant="subtitle" style={styles.contactInfoTitle}>Other Ways to Reach Us</Text>
          
          <View style={styles.contactMethod}>
            <Ionicons name="mail-outline" size={24} color={theme.primary} style={styles.contactIcon} />
            <Text variant="body"><EMAIL></Text>
          </View>
          
          <View style={styles.contactMethod}>
            <Ionicons name="call-outline" size={24} color={theme.primary} style={styles.contactIcon} />
            <Text variant="body">+1 (555) 123-4567</Text>
          </View>
          
          <View style={styles.contactMethod}>
            <Ionicons name="time-outline" size={24} color={theme.primary} style={styles.contactIcon} />
            <Text variant="body">Monday - Friday, 9am - 5pm EST</Text>
          </View>
        </Card>
      </ScrollView>
    </Container>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backButton: {
    position: 'absolute',
    top: 50,
    left: 16,
    zIndex: 10,
    padding: 8,
  },
  title: {
    marginTop: 60,
    marginBottom: 20,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  faqCard: {
    borderRadius: 12,
    marginBottom: 12,
    overflow: 'hidden',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  faqHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  faqQuestion: {
    flex: 1,
    marginRight: 8,
  },
  faqAnswer: {
    padding: 16,
    paddingTop: 0,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  contactCard: {
    borderRadius: 12,
    marginBottom: 20,
    padding: 16,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  contactText: {
    marginBottom: 16,
  },
  messageInput: {
    height: 120,
    textAlignVertical: 'top',
  },
  submitButton: {
    marginTop: 16,
  },
  contactInfoCard: {
    borderRadius: 12,
    marginBottom: 20,
    padding: 16,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  contactInfoTitle: {
    marginBottom: 16,
  },
  contactMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  contactIcon: {
    marginRight: 12,
  },
});
