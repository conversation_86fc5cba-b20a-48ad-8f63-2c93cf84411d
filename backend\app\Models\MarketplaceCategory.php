<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MarketplaceCategory extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
        'icon',
        'order',
        'is_active',
        'type',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'order' => 'integer',
    ];

    /**
     * Get the themes in this category.
     */
    public function themes(): HasMany
    {
        return $this->hasMany(Theme::class, 'category_id');
    }

    /**
     * Get the modules in this category.
     */
    public function modules(): HasMany
    {
        return $this->hasMany(Module::class, 'category_id');
    }

    /**
     * Scope a query to only include active categories.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include categories of a specific type.
     */
    public function scopeOfType($query, $type)
    {
        if ($type === 'both') {
            return $query;
        }
        
        return $query->where(function($q) use ($type) {
            $q->where('type', $type)
              ->orWhere('type', 'both');
        });
    }
}
