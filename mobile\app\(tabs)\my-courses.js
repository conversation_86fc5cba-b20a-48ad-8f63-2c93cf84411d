import React, { useState, useEffect, useContext } from 'react';
import { FlatList, View, StyleSheet, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';

import Container from '@/components/ui/Container';
import Text from '@/components/ui/Text';
import Button from '@/components/ui/Button';
import CourseCard from '@/components/CourseCard';
import { AuthContext } from '@/context/AuthContext';
import { courseService } from '@/services/api';
import { useTheme } from '@/components/ThemeProvider';

export default function MyCoursesScreen() {
  const [enrolledCourses, setEnrolledCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { isAuthenticated, user } = useContext(AuthContext);
  const { theme } = useTheme();

  useEffect(() => {
    if (isAuthenticated) {
      fetchEnrolledCourses();
    } else {
      setLoading(false);
    }
  }, [isAuthenticated]);

  const fetchEnrolledCourses = async () => {
    try {
      setLoading(true);
      // In a real app, you would fetch only enrolled courses
      // For now, we'll use the mock data and filter for demonstration
      const response = await courseService.getAllCourses();
      
      // Simulate enrolled courses (in mock mode, we'll just take the first 3 courses)
      const courses = response.data.slice(0, 3);
      courses.forEach(course => {
        course.progress = Math.floor(Math.random() * 100); // Random progress for demo
        course.lastAccessed = new Date(Date.now() - Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000)).toISOString(); // Random date within last week
      });
      
      setEnrolledCourses(courses);
      setError(null);
    } catch (error) {
      console.error('Error fetching enrolled courses:', error);
      setError('Failed to load your courses. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleContinueLearning = (courseId) => {
    router.push(`/course/${courseId}/learn`);
  };

  const renderCourseItem = ({ item }) => (
    <View style={styles.courseItem}>
      <CourseCard course={item} compact />
      
      <View style={styles.progressContainer}>
        <View style={styles.progressInfo}>
          <Text variant="caption">Progress: {item.progress}%</Text>
          <View style={[styles.progressBarContainer, { backgroundColor: theme.border }]}>
            <View 
              style={[
                styles.progressBar, 
                { 
                  width: `${item.progress}%`,
                  backgroundColor: theme.secondary
                }
              ]} 
            />
          </View>
        </View>
        
        <Button 
          variant="secondary" 
          onPress={() => handleContinueLearning(item.id)}
        >
          Continue
        </Button>
      </View>
    </View>
  );

  if (!isAuthenticated) {
    return (
      <Container style={styles.container}>
        <View style={styles.authContainer}>
          <Text variant="h2" style={styles.title}>My Courses</Text>
          <Text style={styles.message}>Please log in to view your enrolled courses.</Text>
          <Button 
            onPress={() => router.push('/login')}
            style={styles.loginButton}
          >
            Log In
          </Button>
        </View>
      </Container>
    );
  }

  return (
    <Container style={styles.container}>
      <Text variant="h2" style={styles.title}>My Courses</Text>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.primary} />
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <Button onPress={fetchEnrolledCourses}>Try Again</Button>
        </View>
      ) : enrolledCourses.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>You haven't enrolled in any courses yet.</Text>
          <Button 
            onPress={() => router.push('/explore')}
            style={styles.exploreButton}
          >
            Explore Courses
          </Button>
        </View>
      ) : (
        <FlatList
          data={enrolledCourses}
          renderItem={renderCourseItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
        />
      )}
    </Container>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  title: {
    marginBottom: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    marginBottom: 16,
    textAlign: 'center',
  },
  exploreButton: {
    marginTop: 16,
  },
  authContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  message: {
    marginBottom: 24,
    textAlign: 'center',
  },
  loginButton: {
    minWidth: 150,
  },
  listContent: {
    paddingBottom: 20,
  },
  courseItem: {
    marginBottom: 16,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 8,
    paddingHorizontal: 8,
  },
  progressInfo: {
    flex: 1,
    marginRight: 16,
  },
  progressBarContainer: {
    height: 6,
    borderRadius: 3,
    marginTop: 4,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 3,
  },
});
