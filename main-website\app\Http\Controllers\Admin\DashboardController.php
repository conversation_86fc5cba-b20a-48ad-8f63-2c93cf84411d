<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\View;

class DashboardController extends Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        // Check if user is admin for all methods
        $this->middleware(function ($request, $next) {
            if (!Auth::check() || !Auth::user()->isAdmin()) {
                return redirect()->route('login')->with('error', 'You do not have permission to access this area.');
            }
            return $next($request);
        });
    }

    /**
     * Display the admin dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get counts for dashboard stats
        $stats = [
            'users' => [
                'total' => User::count(),
                'admins' => User::where('role', 'admin')->count(),
                'tenants' => 0,
                'students' => 0,
            ],
            'tenants' => [
                'total' => 0,
                'active' => 0,
                'pending' => 0,
                'suspended' => 0,
            ],
            'subscriptions' => [
                'total' => 0,
                'active' => 0,
                'canceled' => 0,
                'trial' => 0,
            ],
            'payments' => [
                'total' => 0,
                'successful' => 0,
                'pending' => 0,
                'failed' => 0,
                'revenue' => 0,
            ],
            'tickets' => [
                'total' => 0,
                'open' => 0,
                'pending' => 0,
                'closed' => 0,
            ],
        ];

        // Mock data for charts
        $tenantSignups = collect([
            (object) ['month' => 1, 'year' => 2023, 'count' => 5, 'month_name' => 'January'],
            (object) ['month' => 2, 'year' => 2023, 'count' => 8, 'month_name' => 'February'],
            (object) ['month' => 3, 'year' => 2023, 'count' => 12, 'month_name' => 'March'],
            (object) ['month' => 4, 'year' => 2023, 'count' => 10, 'month_name' => 'April'],
            (object) ['month' => 5, 'year' => 2023, 'count' => 15, 'month_name' => 'May'],
            (object) ['month' => 6, 'year' => 2023, 'count' => 20, 'month_name' => 'June'],
        ]);

        $revenueByMonth = collect([
            (object) ['month' => 1, 'year' => 2023, 'total' => 1500, 'month_name' => 'January'],
            (object) ['month' => 2, 'year' => 2023, 'total' => 2200, 'month_name' => 'February'],
            (object) ['month' => 3, 'year' => 2023, 'total' => 3000, 'month_name' => 'March'],
            (object) ['month' => 4, 'year' => 2023, 'total' => 2800, 'month_name' => 'April'],
            (object) ['month' => 5, 'year' => 2023, 'total' => 3500, 'month_name' => 'May'],
            (object) ['month' => 6, 'year' => 2023, 'total' => 4200, 'month_name' => 'June'],
        ]);

        // Mock data for recent items
        $recentTenants = collect([
            (object) [
                'id' => 1,
                'name' => 'Acme Inc.',
                'domain' => 'acme.naxofy.com',
                'logo' => null,
                'created_at' => now()->subDays(2),
                'owner' => (object) ['name' => 'John Doe'],
                'title' => 'CEO', // Added missing title property
            ],
            (object) [
                'id' => 2,
                'name' => 'XYZ Corp',
                'domain' => 'xyz.naxofy.com',
                'logo' => null,
                'created_at' => now()->subDays(5),
                'owner' => (object) ['name' => 'Jane Smith'],
                'title' => 'CTO', // Added missing title property
            ],
            (object) [
                'id' => 3,
                'name' => 'ABC Ltd',
                'domain' => 'abc.naxofy.com',
                'logo' => null,
                'created_at' => now()->subDays(7),
                'owner' => (object) ['name' => 'Bob Johnson'],
                'title' => 'Director', // Added missing title property
            ],
        ]);

        $recentPayments = collect([
            (object) [
                'id' => 1,
                'amount' => 199.00,
                'status' => 'succeeded',
                'created_at' => now()->subHours(2),
                'tenant' => (object) ['name' => 'Acme Inc.'],
            ],
            (object) [
                'id' => 2,
                'amount' => 299.00,
                'status' => 'succeeded',
                'created_at' => now()->subHours(5),
                'tenant' => (object) ['name' => 'XYZ Corp'],
            ],
            (object) [
                'id' => 3,
                'amount' => 99.00,
                'status' => 'pending',
                'created_at' => now()->subHours(8),
                'tenant' => (object) ['name' => 'ABC Ltd'],
            ],
        ]);

        $recentTickets = collect([
            (object) [
                'id' => 1,
                'subject' => 'Need help with course upload',
                'status' => 'open',
                'created_at' => now()->subHours(3),
                'tenant' => (object) ['name' => 'Acme Inc.'],
                'user' => (object) ['name' => 'John Doe'],
            ],
            (object) [
                'id' => 2,
                'subject' => 'Payment issue',
                'status' => 'pending',
                'created_at' => now()->subHours(6),
                'tenant' => (object) ['name' => 'XYZ Corp'],
                'user' => (object) ['name' => 'Jane Smith'],
            ],
            (object) [
                'id' => 3,
                'subject' => 'Custom domain setup',
                'status' => 'closed',
                'created_at' => now()->subHours(12),
                'tenant' => (object) ['name' => 'ABC Ltd'],
                'user' => (object) ['name' => 'Bob Johnson'],
            ],
        ]);

        return view('admin.dashboard.index', compact(
            'stats',
            'recentTenants',
            'recentPayments',
            'recentTickets',
            'tenantSignups',
            'revenueByMonth'
        ));
    }

    /**
     * Display the tenants list.
     *
     * @return \Illuminate\View\View
     */
    public function tenants()
    {
        // Mock data for tenants
        $tenants = collect([
            (object) [
                'id' => 1,
                'name' => 'Acme Inc.',
                'domain' => 'acme.naxofy.com',
                'status' => 'active',
                'plan' => 'Premium',
                'owner' => 'John Doe',
                'created_at' => now()->subDays(2),
            ],
            (object) [
                'id' => 2,
                'name' => 'XYZ Corp',
                'domain' => 'xyz.naxofy.com',
                'status' => 'active',
                'plan' => 'Basic',
                'owner' => 'Jane Smith',
                'created_at' => now()->subDays(5),
            ],
            (object) [
                'id' => 3,
                'name' => 'ABC Ltd',
                'domain' => 'abc.naxofy.com',
                'status' => 'pending',
                'plan' => 'Standard',
                'owner' => 'Bob Johnson',
                'created_at' => now()->subDays(7),
            ],
            (object) [
                'id' => 4,
                'name' => 'Tech Solutions',
                'domain' => 'techsolutions.naxofy.com',
                'status' => 'active',
                'plan' => 'Premium',
                'owner' => 'Alice Brown',
                'created_at' => now()->subDays(10),
            ],
            (object) [
                'id' => 5,
                'name' => 'Global Education',
                'domain' => 'globaledu.naxofy.com',
                'status' => 'suspended',
                'plan' => 'Premium',
                'owner' => 'Charlie Wilson',
                'created_at' => now()->subDays(15),
            ],
        ]);

        return view('admin.tenants.index', compact('tenants'));
    }

    /**
     * Display the tenant creation form.
     *
     * @return \Illuminate\View\View
     */
    public function createTenant()
    {
        return view('admin.tenants.create');
    }

    /**
     * Display the tenant details.
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function showTenant($id)
    {
        // Mock data for tenant
        $tenant = (object) [
            'id' => $id,
            'name' => 'Acme Inc.',
            'domain' => 'acme.naxofy.com',
            'status' => 'active',
            'plan' => 'Premium',
            'owner' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+****************',
            'address' => '123 Main St, New York, NY 10001',
            'created_at' => now()->subDays(2),
            'expires_at' => now()->addMonths(12),
            'students_count' => 250,
            'courses_count' => 15,
            'storage_used' => '2.5 GB',
            'last_login' => now()->subHours(5),
        ];

        return view('admin.tenants.show', compact('tenant'));
    }

    /**
     * Display the tenant edit form.
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function editTenant($id)
    {
        // Mock data for tenant
        $tenant = (object) [
            'id' => $id,
            'name' => 'Acme Inc.',
            'domain' => 'acme.naxofy.com',
            'status' => 'active',
            'plan' => 'Premium',
            'owner' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+****************',
            'address' => '123 Main St, New York, NY 10001',
        ];

        return view('admin.tenants.edit', compact('tenant'));
    }

    /**
     * Display the users list.
     *
     * @return \Illuminate\View\View
     */
    public function users()
    {
        // Get real users from database
        $users = User::all();

        return view('admin.users.index', compact('users'));
    }

    /**
     * Display the user creation form.
     *
     * @return \Illuminate\View\View
     */
    public function createUser()
    {
        return view('admin.users.create');
    }

    /**
     * Display the user details.
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function showUser($id)
    {
        // Get real user from database
        $user = User::findOrFail($id);

        return view('admin.users.show', compact('user'));
    }

    /**
     * Display the user edit form.
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function editUser($id)
    {
        // Get real user from database
        $user = User::findOrFail($id);

        return view('admin.users.edit', compact('user'));
    }

    /**
     * Display the plans list.
     *
     * @return \Illuminate\View\View
     */
    public function plans()
    {
        // Mock data for plans
        $plans = collect([
            (object) [
                'id' => 1,
                'name' => 'Basic',
                'price' => 9.99,
                'interval' => 'monthly',
                'features' => ['5 courses', '100 students', '1 GB storage', 'Email support'],
                'is_active' => true,
                'is_featured' => false,
                'tenants_count' => 25,
            ],
            (object) [
                'id' => 2,
                'name' => 'Standard',
                'price' => 29.99,
                'interval' => 'monthly',
                'features' => ['20 courses', '500 students', '5 GB storage', 'Priority support', 'Custom domain'],
                'is_active' => true,
                'is_featured' => true,
                'tenants_count' => 42,
            ],
            (object) [
                'id' => 3,
                'name' => 'Premium',
                'price' => 99.99,
                'interval' => 'monthly',
                'features' => ['Unlimited courses', 'Unlimited students', '20 GB storage', 'Premium support', 'Custom domain', 'White label', 'API access'],
                'is_active' => true,
                'is_featured' => false,
                'tenants_count' => 18,
            ],
        ]);

        return view('admin.plans.index', compact('plans'));
    }

    /**
     * Display the settings page.
     *
     * @return \Illuminate\View\View
     */
    public function settings()
    {
        // Mock data for settings
        $settings = (object) [
            'site_name' => 'Naxofy',
            'site_description' => 'The ultimate LMS platform',
            'site_logo' => 'images/logo.png',
            'primary_color' => '#ff7700',
            'secondary_color' => '#0369a1',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+****************',
            'address' => '123 Main St, New York, NY 10001',
            'social_links' => [
                'facebook' => 'https://facebook.com/naxofy',
                'twitter' => 'https://twitter.com/naxofy',
                'instagram' => 'https://instagram.com/naxofy',
                'linkedin' => 'https://linkedin.com/company/naxofy',
            ],
        ];

        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Display the notifications list.
     *
     * @return \Illuminate\View\View
     */
    public function notifications()
    {
        // Initialize notifications in session if not exists
        if (!Session::has('notifications')) {
            $this->initializeNotifications();
        }

        // Get notifications from session
        $notifications = collect(Session::get('notifications'));

        // Stats for notification counts
        $notificationStats = [
            'total' => $notifications->count(),
            'unread' => $notifications->where('is_read', false)->count(),
            'read' => $notifications->where('is_read', true)->count(),
        ];

        return view('admin.notifications.index', compact('notifications', 'notificationStats'));
    }

    /**
     * Initialize notifications in session.
     */
    private function initializeNotifications()
    {
        // Mock data for notifications
        $notifications = [
            (object) [
                'id' => 1,
                'type' => 'tenant_created',
                'title' => 'New Tenant Registered',
                'message' => 'Acme Inc. has registered as a new tenant.',
                'is_read' => false,
                'created_at' => now()->subHours(2),
            ],
            (object) [
                'id' => 2,
                'type' => 'payment_received',
                'title' => 'Payment Received',
                'message' => 'Payment of $99.99 received from XYZ Corp.',
                'is_read' => true,
                'created_at' => now()->subHours(5),
            ],
            (object) [
                'id' => 3,
                'type' => 'subscription_canceled',
                'title' => 'Subscription Canceled',
                'message' => 'ABC Ltd has canceled their Premium subscription.',
                'is_read' => false,
                'created_at' => now()->subHours(8),
            ],
            (object) [
                'id' => 4,
                'type' => 'support_ticket',
                'title' => 'New Support Ticket',
                'message' => 'Tech Solutions has opened a new support ticket: "Cannot access course content".',
                'is_read' => false,
                'created_at' => now()->subHours(12),
            ],
            (object) [
                'id' => 5,
                'type' => 'tenant_updated',
                'title' => 'Tenant Updated Plan',
                'message' => 'Global Education has upgraded to Premium plan.',
                'is_read' => true,
                'created_at' => now()->subDays(1),
            ],
            (object) [
                'id' => 6,
                'type' => 'system_alert',
                'title' => 'System Update Required',
                'message' => 'A new system update is available. Please update to the latest version.',
                'is_read' => false,
                'created_at' => now()->subDays(2),
            ],
            (object) [
                'id' => 7,
                'type' => 'storage_alert',
                'title' => 'Storage Limit Warning',
                'message' => 'Acme Inc. is approaching their storage limit (85% used).',
                'is_read' => true,
                'created_at' => now()->subDays(3),
            ],
            (object) [
                'id' => 8,
                'type' => 'payment_failed',
                'title' => 'Payment Failed',
                'message' => 'Payment from Tech Solutions failed due to expired card.',
                'is_read' => false,
                'created_at' => now()->subDays(4),
            ],
            (object) [
                'id' => 9,
                'type' => 'new_feature',
                'title' => 'New Feature Available',
                'message' => 'A new feature "Advanced Analytics" is now available for all tenants.',
                'is_read' => true,
                'created_at' => now()->subDays(5),
            ],
            (object) [
                'id' => 10,
                'type' => 'security_alert',
                'title' => 'Security Alert',
                'message' => 'Multiple failed login attempts detected for admin account.',
                'is_read' => false,
                'created_at' => now()->subDays(6),
            ],
        ];

        // Store notifications in session
        Session::put('notifications', $notifications);
    }

    /**
     * Display a specific notification.
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function showNotification($id)
    {
        // Initialize notifications in session if not exists
        if (!Session::has('notifications')) {
            $this->initializeNotifications();
        }

        // Get notifications from session
        $notifications = collect(Session::get('notifications'));

        // Find the notification by ID
        $notification = $notifications->firstWhere('id', (int)$id);

        if (!$notification) {
            return redirect()->route('admin.notifications.index')->with('error', 'Notification not found.');
        }

        // Add additional details for the notification
        $notification->details = $this->getNotificationDetails($notification);
        $notification->related_entity = $this->getRelatedEntity($notification);

        return view('admin.notifications.show', compact('notification'));
    }

    /**
     * Get detailed information for a notification.
     *
     * @param object $notification
     * @return string
     */
    private function getNotificationDetails($notification)
    {
        switch ($notification->type) {
            case 'tenant_created':
                return 'Acme Inc. has registered as a new tenant on the platform. They have selected the Premium plan with a 12-month subscription. The tenant owner is John Doe (<EMAIL>). Their domain is acme.naxofy.com.';

            case 'payment_received':
                return 'XYZ Corp has made a payment of $99.99 for their Standard plan subscription. The payment was processed via Stripe on ' . now()->subHours(5)->format('M d, Y H:i:s') . '. The transaction ID is TXID-' . rand(10000, 99999) . '.';

            case 'subscription_canceled':
                return 'ABC Ltd has canceled their Premium subscription effective from ' . now()->addDays(30)->format('M d, Y') . '. The reason provided was "Cost cutting measures". They have been a customer for 7 months.';

            case 'support_ticket':
                return 'Tech Solutions has opened a new support ticket with the subject "Cannot access course content". The ticket was created by Alice Brown (<EMAIL>). Priority: High. The issue is related to course ID 123.';

            case 'tenant_updated':
                return 'Global Education has upgraded from Standard to Premium plan. The upgrade was processed on ' . now()->subHours(12)->format('M d, Y H:i:s') . '. The new billing cycle will start on ' . now()->addDays(15)->format('M d, Y') . '.';

            case 'system_alert':
                return 'A new system update (v2.5.0) is available. This update includes security patches and new features. It is recommended to update as soon as possible. The update can be applied from the System Maintenance section.';

            case 'storage_alert':
                return 'Acme Inc. is approaching their storage limit (85% used). The current storage usage is 8.5 GB out of 10 GB. If they exceed their storage limit, they will not be able to upload new files. Consider upgrading their plan or contacting them to clean up unused files.';

            case 'payment_failed':
                return 'Payment from Tech Solutions failed due to expired card. The payment was attempted on ' . now()->subDays(4)->format('M d, Y H:i:s') . '. The amount was $99.99 for their Premium plan subscription. Please contact them to update their payment information.';

            case 'new_feature':
                return 'A new feature "Advanced Analytics" is now available for all tenants. This feature provides detailed insights into student engagement, course completion rates, and more. It is available for all plans at no additional cost.';

            case 'security_alert':
                return 'Multiple failed login attempts detected for admin account. There were 5 failed login attempts from IP address *********** on ' . now()->subDays(6)->format('M d, Y H:i:s') . '. The account has been temporarily locked for security reasons.';

            default:
                return 'No additional details available.';
        }
    }

    /**
     * Get related entity information for a notification.
     *
     * @param object $notification
     * @return array
     */
    private function getRelatedEntity($notification)
    {
        switch ($notification->type) {
            case 'tenant_created':
            case 'tenant_updated':
                return [
                    'type' => 'tenant',
                    'id' => $notification->id,
                    'name' => $notification->type === 'tenant_created' ? 'Acme Inc.' : 'Global Education',
                    'url' => route('admin.tenants.show', $notification->id),
                ];

            case 'payment_received':
            case 'payment_failed':
                return [
                    'type' => 'payment',
                    'id' => $notification->id,
                    'name' => 'Payment #' . rand(1000, 9999),
                    'url' => route('admin.payments.show', $notification->id),
                ];

            case 'subscription_canceled':
                return [
                    'type' => 'subscription',
                    'id' => $notification->id,
                    'name' => 'Subscription #' . rand(1000, 9999),
                    'url' => route('admin.subscriptions.show', $notification->id),
                ];

            case 'support_ticket':
                return [
                    'type' => 'support_ticket',
                    'id' => $notification->id,
                    'name' => 'Ticket #' . rand(1000, 9999),
                    'url' => route('admin.support.tickets.show', $notification->id),
                ];

            case 'system_alert':
            case 'new_feature':
                return [
                    'type' => 'system',
                    'id' => $notification->id,
                    'name' => $notification->type === 'system_alert' ? 'System Update v2.5.0' : 'Advanced Analytics Feature',
                    'url' => '#',
                ];

            case 'storage_alert':
                return [
                    'type' => 'tenant',
                    'id' => $notification->id,
                    'name' => 'Acme Inc.',
                    'url' => route('admin.tenants.show', $notification->id),
                ];

            case 'security_alert':
                return [
                    'type' => 'security',
                    'id' => $notification->id,
                    'name' => 'Security Log',
                    'url' => '#',
                ];

            default:
                return [
                    'type' => 'unknown',
                    'id' => $notification->id,
                    'name' => 'Unknown',
                    'url' => '#',
                ];
        }
    }

    /**
     * Mark a notification as read.
     *
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function markNotificationAsRead($id)
    {
        // Initialize notifications in session if not exists
        if (!Session::has('notifications')) {
            $this->initializeNotifications();
        }

        // Get notifications from session
        $notifications = collect(Session::get('notifications'));

        // Find the notification by ID and mark it as read
        $notifications = $notifications->map(function ($notification) use ($id) {
            if ($notification->id == $id) {
                $notification->is_read = true;
            }
            return $notification;
        });

        // Update notifications in session
        Session::put('notifications', $notifications->all());

        return redirect()->back()->with('success', 'Notification marked as read.');
    }

    /**
     * Mark all notifications as read.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function markAllNotificationsAsRead()
    {
        // Initialize notifications in session if not exists
        if (!Session::has('notifications')) {
            $this->initializeNotifications();
        }

        // Get notifications from session
        $notifications = collect(Session::get('notifications'));

        // Mark all notifications as read
        $notifications = $notifications->map(function ($notification) {
            $notification->is_read = true;
            return $notification;
        });

        // Update notifications in session
        Session::put('notifications', $notifications->all());

        return redirect()->back()->with('success', 'All notifications marked as read.');
    }

    /**
     * Delete a notification.
     *
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function deleteNotification($id)
    {
        // Initialize notifications in session if not exists
        if (!Session::has('notifications')) {
            $this->initializeNotifications();
        }

        // Get notifications from session
        $notifications = collect(Session::get('notifications'));

        // Remove the notification by ID
        $notifications = $notifications->reject(function ($notification) use ($id) {
            return $notification->id == $id;
        });

        // Update notifications in session
        Session::put('notifications', $notifications->all());

        return redirect()->route('admin.notifications.index')->with('success', 'Notification deleted successfully.');
    }

    /**
     * Display the extensions list.
     *
     * @return \Illuminate\View\View
     */
    public function extensions()
    {
        // Mock data for extensions
        $extensions = collect([
            (object) [
                'id' => 1,
                'name' => 'Live Class',
                'slug' => 'live-class',
                'description' => 'Enable real-time virtual classrooms with video conferencing, screen sharing, and interactive whiteboard.',
                'version' => '1.2.0',
                'author' => 'Naxofy',
                'status' => 'active',
                'is_featured' => true,
                'icon' => 'video-camera',
                'installations' => 245,
                'rating' => 4.8,
                'created_at' => now()->subMonths(3),
                'updated_at' => now()->subWeeks(2),
                'plans' => ['Premium', 'Standard'],
            ],
            (object) [
                'id' => 2,
                'name' => 'Quiz Builder',
                'slug' => 'quiz-builder',
                'description' => 'Create interactive quizzes with multiple question types, automatic grading, and detailed analytics.',
                'version' => '2.1.0',
                'author' => 'Naxofy',
                'status' => 'active',
                'is_featured' => false,
                'icon' => 'academic-cap',
                'installations' => 312,
                'rating' => 4.6,
                'created_at' => now()->subMonths(5),
                'updated_at' => now()->subWeeks(1),
                'plans' => ['Premium', 'Standard', 'Basic'],
            ],
            (object) [
                'id' => 3,
                'name' => 'Certificate Generator',
                'slug' => 'certificate-generator',
                'description' => 'Automatically generate and issue certificates upon course completion with customizable templates.',
                'version' => '1.5.0',
                'author' => 'EduTech Solutions',
                'status' => 'active',
                'is_featured' => false,
                'icon' => 'document-text',
                'installations' => 189,
                'rating' => 4.5,
                'created_at' => now()->subMonths(2),
                'updated_at' => now()->subDays(10),
                'plans' => ['Premium'],
            ],
            (object) [
                'id' => 4,
                'name' => 'Discussion Forum',
                'slug' => 'discussion-forum',
                'description' => 'Add course-specific discussion forums for student collaboration and instructor moderation.',
                'version' => '1.0.2',
                'author' => 'Community Builders',
                'status' => 'pending',
                'is_featured' => false,
                'icon' => 'chat-alt',
                'installations' => 0,
                'rating' => 0,
                'created_at' => now()->subWeeks(2),
                'updated_at' => now()->subWeeks(2),
                'plans' => [],
            ],
            (object) [
                'id' => 5,
                'name' => 'Assignment Submission',
                'slug' => 'assignment-submission',
                'description' => 'Allow students to submit assignments with file uploads and receive instructor feedback.',
                'version' => '1.3.1',
                'author' => 'Naxofy',
                'status' => 'active',
                'is_featured' => true,
                'icon' => 'clipboard-check',
                'installations' => 278,
                'rating' => 4.7,
                'created_at' => now()->subMonths(4),
                'updated_at' => now()->subWeeks(3),
                'plans' => ['Premium', 'Standard'],
            ],
            (object) [
                'id' => 6,
                'name' => 'Progress Tracker',
                'slug' => 'progress-tracker',
                'description' => 'Track and visualize student progress through courses with detailed analytics and reports.',
                'version' => '2.0.0',
                'author' => 'Data Insights',
                'status' => 'active',
                'is_featured' => false,
                'icon' => 'chart-bar',
                'installations' => 156,
                'rating' => 4.3,
                'created_at' => now()->subMonths(6),
                'updated_at' => now()->subMonths(1),
                'plans' => ['Premium', 'Standard'],
            ],
            (object) [
                'id' => 7,
                'name' => 'Gamification',
                'slug' => 'gamification',
                'description' => 'Add points, badges, and leaderboards to increase student engagement and motivation.',
                'version' => '1.1.0',
                'author' => 'Engagement Plus',
                'status' => 'inactive',
                'is_featured' => false,
                'icon' => 'sparkles',
                'installations' => 98,
                'rating' => 4.2,
                'created_at' => now()->subMonths(7),
                'updated_at' => now()->subMonths(2),
                'plans' => ['Premium'],
            ],
            (object) [
                'id' => 8,
                'name' => 'Social Learning',
                'slug' => 'social-learning',
                'description' => 'Integrate social media features like profiles, activity feeds, and direct messaging.',
                'version' => '1.0.0',
                'author' => 'Social Edu',
                'status' => 'pending',
                'is_featured' => false,
                'icon' => 'users',
                'installations' => 0,
                'rating' => 0,
                'created_at' => now()->subWeeks(3),
                'updated_at' => now()->subWeeks(3),
                'plans' => [],
            ],
        ]);

        // Stats for extension counts
        $extensionStats = [
            'total' => $extensions->count(),
            'active' => $extensions->where('status', 'active')->count(),
            'pending' => $extensions->where('status', 'pending')->count(),
            'inactive' => $extensions->where('status', 'inactive')->count(),
        ];

        return view('admin.extensions.index', compact('extensions', 'extensionStats'));
    }

    /**
     * Display the extension creation form.
     *
     * @return \Illuminate\View\View
     */
    public function createExtension()
    {
        // Get available plans
        $plans = ['Basic', 'Standard', 'Premium'];

        return view('admin.extensions.create', compact('plans'));
    }

    /**
     * Display the extension details.
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function showExtension($id)
    {
        // Mock data for a specific extension
        $extension = (object) [
            'id' => $id,
            'name' => 'Live Class',
            'slug' => 'live-class',
            'description' => 'Enable real-time virtual classrooms with video conferencing, screen sharing, and interactive whiteboard.',
            'long_description' => '<p>The Live Class extension transforms your online courses into interactive virtual classrooms. With real-time video conferencing, screen sharing, and an interactive whiteboard, instructors can deliver engaging live sessions to students anywhere in the world.</p>
                <p>Key features include:</p>
                <ul>
                    <li>HD video conferencing with up to 100 participants</li>
                    <li>Screen sharing for presentations and demonstrations</li>
                    <li>Interactive whiteboard for real-time collaboration</li>
                    <li>Chat functionality for questions and discussions</li>
                    <li>Recording capabilities for later review</li>
                    <li>Breakout rooms for small group activities</li>
                </ul>
                <p>This extension integrates seamlessly with the course management system, allowing instructors to schedule and manage live sessions directly from their course dashboard.</p>',
            'version' => '1.2.0',
            'author' => 'Naxofy',
            'author_website' => 'https://naxofy.com',
            'status' => 'active',
            'is_featured' => true,
            'icon' => 'video-camera',
            'installations' => 245,
            'rating' => 4.8,
            'created_at' => now()->subMonths(3),
            'updated_at' => now()->subWeeks(2),
            'plans' => ['Premium', 'Standard'],
            'requirements' => [
                'PHP 7.4 or higher',
                'MySQL 5.7 or higher',
                'WebRTC compatible browser',
                'Minimum 2GB RAM server',
            ],
            'screenshots' => [
                'live-class-1.jpg',
                'live-class-2.jpg',
                'live-class-3.jpg',
            ],
            'changelog' => [
                [
                    'version' => '1.2.0',
                    'date' => now()->subWeeks(2)->format('Y-m-d'),
                    'changes' => [
                        'Added breakout rooms feature',
                        'Improved video quality and stability',
                        'Fixed issue with screen sharing on Safari',
                    ],
                ],
                [
                    'version' => '1.1.0',
                    'date' => now()->subMonths(1)->format('Y-m-d'),
                    'changes' => [
                        'Added recording functionality',
                        'Enhanced whiteboard tools',
                        'Fixed chat notification issues',
                    ],
                ],
                [
                    'version' => '1.0.0',
                    'date' => now()->subMonths(3)->format('Y-m-d'),
                    'changes' => [
                        'Initial release',
                    ],
                ],
            ],
        ];

        // Adjust data based on ID
        if ($id == 2) {
            $extension->name = 'Quiz Builder';
            $extension->slug = 'quiz-builder';
            $extension->description = 'Create interactive quizzes with multiple question types, automatic grading, and detailed analytics.';
            $extension->long_description = '<p>The Quiz Builder extension allows instructors to create engaging assessments with a variety of question types. From multiple choice to essay questions, this tool provides flexibility in assessment design while automating the grading process.</p>
                <p>Key features include:</p>
                <ul>
                    <li>Multiple question types (multiple choice, true/false, matching, short answer, essay)</li>
                    <li>Automatic grading for objective questions</li>
                    <li>Detailed analytics on student performance</li>
                    <li>Question bank for reusing questions</li>
                    <li>Randomized question order</li>
                    <li>Timed quizzes with auto-submit</li>
                </ul>
                <p>This extension integrates with the gradebook, automatically updating student scores upon quiz completion.</p>';
            $extension->version = '2.1.0';
            $extension->author = 'Naxofy';
            $extension->icon = 'academic-cap';
            $extension->installations = 312;
            $extension->rating = 4.6;
            $extension->plans = ['Premium', 'Standard', 'Basic'];
        } elseif ($id == 3) {
            $extension->name = 'Certificate Generator';
            $extension->slug = 'certificate-generator';
            $extension->description = 'Automatically generate and issue certificates upon course completion with customizable templates.';
            $extension->long_description = '<p>The Certificate Generator extension automates the process of creating and issuing professional certificates to students upon course completion. With customizable templates and dynamic data insertion, instructors can provide students with personalized certificates of achievement.</p>
                <p>Key features include:</p>
                <ul>
                    <li>Multiple certificate templates</li>
                    <li>Customizable design elements (colors, fonts, images)</li>
                    <li>Dynamic data insertion (student name, course name, date, etc.)</li>
                    <li>Automatic issuance upon course completion</li>
                    <li>PDF generation for easy downloading and printing</li>
                    <li>Certificate verification system</li>
                </ul>
                <p>This extension helps increase student motivation and provides tangible recognition of their achievements.</p>';
            $extension->version = '1.5.0';
            $extension->author = 'EduTech Solutions';
            $extension->author_website = 'https://edutechsolutions.com';
            $extension->icon = 'document-text';
            $extension->installations = 189;
            $extension->rating = 4.5;
            $extension->plans = ['Premium'];
        }

        // Get available plans
        $plans = ['Basic', 'Standard', 'Premium'];

        return view('admin.extensions.show', compact('extension', 'plans'));
    }

    /**
     * Display the extension edit form.
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function editExtension($id)
    {
        // Mock data for a specific extension
        $extension = (object) [
            'id' => $id,
            'name' => 'Live Class',
            'slug' => 'live-class',
            'description' => 'Enable real-time virtual classrooms with video conferencing, screen sharing, and interactive whiteboard.',
            'version' => '1.2.0',
            'author' => 'Naxofy',
            'author_website' => 'https://naxofy.com',
            'status' => 'active',
            'is_featured' => true,
            'icon' => 'video-camera',
            'plans' => ['Premium', 'Standard'],
        ];

        // Adjust data based on ID
        if ($id == 2) {
            $extension->name = 'Quiz Builder';
            $extension->slug = 'quiz-builder';
            $extension->description = 'Create interactive quizzes with multiple question types, automatic grading, and detailed analytics.';
            $extension->version = '2.1.0';
            $extension->author = 'Naxofy';
            $extension->icon = 'academic-cap';
            $extension->plans = ['Premium', 'Standard', 'Basic'];
        } elseif ($id == 3) {
            $extension->name = 'Certificate Generator';
            $extension->slug = 'certificate-generator';
            $extension->description = 'Automatically generate and issue certificates upon course completion with customizable templates.';
            $extension->version = '1.5.0';
            $extension->author = 'EduTech Solutions';
            $extension->author_website = 'https://edutechsolutions.com';
            $extension->icon = 'document-text';
            $extension->plans = ['Premium'];
        }

        // Get available plans
        $plans = ['Basic', 'Standard', 'Premium'];

        return view('admin.extensions.edit', compact('extension', 'plans'));
    }

    /**
     * Display the extension marketplace.
     *
     * @return \Illuminate\View\View
     */
    public function extensionMarketplace()
    {
        // Mock data for marketplace extensions
        $extensions = collect([
            (object) [
                'id' => 1,
                'name' => 'Live Class',
                'slug' => 'live-class',
                'description' => 'Enable real-time virtual classrooms with video conferencing, screen sharing, and interactive whiteboard.',
                'version' => '1.2.0',
                'author' => 'Naxofy',
                'price' => 0,
                'is_free' => true,
                'is_featured' => true,
                'icon' => 'video-camera',
                'installations' => 245,
                'rating' => 4.8,
                'created_at' => now()->subMonths(3),
                'updated_at' => now()->subWeeks(2),
            ],
            (object) [
                'id' => 2,
                'name' => 'Quiz Builder',
                'slug' => 'quiz-builder',
                'description' => 'Create interactive quizzes with multiple question types, automatic grading, and detailed analytics.',
                'version' => '2.1.0',
                'author' => 'Naxofy',
                'price' => 0,
                'is_free' => true,
                'is_featured' => false,
                'icon' => 'academic-cap',
                'installations' => 312,
                'rating' => 4.6,
                'created_at' => now()->subMonths(5),
                'updated_at' => now()->subWeeks(1),
            ],
            (object) [
                'id' => 3,
                'name' => 'Certificate Generator',
                'slug' => 'certificate-generator',
                'description' => 'Automatically generate and issue certificates upon course completion with customizable templates.',
                'version' => '1.5.0',
                'author' => 'EduTech Solutions',
                'price' => 29.99,
                'is_free' => false,
                'is_featured' => false,
                'icon' => 'document-text',
                'installations' => 189,
                'rating' => 4.5,
                'created_at' => now()->subMonths(2),
                'updated_at' => now()->subDays(10),
            ],
            (object) [
                'id' => 5,
                'name' => 'Assignment Submission',
                'slug' => 'assignment-submission',
                'description' => 'Allow students to submit assignments with file uploads and receive instructor feedback.',
                'version' => '1.3.1',
                'author' => 'Naxofy',
                'price' => 0,
                'is_free' => true,
                'is_featured' => true,
                'icon' => 'clipboard-check',
                'installations' => 278,
                'rating' => 4.7,
                'created_at' => now()->subMonths(4),
                'updated_at' => now()->subWeeks(3),
            ],
            (object) [
                'id' => 6,
                'name' => 'Progress Tracker',
                'slug' => 'progress-tracker',
                'description' => 'Track and visualize student progress through courses with detailed analytics and reports.',
                'version' => '2.0.0',
                'author' => 'Data Insights',
                'price' => 19.99,
                'is_free' => false,
                'is_featured' => false,
                'icon' => 'chart-bar',
                'installations' => 156,
                'rating' => 4.3,
                'created_at' => now()->subMonths(6),
                'updated_at' => now()->subMonths(1),
            ],
            (object) [
                'id' => 9,
                'name' => 'Content Drip',
                'slug' => 'content-drip',
                'description' => 'Release course content gradually based on schedule or student progress.',
                'version' => '1.4.0',
                'author' => 'Course Control',
                'price' => 24.99,
                'is_free' => false,
                'is_featured' => true,
                'icon' => 'clock',
                'installations' => 142,
                'rating' => 4.4,
                'created_at' => now()->subMonths(8),
                'updated_at' => now()->subWeeks(6),
            ],
            (object) [
                'id' => 10,
                'name' => 'Interactive Video',
                'slug' => 'interactive-video',
                'description' => 'Add quizzes, hotspots, and branching scenarios to video content.',
                'version' => '2.2.0',
                'author' => 'Video Plus',
                'price' => 39.99,
                'is_free' => false,
                'is_featured' => false,
                'icon' => 'film',
                'installations' => 98,
                'rating' => 4.9,
                'created_at' => now()->subMonths(10),
                'updated_at' => now()->subWeeks(4),
            ],
            (object) [
                'id' => 11,
                'name' => 'AI Tutor',
                'slug' => 'ai-tutor',
                'description' => 'Provide personalized learning assistance with AI-powered tutoring.',
                'version' => '1.0.0',
                'author' => 'AI Education',
                'price' => 49.99,
                'is_free' => false,
                'is_featured' => true,
                'icon' => 'chip',
                'installations' => 76,
                'rating' => 4.2,
                'created_at' => now()->subMonths(1),
                'updated_at' => now()->subWeeks(1),
            ],
        ]);

        // Categories for filtering
        $categories = [
            'All',
            'Assessment',
            'Communication',
            'Content',
            'Engagement',
            'Analytics',
            'Integration',
        ];

        return view('admin.extensions.marketplace', compact('extensions', 'categories'));
    }

    /**
     * Display the extension requests.
     *
     * @return \Illuminate\View\View
     */
    public function extensionRequests()
    {
        // Mock data for extension requests
        $requests = collect([
            (object) [
                'id' => 1,
                'extension_name' => 'Discussion Forum',
                'developer_name' => 'Community Builders',
                'developer_email' => '<EMAIL>',
                'description' => 'Add course-specific discussion forums for student collaboration and instructor moderation.',
                'status' => 'pending',
                'submitted_at' => now()->subWeeks(2),
            ],
            (object) [
                'id' => 2,
                'extension_name' => 'Social Learning',
                'developer_name' => 'Social Edu',
                'developer_email' => '<EMAIL>',
                'description' => 'Integrate social media features like profiles, activity feeds, and direct messaging.',
                'status' => 'pending',
                'submitted_at' => now()->subWeeks(3),
            ],
            (object) [
                'id' => 3,
                'extension_name' => 'Flashcards',
                'developer_name' => 'Memory Tech',
                'developer_email' => '<EMAIL>',
                'description' => 'Create and study flashcards with spaced repetition for improved retention.',
                'status' => 'approved',
                'submitted_at' => now()->subMonths(1),
                'approved_at' => now()->subWeeks(3),
            ],
            (object) [
                'id' => 4,
                'extension_name' => 'Peer Review',
                'developer_name' => 'Collaborative Learning',
                'developer_email' => '<EMAIL>',
                'description' => 'Enable peer assessment and feedback for assignments and projects.',
                'status' => 'rejected',
                'submitted_at' => now()->subMonths(2),
                'rejected_at' => now()->subMonths(1)->subWeeks(2),
                'rejection_reason' => 'Similar functionality already exists in the Assignment Submission extension.',
            ],
            (object) [
                'id' => 5,
                'extension_name' => 'Language Translation',
                'developer_name' => 'Global Edu',
                'developer_email' => '<EMAIL>',
                'description' => 'Automatically translate course content into multiple languages.',
                'status' => 'pending',
                'submitted_at' => now()->subDays(5),
            ],
        ]);

        return view('admin.extensions.requests', compact('requests'));
    }

    /**
     * Approve an extension request.
     *
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function approveExtensionRequest($id)
    {
        // In a real application, you would update the request in the database
        // For now, we'll just redirect back with a success message

        return redirect()->route('admin.extensions.requests')->with('success', 'Extension request approved successfully.');
    }

    /**
     * Reject an extension request.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function rejectExtensionRequest(Request $request, $id)
    {
        // In a real application, you would update the request in the database
        // For now, we'll just redirect back with a success message

        return redirect()->route('admin.extensions.requests')->with('success', 'Extension request rejected successfully.');
    }

    /**
     * Enable an extension.
     *
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function enableExtension($id)
    {
        // In a real application, you would update the extension in the database
        // For now, we'll just redirect back with a success message

        return redirect()->back()->with('success', 'Extension enabled successfully.');
    }

    /**
     * Disable an extension.
     *
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function disableExtension($id)
    {
        // In a real application, you would update the extension in the database
        // For now, we'll just redirect back with a success message

        return redirect()->back()->with('success', 'Extension disabled successfully.');
    }

    /**
     * Update extension plans.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateExtensionPlans(Request $request, $id)
    {
        // In a real application, you would update the extension plans in the database
        // For now, we'll just redirect back with a success message

        return redirect()->back()->with('success', 'Extension plans updated successfully.');
    }
}
