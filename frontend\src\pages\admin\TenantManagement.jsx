import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Button,
  Chip,
  TextField,
  InputAdornment,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  CircularProgress,
  Grid,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Alert
} from '@mui/material';
import {
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Delete as DeleteIcon,
  Block as BlockIcon
} from '@mui/icons-material';
import { tenantService } from '../../services/api';
import { Link as RouterLink } from 'react-router-dom';

const TenantManagement = () => {
  const [tenants, setTenants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedTenant, setSelectedTenant] = useState(null);
  const [approveDialogOpen, setApproveDialogOpen] = useState(false);
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  const [suspendDialogOpen, setSuspendDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    fetchTenants();
  }, []);

  const fetchTenants = async () => {
    try {
      setLoading(true);
      const response = await tenantService.getAllTenants();
      setTenants(response.data);
      setError(null);
    } catch (error) {
      console.error('Error fetching tenants:', error);
      setError('Failed to load tenants. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value);
    setPage(0);
  };

  const handleStatusFilterChange = (event) => {
    setStatusFilter(event.target.value);
    setPage(0);
  };

  const handleApproveDialogOpen = (tenant) => {
    setSelectedTenant(tenant);
    setApproveDialogOpen(true);
  };

  const handleApproveDialogClose = () => {
    setApproveDialogOpen(false);
    setSelectedTenant(null);
  };

  const handleRejectDialogOpen = (tenant) => {
    setSelectedTenant(tenant);
    setRejectDialogOpen(true);
  };

  const handleRejectDialogClose = () => {
    setRejectDialogOpen(false);
    setSelectedTenant(null);
  };

  const handleSuspendDialogOpen = (tenant) => {
    setSelectedTenant(tenant);
    setSuspendDialogOpen(true);
  };

  const handleSuspendDialogClose = () => {
    setSuspendDialogOpen(false);
    setSelectedTenant(null);
  };

  const handleDeleteDialogOpen = (tenant) => {
    setSelectedTenant(tenant);
    setDeleteDialogOpen(true);
  };

  const handleDeleteDialogClose = () => {
    setDeleteDialogOpen(false);
    setSelectedTenant(null);
  };

  const handleApproveTenant = async () => {
    if (!selectedTenant) return;
    
    try {
      setActionLoading(true);
      await tenantService.approveTenant(selectedTenant.id);
      
      // Update the tenant status in the local state
      setTenants(tenants.map(tenant => 
        tenant.id === selectedTenant.id ? { ...tenant, status: 'active' } : tenant
      ));
      
      handleApproveDialogClose();
    } catch (error) {
      console.error('Error approving tenant:', error);
      setError('Failed to approve tenant. Please try again later.');
    } finally {
      setActionLoading(false);
    }
  };

  const handleRejectTenant = async () => {
    if (!selectedTenant) return;
    
    try {
      setActionLoading(true);
      await tenantService.rejectTenant(selectedTenant.id);
      
      // Update the tenant status in the local state
      setTenants(tenants.map(tenant => 
        tenant.id === selectedTenant.id ? { ...tenant, status: 'rejected' } : tenant
      ));
      
      handleRejectDialogClose();
    } catch (error) {
      console.error('Error rejecting tenant:', error);
      setError('Failed to reject tenant. Please try again later.');
    } finally {
      setActionLoading(false);
    }
  };

  const handleSuspendTenant = async () => {
    if (!selectedTenant) return;
    
    try {
      setActionLoading(true);
      await tenantService.suspendTenant(selectedTenant.id);
      
      // Update the tenant status in the local state
      setTenants(tenants.map(tenant => 
        tenant.id === selectedTenant.id ? { ...tenant, status: 'suspended' } : tenant
      ));
      
      handleSuspendDialogClose();
    } catch (error) {
      console.error('Error suspending tenant:', error);
      setError('Failed to suspend tenant. Please try again later.');
    } finally {
      setActionLoading(false);
    }
  };

  const handleDeleteTenant = async () => {
    if (!selectedTenant) return;
    
    try {
      setActionLoading(true);
      await tenantService.deleteTenant(selectedTenant.id);
      
      // Remove the tenant from the local state
      setTenants(tenants.filter(tenant => tenant.id !== selectedTenant.id));
      
      handleDeleteDialogClose();
    } catch (error) {
      console.error('Error deleting tenant:', error);
      setError('Failed to delete tenant. Please try again later.');
    } finally {
      setActionLoading(false);
    }
  };

  const filteredTenants = tenants.filter(tenant => {
    const matchesSearch = tenant.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
                          tenant.domain.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          tenant.email.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || tenant.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const paginatedTenants = filteredTenants.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  const getStatusChip = (status) => {
    switch (status) {
      case 'active':
        return <Chip label="Active" color="success" size="small" />;
      case 'pending':
        return <Chip label="Pending" color="warning" size="small" />;
      case 'suspended':
        return <Chip label="Suspended" color="error" size="small" />;
      case 'rejected':
        return <Chip label="Rejected" color="default" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Tenant Management
      </Typography>
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      <Paper sx={{ p: 3, mb: 4 }}>
        <Grid container spacing={2} alignItems="center" sx={{ mb: 3 }}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search tenants..."
              variant="outlined"
              value={searchQuery}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth variant="outlined">
              <InputLabel id="status-filter-label">Status</InputLabel>
              <Select
                labelId="status-filter-label"
                id="status-filter"
                value={statusFilter}
                onChange={handleStatusFilterChange}
                label="Status"
              >
                <MenuItem value="all">All Statuses</MenuItem>
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="pending">Pending</MenuItem>
                <MenuItem value="suspended">Suspended</MenuItem>
                <MenuItem value="rejected">Rejected</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3} sx={{ textAlign: { xs: 'left', md: 'right' } }}>
            <Button
              variant="contained"
              color="primary"
              onClick={fetchTenants}
            >
              Refresh
            </Button>
          </Grid>
        </Grid>
        
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Domain</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Created At</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {paginatedTenants.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    No tenants found
                  </TableCell>
                </TableRow>
              ) : (
                paginatedTenants.map((tenant) => (
                  <TableRow key={tenant.id}>
                    <TableCell>{tenant.name}</TableCell>
                    <TableCell>{tenant.domain}.lmsplatform.com</TableCell>
                    <TableCell>{tenant.email}</TableCell>
                    <TableCell>{getStatusChip(tenant.status)}</TableCell>
                    <TableCell>{new Date(tenant.created_at).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <IconButton
                          size="small"
                          color="primary"
                          component={RouterLink}
                          to={`/admin/tenants/${tenant.id}`}
                          title="View Details"
                        >
                          <VisibilityIcon fontSize="small" />
                        </IconButton>
                        
                        {tenant.status === 'pending' && (
                          <>
                            <IconButton
                              size="small"
                              color="success"
                              onClick={() => handleApproveDialogOpen(tenant)}
                              title="Approve"
                            >
                              <CheckCircleIcon fontSize="small" />
                            </IconButton>
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleRejectDialogOpen(tenant)}
                              title="Reject"
                            >
                              <CancelIcon fontSize="small" />
                            </IconButton>
                          </>
                        )}
                        
                        {tenant.status === 'active' && (
                          <IconButton
                            size="small"
                            color="warning"
                            onClick={() => handleSuspendDialogOpen(tenant)}
                            title="Suspend"
                          >
                            <BlockIcon fontSize="small" />
                          </IconButton>
                        )}
                        
                        {tenant.status !== 'active' && tenant.status !== 'pending' && (
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleDeleteDialogOpen(tenant)}
                            title="Delete"
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
        
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={filteredTenants.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>
      
      {/* Approve Dialog */}
      <Dialog
        open={approveDialogOpen}
        onClose={handleApproveDialogClose}
      >
        <DialogTitle>Approve Tenant</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to approve {selectedTenant?.name}? This will activate their account and allow them to use the platform.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleApproveDialogClose} disabled={actionLoading}>
            Cancel
          </Button>
          <Button 
            onClick={handleApproveTenant} 
            color="success" 
            variant="contained"
            disabled={actionLoading}
          >
            {actionLoading ? <CircularProgress size={24} /> : 'Approve'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Reject Dialog */}
      <Dialog
        open={rejectDialogOpen}
        onClose={handleRejectDialogClose}
      >
        <DialogTitle>Reject Tenant</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to reject {selectedTenant?.name}? This will prevent them from using the platform.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleRejectDialogClose} disabled={actionLoading}>
            Cancel
          </Button>
          <Button 
            onClick={handleRejectTenant} 
            color="error" 
            variant="contained"
            disabled={actionLoading}
          >
            {actionLoading ? <CircularProgress size={24} /> : 'Reject'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Suspend Dialog */}
      <Dialog
        open={suspendDialogOpen}
        onClose={handleSuspendDialogClose}
      >
        <DialogTitle>Suspend Tenant</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to suspend {selectedTenant?.name}? This will temporarily disable their account.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleSuspendDialogClose} disabled={actionLoading}>
            Cancel
          </Button>
          <Button 
            onClick={handleSuspendTenant} 
            color="warning" 
            variant="contained"
            disabled={actionLoading}
          >
            {actionLoading ? <CircularProgress size={24} /> : 'Suspend'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Delete Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteDialogClose}
      >
        <DialogTitle>Delete Tenant</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to permanently delete {selectedTenant?.name}? This action cannot be undone and will remove all associated data.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteDialogClose} disabled={actionLoading}>
            Cancel
          </Button>
          <Button 
            onClick={handleDeleteTenant} 
            color="error" 
            variant="contained"
            disabled={actionLoading}
          >
            {actionLoading ? <CircularProgress size={24} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default TenantManagement;
