🚀 Installation Guide

This document provides step-by-step instructions for setting up the development environment and installing the LMS platform components.

## 📋 Prerequisites

Before starting the installation, ensure you have the following prerequisites installed:

- PHP 8.1 or higher
- Composer 2.0 or higher
- Node.js 16.0 or higher
- npm 8.0 or higher
- MySQL 8.0 or higher
- Git

## 🔄 Clone the Repository

```bash
git clone https://github.com/yourusername/lms-platform.git
cd lms-platform
```

## 🔙 Backend Setup (Laravel)

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Install PHP dependencies:
   ```bash
   composer install
   ```

3. Create a copy of the environment file:
   ```bash
   cp .env.example .env
   ```

4. Generate an application key:
   ```bash
   php artisan key:generate
   ```

5. Configure the database connection in the `.env` file:
   ```
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=lms
   DB_USERNAME=root
   DB_PASSWORD=your_password
   ```

6. Run database migrations and seed the database:
   ```bash
   php artisan migrate --seed
   ```

7. Create a symbolic link for storage:
   ```bash
   php artisan storage:link
   ```

8. Start the development server:
   ```bash
   php artisan serve
   ```
   The backend will be available at http://localhost:8000

## 🖥️ Frontend Setup (React)

1. Navigate to the frontend directory:
   ```bash
   cd ../frontend
   ```

2. Install JavaScript dependencies:
   ```bash
   npm install
   ```

3. Create a copy of the environment file:
   ```bash
   cp .env.example .env
   ```

4. Configure the API URL in the `.env` file:
   ```
   VITE_API_URL=http://localhost:8000/api
   ```

5. Start the development server:
   ```bash
   npm run dev
   ```
   The frontend will be available at http://localhost:5173

## 📱 Mobile App Setup (React Native)

1. Navigate to the mobile directory:
   ```bash
   cd ../mobile
   ```

2. Install JavaScript dependencies:
   ```bash
   npm install
   ```

3. Install Expo CLI globally (if not already installed):
   ```bash
   npm install -g expo-cli
   ```

4. Create a copy of the environment file:
   ```bash
   cp .env.example .env
   ```

5. Configure the API URL in the `.env` file:
   ```
   EXPO_PUBLIC_API_URL=http://localhost:8000/api
   ```

6. Start the Expo development server:
   ```bash
   npx expo start
   ```

7. Use the Expo Go app on your mobile device to scan the QR code, or press:
   - `a` to open in an Android emulator
   - `i` to open in an iOS simulator

## 🔐 Setting Up Authentication

### Google Authentication

1. Create a Google OAuth client ID at https://console.cloud.google.com/apis/credentials

2. Configure the OAuth consent screen with the necessary scopes:
   - `email`
   - `profile`

3. Add authorized redirect URIs:
   - `http://localhost:8000/auth/google/callback` (Backend)
   - `http://localhost:5173/auth/google/callback` (Frontend)

4. Update the `.env` file in the backend directory:
   ```
   GOOGLE_CLIENT_ID=your_client_id
   GOOGLE_CLIENT_SECRET=your_client_secret
   GOOGLE_REDIRECT_URI=http://localhost:8000/auth/google/callback
   ```

5. Update the `.env` file in the frontend directory:
   ```
   VITE_GOOGLE_CLIENT_ID=your_client_id
   ```

## 🗃️ Database Setup

1. Create a MySQL database:
   ```sql
   CREATE DATABASE lms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. Create a database user (optional but recommended):
   ```sql
   CREATE USER 'lms_user'@'localhost' IDENTIFIED BY 'your_password';
   GRANT ALL PRIVILEGES ON lms.* TO 'lms_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

3. Update the `.env` file in the backend directory with the database credentials.

## 📧 Email Configuration

1. Configure email settings in the backend `.env` file:
   ```
   MAIL_MAILER=smtp
   MAIL_HOST=smtp.mailtrap.io
   MAIL_PORT=2525
   MAIL_USERNAME=your_username
   MAIL_PASSWORD=your_password
   MAIL_ENCRYPTION=tls
   MAIL_FROM_ADDRESS=<EMAIL>
   MAIL_FROM_NAME="${APP_NAME}"
   ```

## 💳 Payment Gateway Setup (Razorpay)

1. Create a Razorpay account at https://razorpay.com

2. Get your API keys from the Razorpay Dashboard

3. Update the `.env` file in the backend directory:
   ```
   RAZORPAY_KEY_ID=your_key_id
   RAZORPAY_KEY_SECRET=your_key_secret
   ```

## 🔄 Multi-tenancy Setup

1. Configure the multi-tenancy settings in the backend `.env` file:
   ```
   TENANT_DATABASE_PREFIX=tenant_
   TENANT_DOMAIN_SUFFIX=.localhost
   ```

2. Run the tenant setup command:
   ```bash
   php artisan tenancy:setup
   ```

## 🧪 Running Tests

### Backend Tests

```bash
cd backend
php artisan test
```

### Frontend Tests

```bash
cd frontend
npm run test
```

### Mobile Tests

```bash
cd mobile
npm run test
```

## 🛠️ Additional Configuration

### File Storage

By default, the application uses the local disk for file storage. To use S3 or another storage driver:

1. Install the required packages:
   ```bash
   cd backend
   composer require league/flysystem-aws-s3-v3
   ```

2. Update the `.env` file:
   ```
   FILESYSTEM_DISK=s3
   AWS_ACCESS_KEY_ID=your_access_key
   AWS_SECRET_ACCESS_KEY=your_secret_key
   AWS_DEFAULT_REGION=your_region
   AWS_BUCKET=your_bucket
   ```

### Caching

To improve performance, configure Redis for caching:

1. Install the required packages:
   ```bash
   cd backend
   composer require predis/predis
   ```

2. Update the `.env` file:
   ```
   CACHE_DRIVER=redis
   REDIS_HOST=127.0.0.1
   REDIS_PASSWORD=null
   REDIS_PORT=6379
   ```

### Queue System

For background processing, configure a queue driver:

1. Update the `.env` file:
   ```
   QUEUE_CONNECTION=database
   ```

2. Run the queue worker:
   ```bash
   php artisan queue:work
   ```

## 🚀 Deployment

For production deployment, follow these additional steps:

1. Optimize the Laravel application:
   ```bash
   cd backend
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache
   ```

2. Build the frontend for production:
   ```bash
   cd frontend
   npm run build
   ```

3. Build the mobile app for production:
   ```bash
   cd mobile
   eas build --platform all
   ```

## 🔍 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Verify database credentials in the `.env` file
   - Ensure MySQL service is running
   - Check if the database exists

2. **Permission Issues**
   - Ensure the `storage` and `bootstrap/cache` directories are writable
   - Run `chmod -R 775 storage bootstrap/cache`

3. **API Connection Error**
   - Verify the API URL in the frontend `.env` file
   - Check if the backend server is running
   - Check for CORS issues

4. **Node.js Dependency Issues**
   - Try clearing npm cache: `npm cache clean --force`
   - Delete `node_modules` and reinstall: `rm -rf node_modules && npm install`

5. **Expo Build Issues**
   - Update Expo CLI: `npm install -g expo-cli`
   - Clear Expo cache: `expo r -c`

### Getting Help

If you encounter issues not covered in this guide:

1. Check the error logs:
   - Laravel logs: `backend/storage/logs/laravel.log`
   - Frontend console: Browser developer tools
   - Expo logs: Terminal running Expo

2. Refer to the official documentation:
   - Laravel: https://laravel.com/docs
   - React: https://reactjs.org/docs
   - Expo: https://docs.expo.dev

3. Contact the development team for assistance.
