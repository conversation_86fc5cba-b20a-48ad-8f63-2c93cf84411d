<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class FeaturesController extends Controller
{
    /**
     * Display the features page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $featureCategories = [
            [
                'name' => 'Teaching',
                'icon' => 'academic-cap',
                'features' => [
                    [
                        'title' => 'Course Builder',
                        'description' => 'Create engaging courses with our intuitive drag-and-drop course builder.',
                        'icon' => 'puzzle-piece',
                    ],
                    [
                        'title' => 'Quiz & Certification',
                        'description' => 'Create quizzes, assignments, and issue certificates to your students.',
                        'icon' => 'document-check',
                    ],
                    [
                        'title' => 'Video Hosting',
                        'description' => 'Upload and stream videos directly from our platform with no bandwidth limits.',
                        'icon' => 'video-camera',
                    ],
                ],
            ],
            [
                'name' => 'Administration',
                'icon' => 'cog',
                'features' => [
                    [
                        'title' => 'Student Management',
                        'description' => 'Manage student enrollments, progress, and communications in one place.',
                        'icon' => 'users',
                    ],
                    [
                        'title' => 'Analytics',
                        'description' => 'Get detailed insights into student engagement and course performance.',
                        'icon' => 'chart-bar',
                    ],
                    [
                        'title' => 'Payment Processing',
                        'description' => 'Accept payments globally with our integrated payment processing.',
                        'icon' => 'credit-card',
                    ],
                ],
            ],
            [
                'name' => 'Customization',
                'icon' => 'paint-brush',
                'features' => [
                    [
                        'title' => 'Theme Customizer',
                        'description' => 'Customize the look and feel of your learning platform with our theme editor.',
                        'icon' => 'color-swatch',
                    ],
                    [
                        'title' => 'App Store',
                        'description' => 'Extend your platform with modules and integrations from our marketplace.',
                        'icon' => 'puzzle',
                    ],
                    [
                        'title' => 'Mobile App',
                        'description' => 'Offer your students a branded mobile app experience on iOS and Android.',
                        'icon' => 'device-mobile',
                    ],
                ],
            ],
        ];

        return view('marketing.features', compact('featureCategories'));
    }
}
