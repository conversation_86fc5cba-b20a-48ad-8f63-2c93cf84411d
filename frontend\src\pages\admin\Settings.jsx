import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  TextField,
  Button,
  Divider,
  Switch,
  FormControlLabel,
  FormGroup,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  InputAdornment,
  IconButton,
  Snackbar
} from '@mui/material';
import {
  Save as SaveIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Key as KeyIcon,
  Payment as PaymentIcon,
  Settings as SettingsIcon,
  Email as EmailIcon,
  Storage as StorageIcon
} from '@mui/icons-material';

const Settings = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [showApiKey, setShowApiKey] = useState(false);
  const [showSecretKey, setShowSecretKey] = useState(false);
  
  const [generalSettings, setGeneralSettings] = useState({
    site_name: 'LMS Platform',
    site_description: 'A comprehensive learning management system',
    contact_email: '<EMAIL>',
    support_phone: '+****************',
    default_language: 'en',
    timezone: 'UTC',
    enable_registration: true,
    enable_tenant_registration: true,
    require_email_verification: true,
    maintenance_mode: false
  });
  
  const [paymentSettings, setPaymentSettings] = useState({
    currency: 'USD',
    stripe_public_key: 'pk_test_51HZ6qEJK8lXh2aIj2RQjcKmyJqkEzSB9tYQgxJnYYQgIjVqYtJtPzVlUiFintmCBwYYQwUBhXFWPmJKwBQXLGNzP00JKYUzFsF',
    stripe_secret_key: '••••••••••••••••••••••••••••••••••••••••••••••••',
    paypal_client_id: 'AeGIgSX--JySJ1uCOCQj2dZnKQR1EM4YVtpP5MfkrQHnVfjs1CQnz--MoKgS5ZEwz3NNxhT8QMnOHMJe',
    paypal_secret: '••••••••••••••••••••••••••••••••••••••••••••••••',
    razorpay_key_id: 'rzp_test_hoXYhE5pdgCYTR',
    razorpay_key_secret: '••••••••••••••••••••••••••••••••••••••••••••••••',
    enable_stripe: true,
    enable_paypal: true,
    enable_razorpay: false,
    platform_fee_percentage: 5
  });
  
  const [emailSettings, setEmailSettings] = useState({
    mail_driver: 'smtp',
    mail_host: 'smtp.mailtrap.io',
    mail_port: '2525',
    mail_username: 'your_username',
    mail_password: '••••••••••••••••',
    mail_encryption: 'tls',
    mail_from_address: '<EMAIL>',
    mail_from_name: 'LMS Platform'
  });
  
  const [apiSettings, setApiSettings] = useState({
    enable_api: true,
    api_key: 'lms_api_key_12345678901234567890',
    api_rate_limit: 60,
    enable_webhooks: true,
    webhook_secret: '••••••••••••••••••••••••••••••••••••••••••••••••'
  });

  useEffect(() => {
    // In a real implementation, you would fetch settings from your API
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, []);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleGeneralSettingsChange = (e) => {
    const { name, value, type, checked } = e.target;
    setGeneralSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handlePaymentSettingsChange = (e) => {
    const { name, value, type, checked } = e.target;
    setPaymentSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleEmailSettingsChange = (e) => {
    const { name, value } = e.target;
    setEmailSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleApiSettingsChange = (e) => {
    const { name, value, type, checked } = e.target;
    setApiSettings(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSaveSettings = () => {
    setSaveLoading(true);
    setError(null);
    
    // In a real implementation, you would save settings to your API
    setTimeout(() => {
      setSaveLoading(false);
      setSuccess(true);
      
      // Hide success message after 3 seconds
      setTimeout(() => {
        setSuccess(false);
      }, 3000);
    }, 1500);
  };

  const handleToggleApiKey = () => {
    setShowApiKey(!showApiKey);
  };

  const handleToggleSecretKey = () => {
    setShowSecretKey(!showSecretKey);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Platform Settings
      </Typography>
      
      <Snackbar
        open={success}
        autoHideDuration={3000}
        onClose={() => setSuccess(false)}
        message="Settings saved successfully"
      />
      
      <Paper sx={{ p: 3, mb: 4 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          sx={{ mb: 3 }}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab icon={<SettingsIcon />} label="General" />
          <Tab icon={<PaymentIcon />} label="Payment" />
          <Tab icon={<EmailIcon />} label="Email" />
          <Tab icon={<KeyIcon />} label="API & Developer" />
          <Tab icon={<StorageIcon />} label="Storage" />
        </Tabs>
        
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}
        
        {/* General Settings */}
        {tabValue === 0 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              General Settings
            </Typography>
            <Divider sx={{ mb: 3 }} />
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Site Name"
                  name="site_name"
                  value={generalSettings.site_name}
                  onChange={handleGeneralSettingsChange}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Contact Email"
                  name="contact_email"
                  value={generalSettings.contact_email}
                  onChange={handleGeneralSettingsChange}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Site Description"
                  name="site_description"
                  value={generalSettings.site_description}
                  onChange={handleGeneralSettingsChange}
                  margin="normal"
                  multiline
                  rows={2}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Support Phone"
                  name="support_phone"
                  value={generalSettings.support_phone}
                  onChange={handleGeneralSettingsChange}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Default Language"
                  name="default_language"
                  value={generalSettings.default_language}
                  onChange={handleGeneralSettingsChange}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Timezone"
                  name="timezone"
                  value={generalSettings.timezone}
                  onChange={handleGeneralSettingsChange}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="subtitle1" gutterBottom>
                  Platform Features
                </Typography>
                <FormGroup>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={generalSettings.enable_registration}
                            onChange={handleGeneralSettingsChange}
                            name="enable_registration"
                            color="primary"
                          />
                        }
                        label="Enable User Registration"
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={generalSettings.enable_tenant_registration}
                            onChange={handleGeneralSettingsChange}
                            name="enable_tenant_registration"
                            color="primary"
                          />
                        }
                        label="Enable Tenant Registration"
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={generalSettings.require_email_verification}
                            onChange={handleGeneralSettingsChange}
                            name="require_email_verification"
                            color="primary"
                          />
                        }
                        label="Require Email Verification"
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={generalSettings.maintenance_mode}
                            onChange={handleGeneralSettingsChange}
                            name="maintenance_mode"
                            color="primary"
                          />
                        }
                        label="Maintenance Mode"
                      />
                    </Grid>
                  </Grid>
                </FormGroup>
              </Grid>
            </Grid>
          </Box>
        )}
        
        {/* Payment Settings */}
        {tabValue === 1 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Payment Settings
            </Typography>
            <Divider sx={{ mb: 3 }} />
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Currency"
                  name="currency"
                  value={paymentSettings.currency}
                  onChange={handlePaymentSettingsChange}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Platform Fee Percentage"
                  name="platform_fee_percentage"
                  type="number"
                  value={paymentSettings.platform_fee_percentage}
                  onChange={handlePaymentSettingsChange}
                  margin="normal"
                  InputProps={{
                    endAdornment: <InputAdornment position="end">%</InputAdornment>,
                  }}
                />
              </Grid>
              
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="subtitle1" gutterBottom>
                  Stripe Integration
                </Typography>
                <FormControlLabel
                  control={
                    <Switch
                      checked={paymentSettings.enable_stripe}
                      onChange={handlePaymentSettingsChange}
                      name="enable_stripe"
                      color="primary"
                    />
                  }
                  label="Enable Stripe"
                />
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Stripe Public Key"
                      name="stripe_public_key"
                      value={paymentSettings.stripe_public_key}
                      onChange={handlePaymentSettingsChange}
                      margin="normal"
                      disabled={!paymentSettings.enable_stripe}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Stripe Secret Key"
                      name="stripe_secret_key"
                      type={showSecretKey ? "text" : "password"}
                      value={paymentSettings.stripe_secret_key}
                      onChange={handlePaymentSettingsChange}
                      margin="normal"
                      disabled={!paymentSettings.enable_stripe}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              onClick={handleToggleSecretKey}
                              edge="end"
                            >
                              {showSecretKey ? <VisibilityOffIcon /> : <VisibilityIcon />}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Grid>
                </Grid>
              </Grid>
              
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="subtitle1" gutterBottom>
                  PayPal Integration
                </Typography>
                <FormControlLabel
                  control={
                    <Switch
                      checked={paymentSettings.enable_paypal}
                      onChange={handlePaymentSettingsChange}
                      name="enable_paypal"
                      color="primary"
                    />
                  }
                  label="Enable PayPal"
                />
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="PayPal Client ID"
                      name="paypal_client_id"
                      value={paymentSettings.paypal_client_id}
                      onChange={handlePaymentSettingsChange}
                      margin="normal"
                      disabled={!paymentSettings.enable_paypal}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="PayPal Secret"
                      name="paypal_secret"
                      type={showSecretKey ? "text" : "password"}
                      value={paymentSettings.paypal_secret}
                      onChange={handlePaymentSettingsChange}
                      margin="normal"
                      disabled={!paymentSettings.enable_paypal}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              onClick={handleToggleSecretKey}
                              edge="end"
                            >
                              {showSecretKey ? <VisibilityOffIcon /> : <VisibilityIcon />}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Grid>
                </Grid>
              </Grid>
              
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="subtitle1" gutterBottom>
                  Razorpay Integration
                </Typography>
                <FormControlLabel
                  control={
                    <Switch
                      checked={paymentSettings.enable_razorpay}
                      onChange={handlePaymentSettingsChange}
                      name="enable_razorpay"
                      color="primary"
                    />
                  }
                  label="Enable Razorpay"
                />
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Razorpay Key ID"
                      name="razorpay_key_id"
                      value={paymentSettings.razorpay_key_id}
                      onChange={handlePaymentSettingsChange}
                      margin="normal"
                      disabled={!paymentSettings.enable_razorpay}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Razorpay Key Secret"
                      name="razorpay_key_secret"
                      type={showSecretKey ? "text" : "password"}
                      value={paymentSettings.razorpay_key_secret}
                      onChange={handlePaymentSettingsChange}
                      margin="normal"
                      disabled={!paymentSettings.enable_razorpay}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              onClick={handleToggleSecretKey}
                              edge="end"
                            >
                              {showSecretKey ? <VisibilityOffIcon /> : <VisibilityIcon />}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Box>
        )}
        
        {/* Email Settings */}
        {tabValue === 2 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Email Settings
            </Typography>
            <Divider sx={{ mb: 3 }} />
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Mail Driver"
                  name="mail_driver"
                  value={emailSettings.mail_driver}
                  onChange={handleEmailSettingsChange}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Mail Host"
                  name="mail_host"
                  value={emailSettings.mail_host}
                  onChange={handleEmailSettingsChange}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Mail Port"
                  name="mail_port"
                  value={emailSettings.mail_port}
                  onChange={handleEmailSettingsChange}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Mail Encryption"
                  name="mail_encryption"
                  value={emailSettings.mail_encryption}
                  onChange={handleEmailSettingsChange}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Mail Username"
                  name="mail_username"
                  value={emailSettings.mail_username}
                  onChange={handleEmailSettingsChange}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Mail Password"
                  name="mail_password"
                  type={showSecretKey ? "text" : "password"}
                  value={emailSettings.mail_password}
                  onChange={handleEmailSettingsChange}
                  margin="normal"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={handleToggleSecretKey}
                          edge="end"
                        >
                          {showSecretKey ? <VisibilityOffIcon /> : <VisibilityIcon />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Mail From Address"
                  name="mail_from_address"
                  value={emailSettings.mail_from_address}
                  onChange={handleEmailSettingsChange}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Mail From Name"
                  name="mail_from_name"
                  value={emailSettings.mail_from_name}
                  onChange={handleEmailSettingsChange}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12}>
                <Button
                  variant="outlined"
                  color="primary"
                  sx={{ mt: 2 }}
                >
                  Send Test Email
                </Button>
              </Grid>
            </Grid>
          </Box>
        )}
        
        {/* API & Developer Settings */}
        {tabValue === 3 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              API & Developer Settings
            </Typography>
            <Divider sx={{ mb: 3 }} />
            
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={apiSettings.enable_api}
                      onChange={handleApiSettingsChange}
                      name="enable_api"
                      color="primary"
                    />
                  }
                  label="Enable API Access"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="API Key"
                  name="api_key"
                  type={showApiKey ? "text" : "password"}
                  value={apiSettings.api_key}
                  onChange={handleApiSettingsChange}
                  margin="normal"
                  disabled={!apiSettings.enable_api}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={handleToggleApiKey}
                          edge="end"
                        >
                          {showApiKey ? <VisibilityOffIcon /> : <VisibilityIcon />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="API Rate Limit (requests per minute)"
                  name="api_rate_limit"
                  type="number"
                  value={apiSettings.api_rate_limit}
                  onChange={handleApiSettingsChange}
                  margin="normal"
                  disabled={!apiSettings.enable_api}
                />
              </Grid>
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="subtitle1" gutterBottom>
                  Webhooks
                </Typography>
                <FormControlLabel
                  control={
                    <Switch
                      checked={apiSettings.enable_webhooks}
                      onChange={handleApiSettingsChange}
                      name="enable_webhooks"
                      color="primary"
                    />
                  }
                  label="Enable Webhooks"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Webhook Secret"
                  name="webhook_secret"
                  type={showSecretKey ? "text" : "password"}
                  value={apiSettings.webhook_secret}
                  onChange={handleApiSettingsChange}
                  margin="normal"
                  disabled={!apiSettings.enable_webhooks}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={handleToggleSecretKey}
                          edge="end"
                        >
                          {showSecretKey ? <VisibilityOffIcon /> : <VisibilityIcon />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <Button
                  variant="outlined"
                  color="primary"
                  sx={{ mt: 2 }}
                >
                  Generate New API Key
                </Button>
              </Grid>
            </Grid>
          </Box>
        )}
        
        {/* Storage Settings */}
        {tabValue === 4 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Storage Settings
            </Typography>
            <Divider sx={{ mb: 3 }} />
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Default Storage Driver"
                  name="storage_driver"
                  value="local"
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Max Upload Size (MB)"
                  name="max_upload_size"
                  type="number"
                  value="50"
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="subtitle1" gutterBottom>
                  Amazon S3 Configuration
                </Typography>
                <FormControlLabel
                  control={
                    <Switch
                      name="enable_s3"
                      color="primary"
                    />
                  }
                  label="Enable Amazon S3"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="AWS Access Key ID"
                  name="aws_access_key_id"
                  margin="normal"
                  disabled
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="AWS Secret Access Key"
                  name="aws_secret_access_key"
                  type="password"
                  margin="normal"
                  disabled
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="AWS Default Region"
                  name="aws_default_region"
                  margin="normal"
                  disabled
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="AWS Bucket"
                  name="aws_bucket"
                  margin="normal"
                  disabled
                />
              </Grid>
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="subtitle1" gutterBottom>
                  File Types
                </Typography>
                <TextField
                  fullWidth
                  label="Allowed File Extensions"
                  name="allowed_file_types"
                  value="jpg,jpeg,png,gif,pdf,doc,docx,ppt,pptx,xls,xlsx,zip,rar,mp4,mp3,avi,mov"
                  margin="normal"
                  helperText="Comma-separated list of allowed file extensions"
                />
              </Grid>
            </Grid>
          </Box>
        )}
        
        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="contained"
            color="primary"
            startIcon={saveLoading ? <CircularProgress size={20} color="inherit" /> : <SaveIcon />}
            onClick={handleSaveSettings}
            disabled={saveLoading}
          >
            {saveLoading ? 'Saving...' : 'Save Settings'}
          </Button>
        </Box>
      </Paper>
    </Container>
  );
};

export default Settings;
