import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  FlatList,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  ScrollView,
  TextInput
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

import Container from '@/components/ui/Container';
import Text from '@/components/ui/Text';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { useTheme } from '@/components/ThemeProvider';
import { useAuth } from '@/context/useAuth';

// Mock data for marketplace items
const mockThemes = [
  {
    id: 1,
    name: 'Modern Light Theme',
    description: 'A clean, modern theme with light colors and minimalist design.',
    thumbnail: 'https://source.unsplash.com/random?light',
    price: 0,
    is_featured: true,
    average_rating: 4.5,
    downloads: 1250,
    category: { name: 'Education' }
  },
  {
    id: 2,
    name: 'Dark Academia',
    description: 'A sophisticated dark theme inspired by academic aesthetics.',
    thumbnail: 'https://source.unsplash.com/random?dark',
    price: 29.99,
    is_featured: false,
    average_rating: 4.2,
    downloads: 850,
    category: { name: 'Business' }
  },
  {
    id: 3,
    name: 'Vibrant Colors',
    description: 'A bold and colorful theme to make your content stand out.',
    thumbnail: 'https://source.unsplash.com/random?colorful',
    price: 19.99,
    is_featured: true,
    average_rating: 4.7,
    downloads: 2100,
    category: { name: 'Creative' }
  },
];

const mockModules = [
  {
    id: 1,
    name: 'Advanced Quiz Builder',
    description: 'Create interactive quizzes with various question types and scoring options.',
    thumbnail: 'https://source.unsplash.com/random?quiz',
    price: 0,
    is_featured: true,
    average_rating: 4.8,
    downloads: 3200,
    category: { name: 'Assessment' }
  },
  {
    id: 2,
    name: 'Discussion Forum',
    description: 'Add a full-featured discussion forum to your courses.',
    thumbnail: 'https://source.unsplash.com/random?forum',
    price: 24.99,
    is_featured: false,
    average_rating: 4.3,
    downloads: 1500,
    category: { name: 'Communication' }
  },
  {
    id: 3,
    name: 'Certificate Generator',
    description: 'Automatically generate and issue certificates upon course completion.',
    thumbnail: 'https://source.unsplash.com/random?certificate',
    price: 14.99,
    is_featured: true,
    average_rating: 4.6,
    downloads: 2800,
    category: { name: 'Assessment' }
  },
];

const MarketplaceScreen = () => {
  const router = useRouter();
  const { theme } = useTheme();
  const { isAuthenticated } = useAuth();

  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTab, setSelectedTab] = useState('all');
  const [featuredItems, setFeaturedItems] = useState({ themes: mockThemes, modules: mockModules });
  const [allItems, setAllItems] = useState({ themes: mockThemes, modules: mockModules });

  useEffect(() => {
    fetchMarketplaceData();
  }, []);

  const fetchMarketplaceData = async () => {
    try {
      setLoading(true);

      // In a real implementation, you would fetch data from your API
      // const featuredResponse = await api.get('/marketplace/featured');
      // const themesResponse = await api.get('/marketplace/themes');
      // const modulesResponse = await api.get('/marketplace/modules');

      // Using mock data for now
      await new Promise(resolve => setTimeout(resolve, 1000));

      // setFeaturedItems(featuredResponse.data.data);
      // setAllItems({
      //   themes: themesResponse.data.data.data,
      //   modules: modulesResponse.data.data.data
      // });
    } catch (error) {
      console.error('Error fetching marketplace data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    fetchMarketplaceData();
  };

  const handleSearch = () => {
    // In a real implementation, you would search the API
    console.log('Searching for:', searchQuery);
  };

  const navigateToItemDetail = (type, id) => {
    router.push(`/marketplace/${type}/${id}`);
  };

  const renderRating = (rating) => {
    return (
      <View style={styles.ratingContainer}>
        {[1, 2, 3, 4, 5].map((star) => (
          <Ionicons
            key={star}
            name={star <= Math.floor(rating) ? 'star' : 'star-outline'}
            size={16}
            color="#fbbf24"
          />
        ))}
        <Text variant="caption" color="muted" style={styles.ratingText}>
          ({rating.toFixed(1)})
        </Text>
      </View>
    );
  };

  const renderItemCard = (item, type) => {
    return (
      <TouchableOpacity
        style={styles.card}
        onPress={() => navigateToItemDetail(type, item.id)}
        activeOpacity={0.7}
      >
        <Card style={styles.cardContainer}>
          <Image source={{ uri: item.thumbnail }} style={styles.cardImage} />
          {item.is_featured && (
            <View style={[styles.featuredChip, { backgroundColor: theme.primary }]}>
              <Text variant="caption" style={styles.featuredText}>Featured</Text>
            </View>
          )}
          <View style={styles.cardContent}>
            <Text variant="subtitle" numberOfLines={1} style={styles.cardTitle}>
              {item.name}
            </Text>
            <Text variant="caption" numberOfLines={2} color="muted" style={styles.description}>
              {item.description}
            </Text>
            {renderRating(item.average_rating || 0)}
            <View style={styles.cardFooter}>
              <Text variant="h4" color="primary" style={styles.price}>
                {item.price > 0 ? `$${item.price.toFixed(2)}` : 'Free'}
              </Text>
              <Button
                variant={item.price > 0 ? "contained" : "outline"}
                onPress={() => navigateToItemDetail(type, item.id)}
                style={styles.cardButton}
              >
                {item.price > 0 ? 'Buy' : 'Install'}
              </Button>
            </View>
          </View>
        </Card>
      </TouchableOpacity>
    );
  };

  const renderFeaturedSection = (title, items, type) => {
    if (!items || items.length === 0) return null;

    return (
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: Colors[theme].text }]}>
          {title}
        </Text>
        <FlatList
          data={items}
          keyExtractor={(item) => `${type}-${item.id}`}
          renderItem={({ item }) => renderItemCard(item, type)}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.featuredList}
        />
      </View>
    );
  };

  const renderAllItems = () => {
    let items = [];

    if (selectedTab === 'all' || selectedTab === 'themes') {
      items = [...items, ...allItems.themes.map(item => ({ ...item, type: 'theme' }))];
    }

    if (selectedTab === 'all' || selectedTab === 'modules') {
      items = [...items, ...allItems.modules.map(item => ({ ...item, type: 'module' }))];
    }

    return (
      <FlatList
        data={items}
        keyExtractor={(item) => `${item.type}-${item.id}`}
        renderItem={({ item }) => renderItemCard(item, item.type)}
        numColumns={2}
        contentContainerStyle={styles.allItemsList}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={{ color: Colors[theme].text }}>No items found</Text>
          </View>
        }
      />
    );
  };

  if (loading && !refreshing) {
    return (
      <Container style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.primary} />
        <Text style={styles.loadingText}>Loading marketplace...</Text>
      </Container>
    );
  }

  return (
    <Container>
      <View style={styles.header}>
        <Text variant="h2" style={styles.headerTitle}>
          Marketplace
        </Text>
        <Text variant="body" color="muted" style={styles.headerSubtitle}>
          Discover themes and modules for your learning platform
        </Text>
      </View>

      <View style={styles.searchContainer}>
        <TextInput
          style={[styles.searchInput, { backgroundColor: theme.backgroundSecondary, borderColor: theme.border }]}
          placeholder="Search marketplace..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          onSubmitEditing={handleSearch}
        />
        <TouchableOpacity
          style={[styles.searchButton, { backgroundColor: theme.primary }]}
          onPress={handleSearch}
        >
          <Ionicons name="search" size={20} color="white" />
        </TouchableOpacity>
      </View>

      <View style={styles.tabContainer}>
        {['all', 'themes', 'modules'].map((tab) => (
          <TouchableOpacity
            key={tab}
            style={[
              styles.tabButton,
              selectedTab === tab && { backgroundColor: theme.primary }
            ]}
            onPress={() => setSelectedTab(tab)}
          >
            <Text
              variant="caption"
              style={[
                styles.tabText,
                { color: selectedTab === tab ? 'white' : theme.textSecondary }
              ]}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {selectedTab === 'all' && (
          <>
            {renderFeaturedSection('Featured Themes', featuredItems.themes.filter(t => t.is_featured), 'theme')}
            {renderFeaturedSection('Featured Modules', featuredItems.modules.filter(m => m.is_featured), 'module')}
          </>
        )}

        <Text variant="h4" style={styles.allItemsTitle}>
          {selectedTab === 'all' ? 'All Items' :
           selectedTab === 'themes' ? 'All Themes' : 'All Modules'}
        </Text>

        {renderAllItems()}
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  headerTitle: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  headerSubtitle: {
    marginTop: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginBottom: 16,
    alignItems: 'center',
  },
  searchInput: {
    flex: 1,
    height: 48,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  searchButton: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
  tabContainer: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginBottom: 16,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 12,
    padding: 4,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  tabText: {
    fontWeight: '500',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginHorizontal: 16,
    marginBottom: 12,
  },
  allItemsTitle: {
    marginHorizontal: 16,
    marginTop: 20,
    marginBottom: 12,
    fontWeight: 'bold',
  },
  featuredList: {
    paddingHorizontal: 8,
  },
  allItemsList: {
    padding: 8,
  },
  card: {
    width: 280,
    marginHorizontal: 8,
    marginBottom: 16,
  },
  cardContainer: {
    overflow: 'hidden',
    borderRadius: 16,
  },
  cardImage: {
    width: '100%',
    height: 140,
    resizeMode: 'cover',
  },
  cardContent: {
    padding: 12,
  },
  cardTitle: {
    marginBottom: 4,
  },
  description: {
    marginVertical: 8,
    minHeight: 32,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 8,
  },
  ratingText: {
    marginLeft: 4,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  cardButton: {
    paddingHorizontal: 16,
  },
  featuredChip: {
    position: 'absolute',
    top: 8,
    right: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  featuredText: {
    color: 'white',
    fontWeight: 'bold',
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
  },
});

export default MarketplaceScreen;
