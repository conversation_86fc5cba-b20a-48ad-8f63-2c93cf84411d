<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Admin Dashboard Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for the admin dashboard.
    |
    */

    // Application name
    'name' => 'Naxofy Admin',

    // Application logo
    'logo' => 'images/logo.png',

    // Primary color
    'primary_color' => '#ff7700',
    'primary_color_light' => '#ff9a40',
    'primary_color_dark' => '#cc5c00',

    // Secondary color
    'secondary_color' => '#0369a1',
    'secondary_color_light' => '#38bdf8',
    'secondary_color_dark' => '#075985',

    // Admin route prefix
    'route_prefix' => 'admin',

    // Admin middleware
    'middleware' => ['web', 'auth', 'admin'],

    // Admin navigation
    'navigation' => [
        [
            'name' => 'Dashboard',
            'icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="nav-icon h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" /></svg>',
            'route' => 'admin.dashboard',
            'group' => null,
        ],
    ],
];
