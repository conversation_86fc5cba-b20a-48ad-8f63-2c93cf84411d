/**
 * LMS App Theme Colors
 */

// Primary colors
const primary = '#2563eb'; // Primary blue
const secondary = '#10b981'; // Success green
const accent = '#f59e0b'; // Warning/accent yellow
const error = '#ef4444'; // Error red

// Neutral colors
const neutralDark = '#1f2937';
const neutralMedium = '#6b7280';
const neutralLight = '#e5e7eb';
const neutralBackground = '#f9fafb';

export const Colors = {
  light: {
    // Base colors
    primary,
    secondary,
    accent,
    error,

    // Text colors
    text: neutralDark,
    textSecondary: neutralMedium,
    textLight: '#fff',

    // Background colors
    background: '#fff',
    backgroundSecondary: neutralBackground,
    card: '#fff',

    // Border colors
    border: neutralLight,
    divider: neutralLight,

    // Tab navigation
    tint: primary,
    tabIconDefault: neutralMedium,
    tabIconSelected: primary,
    tabBackground: '#fff',
    icon: neutralMedium,
  },
  dark: {
    // Base colors
    primary,
    secondary,
    accent,
    error,

    // Text colors
    text: '#fff',
    textSecondary: '#a1a1aa',
    textLight: '#fff',

    // Background colors
    background: '#121212',
    backgroundSecondary: '#1e1e1e',
    card: '#1e1e1e',

    // Border colors
    border: '#2e2e2e',
    divider: '#2e2e2e',

    // Tab navigation
    tint: primary,
    tabIconDefault: '#a1a1aa',
    tabIconSelected: primary,
    tabBackground: '#121212',
    icon: '#a1a1aa',
  },
};
