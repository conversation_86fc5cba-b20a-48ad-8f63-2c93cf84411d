<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Lesson;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class LessonController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth:api', ['except' => ['show']]);
    }

    /**
     * Display a listing of the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        // Validate request
        $validator = Validator::make($request->all(), [
            'course_id' => 'required|exists:courses,id',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 400);
        }

        // Get course
        $course = Course::findOrFail($request->course_id);

        // Get lessons
        $lessons = Lesson::where('course_id', $request->course_id)
            ->orderBy('order', 'asc')
            ->get();

        return response()->json($lessons);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Validate request
        $validator = Validator::make($request->all(), [
            'course_id' => 'required|exists:courses,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'video_url' => 'required|string',
            'type' => 'nullable|string|in:video,text,quiz',
            'order' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 400);
        }

        // Get course
        $course = Course::findOrFail($request->course_id);

        // Check if user is authorized to add lessons to the course
        $user = Auth::user();
        if ($user->role !== 'admin' && ($user->role !== 'tenant' || $course->instructor_id !== $user->id)) {
            return response()->json(['error' => 'Unauthorized. Only the instructor or admin can add lessons to this course.'], 403);
        }

        // Get max order if not provided
        if (!$request->has('order')) {
            $maxOrder = Lesson::where('course_id', $request->course_id)->max('order');
            $order = $maxOrder !== null ? $maxOrder + 1 : 0;
        } else {
            $order = $request->order;
        }

        // Create lesson
        $lesson = Lesson::create([
            'course_id' => $request->course_id,
            'title' => $request->title,
            'description' => $request->description,
            'video_url' => $request->video_url,
            'type' => $request->type ?? 'video',
            'order' => $order,
        ]);

        return response()->json([
            'message' => 'Lesson created successfully',
            'lesson' => $lesson
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(string $id)
    {
        $lesson = Lesson::findOrFail($id);

        // Get course
        $course = Course::findOrFail($lesson->course_id);

        // Check if user is enrolled in the course or is the instructor or admin
        $isAuthorized = false;
        if (Auth::check()) {
            $user = Auth::user();
            if ($user->role === 'admin' || $course->instructor_id === $user->id) {
                $isAuthorized = true;
            } else {
                $enrollment = $user->enrollments()->where('course_id', $course->id)->first();
                $isAuthorized = $enrollment !== null && $enrollment->payment_status === 'completed';
            }
        }

        if (!$isAuthorized) {
            return response()->json(['error' => 'Unauthorized. You must be enrolled in this course to view this lesson.'], 403);
        }

        return response()->json($lesson);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, string $id)
    {
        // Find lesson
        $lesson = Lesson::findOrFail($id);

        // Get course
        $course = Course::findOrFail($lesson->course_id);

        // Check if user is authorized to update the lesson
        $user = Auth::user();
        if ($user->role !== 'admin' && ($user->role !== 'tenant' || $course->instructor_id !== $user->id)) {
            return response()->json(['error' => 'Unauthorized. Only the instructor or admin can update this lesson.'], 403);
        }

        // Validate request
        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'video_url' => 'sometimes|required|string',
            'type' => 'nullable|string|in:video,text,quiz',
            'order' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 400);
        }

        // Update lesson
        if ($request->has('title')) {
            $lesson->title = $request->title;
        }

        if ($request->has('description')) {
            $lesson->description = $request->description;
        }

        if ($request->has('video_url')) {
            $lesson->video_url = $request->video_url;
        }

        if ($request->has('type')) {
            $lesson->type = $request->type;
        }

        if ($request->has('order')) {
            $lesson->order = $request->order;
        }

        $lesson->save();

        return response()->json([
            'message' => 'Lesson updated successfully',
            'lesson' => $lesson
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(string $id)
    {
        // Find lesson
        $lesson = Lesson::findOrFail($id);

        // Get course
        $course = Course::findOrFail($lesson->course_id);

        // Check if user is authorized to delete the lesson
        $user = Auth::user();
        if ($user->role !== 'admin' && ($user->role !== 'tenant' || $course->instructor_id !== $user->id)) {
            return response()->json(['error' => 'Unauthorized. Only the instructor or admin can delete this lesson.'], 403);
        }

        // Delete lesson
        $lesson->delete();

        return response()->json(['message' => 'Lesson deleted successfully']);
    }
}
