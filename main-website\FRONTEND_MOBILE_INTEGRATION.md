# Frontend and Mobile Integration Guide

This guide outlines the changes needed to integrate the React frontend and React Native mobile app with the unified backend.

## Overview

The unified backend combines the previously separate main-website and backend Laravel applications into a single, cohesive application. This requires updates to the API endpoints in both the frontend and mobile app.

## API Endpoint Changes

The API endpoints have been consolidated into a single API with a unified structure. The base URL remains the same, but some endpoint paths have changed.

### Base URL

The base URL for the API remains:

```
http://localhost:8000/api
```

## Frontend Integration

### 1. Update API Service

Update the `frontend/src/services/api.js` file with the following changes:

#### Authentication Endpoints

```javascript
// Auth services
export const authService = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  registerTenant: (tenantData) => api.post('/auth/register-tenant', tenantData),
  logout: () => api.post('/auth/logout'),
  getProfile: () => api.get('/auth/profile'),
  refreshToken: () => api.post('/auth/refresh'),
};
```

#### Course Endpoints

```javascript
// Course services
export const courseService = {
  getAllCourses: (params) => api.get('/courses', { params }),
  getCourseById: (id) => api.get(`/courses/${id}`),
  createCourse: (courseData) => api.post('/courses', courseData),
  updateCourse: (id, courseData) => api.put(`/courses/${id}`, courseData),
  deleteCourse: (id) => api.delete(`/courses/${id}`),
  enrollInCourse: (id) => api.post(`/courses/${id}/enroll`),
  updateProgress: (id, progress) => api.post(`/courses/${id}/progress`, { progress }),
};
```

#### Lesson Endpoints

```javascript
// Lesson services
export const lessonService = {
  getLessonsByCourse: (courseId) => api.get('/lessons', { params: { course_id: courseId } }),
  getLessonById: (id) => api.get(`/lessons/${id}`),
  createLesson: (lessonData) => api.post('/lessons', lessonData),
  updateLesson: (id, lessonData) => api.put(`/lessons/${id}`, lessonData),
  deleteLesson: (id) => api.delete(`/lessons/${id}`),
};
```

#### Tenant Endpoints

```javascript
// Tenant services
export const tenantService = {
  getAllTenants: () => api.get('/tenants'),
  getTenantById: (id) => api.get(`/tenants/${id}`),
  updateTenant: (id, tenantData) => api.put(`/tenants/${id}`, tenantData),
  deleteTenant: (id) => api.delete(`/tenants/${id}`),
  approveTenant: (id) => api.post(`/tenants/${id}/approve`),
};
```

#### Payment Endpoints

```javascript
// Payment services
export const paymentService = {
  createOrder: (courseId) => api.post('/payments/create-order', { course_id: courseId }),
  verifyPayment: (paymentData) => api.post('/payments/verify', paymentData),
  getPaymentHistory: () => api.get('/payments/history'),
};
```

#### Theme Endpoints

```javascript
// Theme services
export const themeService = {
  getAllThemes: (params) => api.get('/themes', { params }),
  getThemeById: (id) => api.get(`/themes/${id}`),
  createTheme: (themeData) => api.post('/themes', themeData),
  updateTheme: (id, themeData) => api.put(`/themes/${id}`, themeData),
  deleteTheme: (id) => api.delete(`/themes/${id}`),
  activateTheme: (id) => api.post(`/themes/${id}/activate`),
  installTheme: (id) => api.post(`/themes/${id}/install`),
};
```

#### Module Endpoints

```javascript
// Module services
export const moduleService = {
  getAllModules: (params) => api.get('/modules', { params }),
  getModuleById: (id) => api.get(`/modules/${id}`),
  createModule: (moduleData) => api.post('/modules', moduleData),
  updateModule: (id, moduleData) => api.put(`/modules/${id}`, moduleData),
  deleteModule: (id) => api.delete(`/modules/${id}`),
  installModule: (id) => api.post(`/modules/${id}/install`),
  uninstallModule: (id) => api.post(`/modules/${id}/uninstall`),
  enableModule: (id) => api.post(`/modules/${id}/enable`),
  disableModule: (id) => api.post(`/modules/${id}/disable`),
};
```

#### Marketplace Endpoints

```javascript
// Marketplace services
export const marketplaceService = {
  getFeatured: () => api.get('/marketplace/featured'),
  getThemes: (params) => api.get('/marketplace/themes', { params }),
  getModules: (params) => api.get('/marketplace/modules', { params }),
  getThemeById: (id) => api.get(`/marketplace/themes/${id}`),
  getModuleById: (id) => api.get(`/marketplace/modules/${id}`),
  getInstalledThemes: () => api.get('/marketplace/installed/themes'),
  getInstalledModules: () => api.get('/marketplace/installed/modules'),
  getThemeReviews: (id) => api.get(`/marketplace/themes/${id}/reviews`),
  getModuleReviews: (id) => api.get(`/marketplace/modules/${id}/reviews`),
  createThemeReview: (id, reviewData) => api.post(`/marketplace/themes/${id}/reviews`, reviewData),
  createModuleReview: (id, reviewData) => api.post(`/marketplace/modules/${id}/reviews`, reviewData),
  updateReview: (id, reviewData) => api.put(`/marketplace/reviews/${id}`, reviewData),
  deleteReview: (id) => api.delete(`/marketplace/reviews/${id}`),
};
```

## Mobile App Integration

### 1. Update API Service

Update the `mobile/services/api.js` file with the following changes:

#### Base URL Configuration

```javascript
const YOUR_IP_ADDRESS = 'localhost'; // Change this to your computer's IP address
const API_URL = USE_MOCK_DATA ? null : `http://${YOUR_IP_ADDRESS}:8000/api`;
```

#### Authentication Endpoints

```javascript
// Auth services
export const authService = {
  login: async (credentials) => {
    if (USE_MOCK_DATA) {
      // Mock implementation
    }
    return api.post('/auth/login', credentials);
  },
  register: async (userData) => {
    if (USE_MOCK_DATA) {
      // Mock implementation
    }
    return api.post('/auth/register', userData);
  },
  registerTenant: async (tenantData) => {
    if (USE_MOCK_DATA) {
      // Mock implementation
    }
    return api.post('/auth/register-tenant', tenantData);
  },
  logout: async () => {
    if (USE_MOCK_DATA) {
      // Mock implementation
    }
    return api.post('/auth/logout');
  },
  getProfile: async () => {
    if (USE_MOCK_DATA) {
      // Mock implementation
    }
    return api.get('/auth/profile');
  },
  refreshToken: async () => {
    if (USE_MOCK_DATA) {
      // Mock implementation
    }
    return api.post('/auth/refresh');
  },
};
```

#### Course Endpoints

```javascript
// Course services
export const courseService = {
  getAllCourses: async (params) => {
    if (USE_MOCK_DATA) {
      // Mock implementation
    }
    return api.get('/courses', { params });
  },
  getCourseById: async (id) => {
    if (USE_MOCK_DATA) {
      // Mock implementation
    }
    return api.get(`/courses/${id}`);
  },
  enrollInCourse: async (id) => {
    if (USE_MOCK_DATA) {
      // Mock implementation
    }
    return api.post(`/courses/${id}/enroll`);
  },
  updateProgress: async (id, progress) => {
    if (USE_MOCK_DATA) {
      // Mock implementation
    }
    return api.post(`/courses/${id}/progress`, { progress });
  },
};
```

#### Lesson Endpoints

```javascript
// Lesson services
export const lessonService = {
  getLessonsByCourse: async (courseId) => {
    if (USE_MOCK_DATA) {
      // Mock implementation
    }
    return api.get('/lessons', { params: { course_id: courseId } });
  },
  getLessonById: async (id) => {
    if (USE_MOCK_DATA) {
      // Mock implementation
    }
    return api.get(`/lessons/${id}`);
  },
};
```

## Testing the Integration

### Frontend Testing

1. Update the API service files as described above
2. Set `USE_MOCK_DATA = false` in the API service file
3. Run the frontend application:
   ```bash
   cd frontend
   npm run dev
   ```
4. Test the following functionality:
   - User registration and login
   - Course listing and details
   - Lesson viewing
   - Enrollment in courses
   - Theme and module management (for tenant users)
   - Marketplace browsing (for tenant users)

### Mobile App Testing

1. Update the API service files as described above
2. Set `USE_MOCK_DATA = false` in the API service file
3. Update the `YOUR_IP_ADDRESS` variable with your computer's IP address
4. Run the mobile application:
   ```bash
   cd mobile
   npx expo start
   ```
5. Test the following functionality:
   - User registration and login
   - Course listing and details
   - Lesson viewing
   - Enrollment in courses

## Troubleshooting

### CORS Issues

If you encounter CORS issues, ensure that the backend has the proper CORS configuration:

```php
// config/cors.php
return [
    'paths' => ['api/*', 'sanctum/csrf-cookie'],
    'allowed_methods' => ['*'],
    'allowed_origins' => ['*'], // In production, specify your frontend and mobile app origins
    'allowed_origins_patterns' => [],
    'allowed_headers' => ['*'],
    'exposed_headers' => [],
    'max_age' => 0,
    'supports_credentials' => true,
];
```

### Authentication Issues

If you encounter authentication issues:

1. Check that the token is being properly stored and sent with requests
2. Verify that the token refresh mechanism is working correctly
3. Check the backend logs for any authentication errors

### API Endpoint Issues

If you encounter issues with API endpoints:

1. Check that the endpoint paths are correct
2. Verify that the request parameters are correct
3. Check the backend logs for any errors
4. Use browser developer tools or Postman to test the API endpoints directly
