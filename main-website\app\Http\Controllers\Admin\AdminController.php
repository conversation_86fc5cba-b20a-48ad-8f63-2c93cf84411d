<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Config;

class AdminController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('admin');
    }

    /**
     * Get the admin config.
     *
     * @return array
     */
    protected function getAdminConfig()
    {
        return Config::get('admin');
    }

    /**
     * Get the view data for the admin layout.
     *
     * @param array $data
     * @return array
     */
    protected function getViewData(array $data = [])
    {
        return array_merge([
            'adminConfig' => $this->getAdminConfig(),
        ], $data);
    }

    /**
     * Return a view with admin layout data.
     *
     * @param string $view
     * @param array $data
     * @return \Illuminate\View\View
     */
    protected function adminView($view, array $data = [])
    {
        return view($view, $this->getViewData($data));
    }
}
