// Mock data for the mobile app to use when the backend is not available

export const mockCourses = [
  {
    id: 1,
    title: "Advanced Web Development",
    description: "Learn modern web development techniques with React, Node.js, and MongoDB. This comprehensive course covers frontend and backend development.",
    price: 99.99,
    thumbnail: "https://images.unsplash.com/photo-1498050108023-c5249f4df085?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2072&q=80",
    instructor: {
      name: "<PERSON>",
      id: 1,
      bio: "Senior Web Developer with 10+ years of experience"
    },
    tenant_id: 1,
    created_at: "2025-04-15T10:00:00Z",
    updated_at: "2025-04-15T10:00:00Z",
    enrollment_count: 245,
    lesson_count: 24,
    rating: 4.8,
    review_count: 156,
    lessons: [
      { id: 1, title: "Introduction to Modern Web Development", duration: "45 min" },
      { id: 2, title: "Setting Up Your Development Environment", duration: "30 min" },
      { id: 3, title: "React Fundamentals", duration: "60 min" },
      { id: 4, title: "State Management with Redux", duration: "55 min" },
      { id: 5, title: "Building APIs with Node.js", duration: "50 min" },
    ]
  },
  {
    id: 2,
    title: "Data Science Fundamentals",
    description: "Master the basics of data science, including statistics, Python, and machine learning. Perfect for beginners looking to enter the field.",
    price: 89.99,
    thumbnail: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    instructor: {
      name: "Emily Johnson",
      id: 2,
      bio: "Data Scientist at a Fortune 500 company with a PhD in Statistics"
    },
    tenant_id: 2,
    created_at: "2025-04-14T10:00:00Z",
    updated_at: "2025-04-14T10:00:00Z",
    enrollment_count: 189,
    lesson_count: 32,
    rating: 4.7,
    review_count: 123,
    lessons: [
      { id: 6, title: "Introduction to Data Science", duration: "40 min" },
      { id: 7, title: "Python for Data Analysis", duration: "65 min" },
      { id: 8, title: "Statistical Methods", duration: "55 min" },
      { id: 9, title: "Data Visualization", duration: "50 min" },
      { id: 10, title: "Machine Learning Basics", duration: "70 min" },
    ]
  },
  {
    id: 3,
    title: "UI/UX Design Principles",
    description: "Learn the fundamentals of user interface and user experience design. Create beautiful, intuitive designs that users will love.",
    price: 79.99,
    thumbnail: "https://images.unsplash.com/photo-1561070791-2526d30994b5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2064&q=80",
    instructor: {
      name: "Michael Brown",
      id: 3,
      bio: "Lead Designer with experience at top tech companies"
    },
    tenant_id: 3,
    created_at: "2025-04-13T10:00:00Z",
    updated_at: "2025-04-13T10:00:00Z",
    enrollment_count: 156,
    lesson_count: 18,
    rating: 4.9,
    review_count: 98,
    lessons: [
      { id: 11, title: "Design Thinking Process", duration: "35 min" },
      { id: 12, title: "User Research Methods", duration: "45 min" },
      { id: 13, title: "Wireframing and Prototyping", duration: "60 min" },
      { id: 14, title: "Visual Design Principles", duration: "50 min" },
      { id: 15, title: "Usability Testing", duration: "40 min" },
    ]
  },
  {
    id: 4,
    title: "Digital Marketing Basics",
    description: "Understand the fundamentals of digital marketing, including SEO, social media, and content marketing. Start growing your online presence.",
    price: 69.99,
    thumbnail: "https://images.unsplash.com/photo-1533750349088-cd871a92f312?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    instructor: {
      name: "Sarah Wilson",
      id: 4,
      bio: "Marketing Director with 15+ years in digital marketing"
    },
    tenant_id: 4,
    created_at: "2025-04-12T10:00:00Z",
    updated_at: "2025-04-12T10:00:00Z",
    enrollment_count: 134,
    lesson_count: 20,
    rating: 4.6,
    review_count: 87,
    lessons: [
      { id: 16, title: "Digital Marketing Overview", duration: "30 min" },
      { id: 17, title: "Search Engine Optimization", duration: "55 min" },
      { id: 18, title: "Social Media Marketing", duration: "50 min" },
      { id: 19, title: "Content Marketing Strategies", duration: "45 min" },
      { id: 20, title: "Email Marketing Campaigns", duration: "40 min" },
    ]
  },
  {
    id: 5,
    title: "Mobile App Development with Flutter",
    description: "Build cross-platform mobile applications using Flutter and Dart. Create beautiful, responsive apps for iOS and Android.",
    price: 109.99,
    thumbnail: "https://images.unsplash.com/photo-1526498460520-4c246339dccb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    instructor: {
      name: "David Lee",
      id: 5,
      bio: "Mobile Developer and Google Developer Expert"
    },
    tenant_id: 1,
    created_at: "2025-04-11T10:00:00Z",
    updated_at: "2025-04-11T10:00:00Z",
    enrollment_count: 122,
    lesson_count: 28,
    rating: 4.8,
    review_count: 76,
    lessons: [
      { id: 21, title: "Introduction to Flutter", duration: "40 min" },
      { id: 22, title: "Dart Programming Language", duration: "50 min" },
      { id: 23, title: "Building UI with Widgets", duration: "65 min" },
      { id: 24, title: "State Management", duration: "55 min" },
      { id: 25, title: "Publishing Your App", duration: "35 min" },
    ]
  },
  {
    id: 6,
    title: "Machine Learning Masterclass",
    description: "Dive deep into machine learning algorithms and applications. Learn how to build and deploy ML models in production.",
    price: 129.99,
    thumbnail: "https://images.unsplash.com/photo-1555949963-ff9fe0c870eb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    instructor: {
      name: "Robert Chen",
      id: 6,
      bio: "AI Researcher and former ML Engineer at a major tech company"
    },
    tenant_id: 2,
    created_at: "2025-04-10T10:00:00Z",
    updated_at: "2025-04-10T10:00:00Z",
    enrollment_count: 110,
    lesson_count: 36,
    rating: 4.9,
    review_count: 65,
    lessons: [
      { id: 26, title: "Machine Learning Fundamentals", duration: "60 min" },
      { id: 27, title: "Supervised Learning Algorithms", duration: "70 min" },
      { id: 28, title: "Unsupervised Learning", duration: "65 min" },
      { id: 29, title: "Deep Learning with Neural Networks", duration: "75 min" },
      { id: 30, title: "Model Deployment", duration: "50 min" },
    ]
  }
];

export const mockUsers = [
  {
    id: 1,
    name: "Admin User",
    email: "<EMAIL>",
    role: "admin",
    avatar: "https://i.pravatar.cc/150?img=1",
    tenant_id: null,
    created_at: "2025-04-01T10:00:00Z"
  },
  {
    id: 2,
    name: "Tech Academy Instructor",
    email: "<EMAIL>",
    role: "tenant",
    avatar: "https://i.pravatar.cc/150?img=2",
    tenant_id: 1,
    created_at: "2025-04-02T10:00:00Z"
  },
  {
    id: 3,
    name: "John Doe",
    email: "<EMAIL>",
    role: "student",
    avatar: "https://i.pravatar.cc/150?img=3",
    tenant_id: null,
    created_at: "2025-04-03T10:00:00Z"
  },
  {
    id: 4,
    name: "Jane Smith",
    email: "<EMAIL>",
    role: "student",
    avatar: "https://i.pravatar.cc/150?img=4",
    tenant_id: null,
    created_at: "2025-04-04T10:00:00Z"
  }
];

export const mockLessons = [
  {
    id: 1,
    course_id: 1,
    title: "Introduction to Modern Web Development",
    description: "Learn the basics of modern web development and the technologies we'll cover in this course.",
    video_url: "https://www.youtube.com/embed/dQw4w9WgXcQ",
    type: "video",
    order: 1,
    duration: "45 min"
  },
  {
    id: 2,
    course_id: 1,
    title: "Setting Up Your Development Environment",
    description: "Set up your local development environment with all the tools you'll need for this course.",
    video_url: "https://www.youtube.com/embed/dQw4w9WgXcQ",
    type: "video",
    order: 2,
    duration: "30 min"
  },
  {
    id: 3,
    course_id: 1,
    title: "React Fundamentals",
    description: "Learn the core concepts of React, including components, props, and state.",
    video_url: "https://www.youtube.com/embed/dQw4w9WgXcQ",
    type: "video",
    order: 3,
    duration: "60 min"
  },
  {
    id: 4,
    course_id: 1,
    title: "State Management with Redux",
    description: "Understand how to manage application state with Redux.",
    video_url: "https://www.youtube.com/embed/dQw4w9WgXcQ",
    type: "video",
    order: 4,
    duration: "55 min"
  },
  {
    id: 5,
    course_id: 1,
    title: "Building APIs with Node.js",
    description: "Learn how to create RESTful APIs using Node.js and Express.",
    video_url: "https://www.youtube.com/embed/dQw4w9WgXcQ",
    type: "video",
    order: 5,
    duration: "50 min"
  }
];

export default {
  mockCourses,
  mockUsers,
  mockLessons
};
