import React, { useContext, useState } from 'react';
import { View, StyleSheet, Image, TouchableOpacity, Alert, ScrollView } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

import Container from '@/components/ui/Container';
import Text from '@/components/ui/Text';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { AuthContext } from '@/context/AuthContext';
import { useTheme } from '@/components/ThemeProvider';

export default function ProfileScreen() {
  const { isAuthenticated, user, logout } = useContext(AuthContext);
  const { theme } = useTheme();
  const [loggingOut, setLoggingOut] = useState(false);

  const handleLogout = async () => {
    Alert.alert(
      'Confirm Logout',
      'Are you sure you want to log out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            setLoggingOut(true);
            try {
              await logout();
              router.replace('/');
            } catch (error) {
              console.error('Error logging out:', error);
              Alert.alert('Error', 'Failed to log out. Please try again.');
            } finally {
              setLoggingOut(false);
            }
          }
        }
      ]
    );
  };

  const menuItems = [
    {
      title: 'My Courses',
      icon: 'book-outline',
      onPress: () => router.push('/my-courses'),
    },
    {
      title: 'Certificates',
      icon: 'ribbon-outline',
      onPress: () => router.push('/certificates'),
    },
    {
      title: 'Notifications',
      icon: 'notifications-outline',
      onPress: () => router.push('/notifications'),
    },
    {
      title: 'Payment Methods',
      icon: 'card-outline',
      onPress: () => router.push('/payment-methods'),
    },
    {
      title: 'Account Settings',
      icon: 'settings-outline',
      onPress: () => router.push('/account-settings'),
    },
    {
      title: 'Help & Support',
      icon: 'help-circle-outline',
      onPress: () => router.push('/help-support'),
    },
  ];

  if (!isAuthenticated) {
    return (
      <Container style={styles.container}>
        <View style={styles.authContainer}>
          <Text variant="h2" style={styles.title}>Profile</Text>
          <Text style={styles.message}>Please log in to view your profile.</Text>
          <Button
            onPress={() => router.push('/login')}
            style={styles.loginButton}
          >
            Log In
          </Button>
        </View>
      </Container>
    );
  }

  return (
    <Container scroll style={styles.container}>
      <Text variant="h2" style={styles.title}>Profile</Text>

      <Card style={styles.profileCard}>
        <View style={styles.profileHeader}>
          <Image
            source={{ uri: user?.avatar || 'https://i.pravatar.cc/150' }}
            style={styles.avatar}
          />
          <View style={styles.profileInfo}>
            <Text variant="h3">{user?.name || 'User'}</Text>
            <Text variant="caption" color="muted">{user?.email || '<EMAIL>'}</Text>
            <View style={[styles.roleBadge, { backgroundColor: theme.primary }]}>
              <Text variant="caption" color="light">
                {user?.role === 'admin' ? 'Admin' :
                 user?.role === 'tenant' ? 'Instructor' : 'Student'}
              </Text>
            </View>
          </View>
        </View>
      </Card>

      <View style={styles.statsContainer}>
        <Card style={styles.statCard}>
          <Text variant="h3" color="primary">0</Text>
          <Text variant="caption">Courses Completed</Text>
        </Card>
        <Card style={styles.statCard}>
          <Text variant="h3" color="primary">0</Text>
          <Text variant="caption">Certificates</Text>
        </Card>
        <Card style={styles.statCard}>
          <Text variant="h3" color="primary">0</Text>
          <Text variant="caption">Hours Learned</Text>
        </Card>
      </View>

      <Text variant="subtitle" style={styles.sectionTitle}>Account</Text>

      {menuItems.map((item, index) => (
        <TouchableOpacity
          key={index}
          style={[styles.menuItem, { borderBottomColor: theme.border }]}
          onPress={item.onPress}
        >
          <View style={styles.menuItemContent}>
            <Ionicons name={item.icon} size={24} color={theme.textSecondary} style={styles.menuIcon} />
            <Text>{item.title}</Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color={theme.textSecondary} />
        </TouchableOpacity>
      ))}

      <Button
        variant="outline"
        onPress={handleLogout}
        loading={loggingOut}
        style={styles.logoutButton}
      >
        Logout
      </Button>

      <Text variant="caption" color="muted" style={styles.version}>
        Version 1.0.0
      </Text>
    </Container>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  title: {
    marginBottom: 20,
  },
  authContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  message: {
    marginBottom: 24,
    textAlign: 'center',
  },
  loginButton: {
    minWidth: 150,
  },
  profileCard: {
    marginBottom: 16,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginRight: 16,
  },
  profileInfo: {
    flex: 1,
  },
  roleBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginTop: 8,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  statCard: {
    flex: 1,
    marginHorizontal: 4,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  sectionTitle: {
    marginBottom: 12,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuIcon: {
    marginRight: 12,
  },
  logoutButton: {
    marginTop: 24,
    marginBottom: 16,
  },
  version: {
    textAlign: 'center',
    marginBottom: 24,
  },
});
