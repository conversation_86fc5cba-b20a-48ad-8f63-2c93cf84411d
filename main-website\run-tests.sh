#!/bin/bash

# Run tests for the unified Naxofy LMS platform
# This script runs tests for the backend, frontend, and mobile app

# Set colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Function to print section headers
print_header() {
    echo -e "\n${YELLOW}=======================================${NC}"
    echo -e "${YELLOW}$1${NC}"
    echo -e "${YELLOW}=======================================${NC}\n"
}

# Function to print success messages
print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

# Function to print error messages
print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# Function to print info messages
print_info() {
    echo -e "$1"
}

# Function to run a command and check its exit status
run_command() {
    print_info "Running: $1"
    eval $1
    if [ $? -eq 0 ]; then
        print_success "$2"
        return 0
    else
        print_error "$3"
        return 1
    fi
}

# Check if the script is run from the project root
if [ ! -f "artisan" ]; then
    print_error "This script must be run from the project root directory"
    exit 1
fi

# Check if the environment is set up for testing
if [ ! -f ".env.testing" ]; then
    print_info "Creating .env.testing file"
    cp .env.example .env.testing
    sed -i 's/DB_DATABASE=.*/DB_DATABASE=lms_testing/' .env.testing
    sed -i 's/CACHE_DRIVER=.*/CACHE_DRIVER=array/' .env.testing
    sed -i 's/SESSION_DRIVER=.*/SESSION_DRIVER=array/' .env.testing
    sed -i 's/QUEUE_CONNECTION=.*/QUEUE_CONNECTION=sync/' .env.testing
fi

# Check if the testing database exists
print_header "Setting up testing database"
DB_NAME=$(grep DB_DATABASE .env.testing | cut -d '=' -f2)
DB_USER=$(grep DB_USERNAME .env.testing | cut -d '=' -f2)
DB_PASS=$(grep DB_PASSWORD .env.testing | cut -d '=' -f2)

if ! mysql -u$DB_USER -p$DB_PASS -e "USE $DB_NAME" 2>/dev/null; then
    print_info "Creating testing database: $DB_NAME"
    mysql -u$DB_USER -p$DB_PASS -e "CREATE DATABASE $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci" 2>/dev/null
    if [ $? -eq 0 ]; then
        print_success "Testing database created"
    else
        print_error "Failed to create testing database"
        exit 1
    fi
else
    print_success "Testing database already exists"
fi

# Run backend tests
print_header "Running Backend Tests"

# Run PHPUnit tests
run_command "php artisan test --env=testing" "PHPUnit tests passed" "PHPUnit tests failed"
PHPUNIT_RESULT=$?

# Run backend linting
run_command "php -l app/" "PHP syntax check passed" "PHP syntax check failed"
LINT_RESULT=$?

# Run backend tests summary
if [ $PHPUNIT_RESULT -eq 0 ] && [ $LINT_RESULT -eq 0 ]; then
    print_success "All backend tests passed"
else
    print_error "Some backend tests failed"
fi

# Run frontend tests
print_header "Running Frontend Tests"

# Check if frontend directory exists
if [ -d "frontend" ]; then
    cd frontend

    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        print_info "Installing frontend dependencies"
        run_command "npm install" "Frontend dependencies installed" "Failed to install frontend dependencies"
    fi

    # Run ESLint
    run_command "npm run lint" "ESLint passed" "ESLint failed"
    ESLINT_RESULT=$?

    # Run frontend tests
    run_command "npm test -- --watchAll=false" "Frontend tests passed" "Frontend tests failed"
    FRONTEND_TEST_RESULT=$?

    # Run frontend build
    run_command "npm run build" "Frontend build passed" "Frontend build failed"
    FRONTEND_BUILD_RESULT=$?

    # Frontend tests summary
    if [ $ESLINT_RESULT -eq 0 ] && [ $FRONTEND_TEST_RESULT -eq 0 ] && [ $FRONTEND_BUILD_RESULT -eq 0 ]; then
        print_success "All frontend tests passed"
    else
        print_error "Some frontend tests failed"
    fi

    cd ..
else
    print_info "Frontend directory not found, skipping frontend tests"
fi

# Run mobile app tests
print_header "Running Mobile App Tests"

# Check if mobile directory exists
if [ -d "mobile" ]; then
    cd mobile

    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        print_info "Installing mobile app dependencies"
        run_command "npm install" "Mobile app dependencies installed" "Failed to install mobile app dependencies"
    fi

    # Run ESLint
    run_command "npm run lint" "ESLint passed" "ESLint failed"
    MOBILE_ESLINT_RESULT=$?

    # Run mobile app tests
    run_command "npm test -- --watchAll=false" "Mobile app tests passed" "Mobile app tests failed"
    MOBILE_TEST_RESULT=$?

    # Mobile app tests summary
    if [ $MOBILE_ESLINT_RESULT -eq 0 ] && [ $MOBILE_TEST_RESULT -eq 0 ]; then
        print_success "All mobile app tests passed"
    else
        print_error "Some mobile app tests failed"
    fi

    cd ..
else
    print_info "Mobile directory not found, skipping mobile app tests"
fi

# Run integration tests
print_header "Running Integration Tests"

# Run API tests
run_command "php artisan test --testsuite=Feature --env=testing" "API tests passed" "API tests failed"
API_TEST_RESULT=$?

# Integration tests summary
if [ $API_TEST_RESULT -eq 0 ]; then
    print_success "All integration tests passed"
else
    print_error "Some integration tests failed"
fi

# Overall summary
print_header "Test Summary"

if [ $PHPUNIT_RESULT -eq 0 ] && [ $LINT_RESULT -eq 0 ] && [ $API_TEST_RESULT -eq 0 ]; then
    if [ -d "frontend" ]; then
        if [ $ESLINT_RESULT -eq 0 ] && [ $FRONTEND_TEST_RESULT -eq 0 ] && [ $FRONTEND_BUILD_RESULT -eq 0 ]; then
            FRONTEND_PASSED=true
        else
            FRONTEND_PASSED=false
        fi
    else
        FRONTEND_PASSED=true
    fi

    if [ -d "mobile" ]; then
        if [ $MOBILE_ESLINT_RESULT -eq 0 ] && [ $MOBILE_TEST_RESULT -eq 0 ]; then
            MOBILE_PASSED=true
        else
            MOBILE_PASSED=false
        fi
    else
        MOBILE_PASSED=true
    fi

    if [ "$FRONTEND_PASSED" = true ] && [ "$MOBILE_PASSED" = true ]; then
        print_success "All tests passed! The application is ready for deployment."
        exit 0
    else
        print_error "Some tests failed. Please fix the issues before deploying."
        exit 1
    fi
else
    print_error "Some tests failed. Please fix the issues before deploying."
    exit 1
fi
