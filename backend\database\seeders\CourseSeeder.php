<?php

namespace Database\Seeders;

use App\Models\Course;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Database\Seeder;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a tenant if none exists
        $tenant = Tenant::firstOrCreate(
            ['name' => 'Demo Academy'],
            [
                'domain' => 'demo-academy',
                'is_active' => true,
            ]
        );

        // Create an instructor if none exists
        $instructor = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => '<PERSON> Instructor',
                'password' => bcrypt('password'),
                'role' => 'tenant',
                'tenant_id' => $tenant->id,
            ]
        );

        // Create demo courses
        $courses = [
            [
                'title' => 'Introduction to Web Development',
                'description' => 'Learn the basics of HTML, CSS, and JavaScript to build your first website. This course is perfect for beginners with no prior coding experience.',
                'price' => 49.99,
                'tenant_id' => $tenant->id,
                'instructor_id' => $instructor->id,
                'thumbnail' => 'https://source.unsplash.com/random/800x600/?coding',
            ],
            [
                'title' => 'Advanced React Development',
                'description' => 'Take your React skills to the next level with advanced concepts like hooks, context API, and Redux. Build complex applications with modern React patterns.',
                'price' => 79.99,
                'tenant_id' => $tenant->id,
                'instructor_id' => $instructor->id,
                'thumbnail' => 'https://source.unsplash.com/random/800x600/?react',
            ],
            [
                'title' => 'Full Stack Laravel & Vue',
                'description' => 'Build complete web applications with Laravel backend and Vue.js frontend. Learn authentication, API development, and modern frontend techniques.',
                'price' => 89.99,
                'tenant_id' => $tenant->id,
                'instructor_id' => $instructor->id,
                'thumbnail' => 'https://source.unsplash.com/random/800x600/?laravel',
            ],
            [
                'title' => 'Mobile App Development with React Native',
                'description' => 'Create native mobile apps for iOS and Android using React Native. Learn once, deploy everywhere with this comprehensive course.',
                'price' => 69.99,
                'tenant_id' => $tenant->id,
                'instructor_id' => $instructor->id,
                'thumbnail' => 'https://source.unsplash.com/random/800x600/?mobile',
            ],
            [
                'title' => 'Python for Data Science',
                'description' => 'Master Python for data analysis, visualization, and machine learning. Learn pandas, NumPy, matplotlib, and scikit-learn libraries.',
                'price' => 59.99,
                'tenant_id' => $tenant->id,
                'instructor_id' => $instructor->id,
                'thumbnail' => 'https://source.unsplash.com/random/800x600/?python',
            ],
            [
                'title' => 'UI/UX Design Fundamentals',
                'description' => 'Learn the principles of user interface and user experience design. Create beautiful, intuitive designs that users will love.',
                'price' => 49.99,
                'tenant_id' => $tenant->id,
                'instructor_id' => $instructor->id,
                'thumbnail' => 'https://source.unsplash.com/random/800x600/?design',
            ],
        ];

        foreach ($courses as $courseData) {
            Course::firstOrCreate(
                ['title' => $courseData['title']],
                $courseData
            );
        }
    }
}
