@extends('marketing.layouts.app')

@section('title', 'Launch Your Own Learning Platform')

@section('content')
    <!-- Hero Section -->
    <div class="relative bg-white overflow-hidden">
        <div class="max-w-7xl mx-auto">
            <div class="relative z-10 pb-8 bg-white sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32">
                <svg class="hidden lg:block absolute right-0 inset-y-0 h-full w-48 text-white transform translate-x-1/2" fill="currentColor" viewBox="0 0 100 100" preserveAspectRatio="none" aria-hidden="true">
                    <polygon points="50,0 100,0 50,100 0,100" />
                </svg>

                <div class="pt-10 mx-auto max-w-7xl px-4 sm:pt-12 sm:px-6 md:pt-16 lg:pt-20 lg:px-8 xl:pt-28">
                    <div class="sm:text-center lg:text-left">
                        <h1 class="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">
                            <span class="block xl:inline">Launch your own</span>
                            <span class="block text-primary-600 xl:inline">Online Learning Platform</span>
                            <span class="block xl:inline">in Minutes</span>
                        </h1>
                        <p class="mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0">
                            Create your own branded learning platform with customizable themes, mobile apps, and powerful features. Start your free trial today and transform your educational content into a thriving online business.
                        </p>
                        <div class="mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start">
                            <div class="rounded-md shadow">
                                <a href="{{ route('signup') }}" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 md:py-4 md:text-lg md:px-10">
                                    Start Free Trial
                                </a>
                            </div>
                            <div class="mt-3 sm:mt-0 sm:ml-3">
                                <a href="#" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 md:py-4 md:text-lg md:px-10">
                                    Watch Demo
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2">
            <img class="h-56 w-full object-cover sm:h-72 md:h-96 lg:w-full lg:h-full" src="https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=2850&q=80" alt="">
        </div>
    </div>

    <!-- Features Overview -->
    <div class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="lg:text-center">
                <h2 class="text-base text-primary-600 font-semibold tracking-wide uppercase">Features</h2>
                <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
                    Everything you need to run your online academy
                </p>
                <p class="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
                    Our platform provides all the tools you need to create, manage, and grow your online learning business.
                </p>
            </div>

            <div class="mt-10">
                <dl class="space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10">
                    <div class="relative">
                        <dt>
                            <div class="absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white">
                                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                </svg>
                            </div>
                            <p class="ml-16 text-lg leading-6 font-medium text-gray-900">Course Builder</p>
                        </dt>
                        <dd class="mt-2 ml-16 text-base text-gray-500">
                            Create engaging courses with videos, quizzes, assignments, and more. Our intuitive course builder makes it easy to organize your content.
                        </dd>
                    </div>

                    <div class="relative">
                        <dt>
                            <div class="absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white">
                                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                                </svg>
                            </div>
                            <p class="ml-16 text-lg leading-6 font-medium text-gray-900">Theme Customizer</p>
                        </dt>
                        <dd class="mt-2 ml-16 text-base text-gray-500">
                            Customize the look and feel of your learning platform with our powerful theme editor. Choose from pre-built themes or create your own.
                        </dd>
                    </div>

                    <div class="relative">
                        <dt>
                            <div class="absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white">
                                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 14v6m-3-3h6M6 10h2a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v2a2 2 0 002 2zm10 0h2a2 2 0 002-2V6a2 2 0 00-2-2h-2a2 2 0 00-2 2v2a2 2 0 002 2zM6 20h2a2 2 0 002-2v-2a2 2 0 00-2-2H6a2 2 0 00-2 2v2a2 2 0 002 2z" />
                                </svg>
                            </div>
                            <p class="ml-16 text-lg leading-6 font-medium text-gray-900">App Store</p>
                        </dt>
                        <dd class="mt-2 ml-16 text-base text-gray-500">
                            Extend your platform's functionality with our marketplace of extensions. Add certificates, gamification, social features, and more.
                        </dd>
                    </div>

                    <div class="relative">
                        <dt>
                            <div class="absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white">
                                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                </svg>
                            </div>
                            <p class="ml-16 text-lg leading-6 font-medium text-gray-900">Mobile App</p>
                        </dt>
                        <dd class="mt-2 ml-16 text-base text-gray-500">
                            Offer your students a branded mobile app experience. Our platform automatically generates iOS and Android apps for your academy.
                        </dd>
                    </div>
                </dl>
            </div>
        </div>
    </div>

    <!-- How It Works -->
    <div class="py-12 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="lg:text-center">
                <h2 class="text-base text-primary-600 font-semibold tracking-wide uppercase">How It Works</h2>
                <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
                    Three simple steps to launch your platform
                </p>
            </div>

            <div class="mt-10">
                <div class="flex flex-col md:flex-row justify-between">
                    <div class="md:w-1/3 p-4 text-center">
                        <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white mx-auto">
                            <span class="text-lg font-bold">1</span>
                        </div>
                        <h3 class="mt-4 text-lg font-medium text-gray-900">Sign Up</h3>
                        <p class="mt-2 text-base text-gray-500">
                            Create your account and choose your subdomain. Our platform will set up your learning environment in minutes.
                        </p>
                    </div>

                    <div class="md:w-1/3 p-4 text-center">
                        <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white mx-auto">
                            <span class="text-lg font-bold">2</span>
                        </div>
                        <h3 class="mt-4 text-lg font-medium text-gray-900">Customize</h3>
                        <p class="mt-2 text-base text-gray-500">
                            Choose your theme, add your branding, and customize your platform to match your vision. Add courses and content.
                        </p>
                    </div>

                    <div class="md:w-1/3 p-4 text-center">
                        <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white mx-auto">
                            <span class="text-lg font-bold">3</span>
                        </div>
                        <h3 class="mt-4 text-lg font-medium text-gray-900">Launch</h3>
                        <p class="mt-2 text-base text-gray-500">
                            Publish your platform and start enrolling students. Grow your online learning business with our powerful tools.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Featured Courses -->
    <div class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="lg:text-center">
                <h2 class="text-base text-primary-600 font-semibold tracking-wide uppercase">Featured Courses</h2>
                <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
                    Example courses from our platform
                </p>
                <p class="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
                    See how your courses could look on our platform. These are examples of courses created by our users.
                </p>
            </div>

            <div class="mt-10 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
                @foreach($featuredCourses as $course)
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="relative pb-48 overflow-hidden">
                        <img class="absolute inset-0 h-full w-full object-cover" src="{{ $course['thumbnail'] }}" alt="{{ $course['title'] }}">
                    </div>
                    <div class="p-6">
                        <div class="flex items-baseline">
                            <span class="inline-block px-2 py-1 leading-none bg-primary-100 text-primary-800 rounded-full font-semibold uppercase tracking-wide text-xs">Featured</span>
                        </div>
                        <h2 class="mt-2 mb-2 font-bold text-xl">{{ $course['title'] }}</h2>
                        <p class="text-sm text-gray-600 mb-4">{{ $course['description'] }}</p>
                        <div class="mt-3 flex items-center">
                            <span class="text-sm font-semibold">Instructor:</span>&nbsp;
                            <span class="text-sm text-gray-600">{{ $course['instructor'] }}</span>
                        </div>
                        <div class="mt-3 flex items-center">
                            <span class="flex items-center">
                                @for($i = 1; $i <= 5; $i++)
                                    @if($i <= floor($course['rating']))
                                        <svg class="w-4 h-4 text-yellow-500 fill-current" viewBox="0 0 24 24">
                                            <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"></path>
                                        </svg>
                                    @elseif($i - 0.5 <= $course['rating'])
                                        <svg class="w-4 h-4 text-yellow-500 fill-current" viewBox="0 0 24 24">
                                            <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"></path>
                                        </svg>
                                    @else
                                        <svg class="w-4 h-4 text-gray-400 fill-current" viewBox="0 0 24 24">
                                            <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"></path>
                                        </svg>
                                    @endif
                                @endfor
                            </span>
                            <span class="ml-2 text-gray-600 text-sm">({{ $course['ratings_count'] }})</span>
                        </div>
                        <div class="mt-4 flex items-center justify-between">
                            <span class="text-xl font-bold text-primary-600">${{ $course['price'] }}</span>
                            <button class="px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded hover:bg-primary-500 focus:outline-none focus:bg-primary-500">View Course</button>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Testimonials -->
    <div class="py-12 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="lg:text-center">
                <h2 class="text-base text-primary-600 font-semibold tracking-wide uppercase">Testimonials</h2>
                <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
                    What our customers say
                </p>
            </div>

            <div class="mt-10 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
                @foreach($testimonials as $testimonial)
                <div class="bg-white overflow-hidden shadow rounded-lg p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-12 w-12">
                            <img class="h-12 w-12 rounded-full" src="{{ $testimonial['avatar'] }}" alt="{{ $testimonial['name'] }}">
                        </div>
                        <div class="ml-4">
                            <h4 class="text-lg font-bold text-gray-900">{{ $testimonial['name'] }}</h4>
                            <p class="text-sm text-gray-500">{{ $testimonial['role'] }}</p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <p class="text-gray-600 italic">"{{ $testimonial['content'] }}"</p>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- FAQ Preview -->
    <div class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="lg:text-center">
                <h2 class="text-base text-primary-600 font-semibold tracking-wide uppercase">FAQ</h2>
                <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
                    Frequently asked questions
                </p>
            </div>

            <div class="mt-10">
                <dl class="space-y-6 divide-y divide-gray-200">
                    <div class="pt-6">
                        <dt class="text-lg font-medium text-gray-900">
                            How much does it cost to use the platform?
                        </dt>
                        <dd class="mt-2 text-base text-gray-500">
                            We offer flexible pricing plans starting at $49/month. You can view our detailed pricing on our <a href="{{ route('pricing') }}" class="text-primary-600 hover:text-primary-500">pricing page</a>. We also offer a 14-day free trial so you can test all features before committing.
                        </dd>
                    </div>

                    <div class="pt-6">
                        <dt class="text-lg font-medium text-gray-900">
                            Can I use my own domain name?
                        </dt>
                        <dd class="mt-2 text-base text-gray-500">
                            Yes, you can use your own custom domain with our platform. We provide easy instructions for setting up your domain with our service.
                        </dd>
                    </div>

                    <div class="pt-6">
                        <dt class="text-lg font-medium text-gray-900">
                            How do I get paid for my courses?
                        </dt>
                        <dd class="mt-2 text-base text-gray-500">
                            We integrate with popular payment processors like Stripe and PayPal. You can easily connect your accounts and start receiving payments directly to your bank account.
                        </dd>
                    </div>

                    <div class="pt-6">
                        <dt class="text-lg font-medium text-gray-900">
                            Can I migrate my existing courses?
                        </dt>
                        <dd class="mt-2 text-base text-gray-500">
                            Yes, we offer migration services to help you move your existing courses from other platforms. Our team can assist with the migration process to ensure a smooth transition.
                        </dd>
                    </div>
                </dl>
            </div>
        </div>
    </div>

    <!-- CTA Footer -->
    <div class="bg-primary-700">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8 lg:flex lg:items-center lg:justify-between">
            <h2 class="text-3xl font-extrabold tracking-tight text-white sm:text-4xl">
                <span class="block">Ready to start your learning platform?</span>
                <span class="block text-primary-200">Start your free trial today.</span>
            </h2>
            <div class="mt-8 flex lg:mt-0 lg:flex-shrink-0">
                <div class="inline-flex rounded-md shadow">
                    <a href="{{ route('signup') }}" class="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-primary-600 bg-white hover:bg-primary-50">
                        Start free trial
                    </a>
                </div>
                <div class="ml-3 inline-flex rounded-md shadow">
                    <a href="{{ route('features') }}" class="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
                        Learn more
                    </a>
                </div>
            </div>
        </div>
    </div>
@endsection
