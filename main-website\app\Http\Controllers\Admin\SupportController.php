<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SupportTicket;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class SupportController extends Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        // Check if user is admin for all methods
        $this->middleware(function ($request, $next) {
            if (!Auth::check() || !Auth::user()->isAdmin()) {
                return redirect()->route('login')->with('error', 'You do not have permission to access this area.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of the support tickets.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        // Get filter parameters
        $status = $request->input('status');
        $priority = $request->input('priority');
        $search = $request->input('search');

        // Get tickets from database or use mock data if table doesn't exist yet
        try {
            $query = SupportTicket::query();
            
            // Apply filters
            if ($status) {
                $query->where('status', $status);
            }
            
            if ($priority) {
                $query->where('priority', $priority);
            }
            
            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('subject', 'like', "%{$search}%")
                      ->orWhere('ticket_id', 'like', "%{$search}%");
                });
            }
            
            $tickets = $query->with(['user', 'assignedTo'])->orderBy('created_at', 'desc')->paginate(10);
            
            if ($tickets->isEmpty() && !$status && !$priority && !$search) {
                $tickets = $this->getMockTickets();
            }
        } catch (\Exception $e) {
            $tickets = $this->getMockTickets();
        }

        // Stats for ticket counts
        $ticketStats = [
            'total' => SupportTicket::count() ?: 25,
            'open' => SupportTicket::where('status', 'open')->count() ?: 10,
            'in_progress' => SupportTicket::where('status', 'in_progress')->count() ?: 8,
            'resolved' => SupportTicket::where('status', 'resolved')->count() ?: 7,
        ];

        return view('admin.support.index', compact('tickets', 'ticketStats', 'status', 'priority', 'search'));
    }

    /**
     * Display the specified support ticket.
     *
     * @param  string  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        try {
            $ticket = SupportTicket::with(['user', 'assignedTo', 'replies'])->where('ticket_id', $id)->firstOrFail();
        } catch (\Exception $e) {
            // Use mock data if ticket not found
            $ticket = collect($this->getMockTickets())->firstWhere('ticket_id', $id);
            
            if (!$ticket) {
                return redirect()->route('admin.support.index')
                    ->with('error', 'Support ticket not found.');
            }
        }

        // Get admin users for assignment
        try {
            $admins = User::where('role', 'admin')->where('status', 'active')->get(['id', 'name']);
        } catch (\Exception $e) {
            $admins = collect();
        }

        return view('admin.support.show', compact('ticket', 'admins'));
    }

    /**
     * Update the status of the specified support ticket.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateStatus(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:open,in_progress,resolved,closed',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $ticket = SupportTicket::where('ticket_id', $id)->firstOrFail();
            
            $ticket->update([
                'status' => $request->status,
            ]);
            
            return redirect()->route('admin.support.show', $ticket->ticket_id)
                ->with('success', 'Ticket status updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update ticket status: ' . $e->getMessage());
        }
    }

    /**
     * Update the priority of the specified support ticket.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updatePriority(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'priority' => 'required|in:low,medium,high,critical',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $ticket = SupportTicket::where('ticket_id', $id)->firstOrFail();
            
            $ticket->update([
                'priority' => $request->priority,
            ]);
            
            return redirect()->route('admin.support.show', $ticket->ticket_id)
                ->with('success', 'Ticket priority updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update ticket priority: ' . $e->getMessage());
        }
    }

    /**
     * Assign the specified support ticket to an admin.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function assign(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'assigned_to' => 'required|exists:users,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $ticket = SupportTicket::where('ticket_id', $id)->firstOrFail();
            
            $ticket->update([
                'assigned_to' => $request->assigned_to,
            ]);
            
            return redirect()->route('admin.support.show', $ticket->ticket_id)
                ->with('success', 'Ticket assigned successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to assign ticket: ' . $e->getMessage());
        }
    }

    /**
     * Add a reply to the specified support ticket.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function reply(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'message' => 'required|string',
            'is_private' => 'boolean',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $ticket = SupportTicket::where('ticket_id', $id)->firstOrFail();
            
            // Add reply
            $reply = $ticket->replies()->create([
                'user_id' => Auth::id(),
                'message' => $request->message,
                'is_private' => $request->has('is_private'),
            ]);
            
            // Update ticket status if it's open
            if ($ticket->status === 'open') {
                $ticket->update([
                    'status' => 'in_progress',
                ]);
            }
            
            return redirect()->route('admin.support.show', $ticket->ticket_id)
                ->with('success', 'Reply added successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to add reply: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Get mock tickets data.
     *
     * @return \Illuminate\Support\Collection
     */
    private function getMockTickets()
    {
        return collect([
            (object) [
                'id' => 1,
                'ticket_id' => 'TKT-1001',
                'subject' => 'Cannot access my courses',
                'message' => 'I\'m having trouble accessing my courses. When I click on a course, I get a 404 error.',
                'status' => 'open',
                'priority' => 'high',
                'user_id' => 4,
                'assigned_to' => null,
                'created_at' => now()->subDays(2),
                'updated_at' => now()->subDays(2),
                'user' => (object) [
                    'id' => 4,
                    'name' => 'Bob Johnson',
                    'email' => '<EMAIL>',
                ],
                'assignedTo' => null,
                'replies' => [],
            ],
            (object) [
                'id' => 2,
                'ticket_id' => 'TKT-1002',
                'subject' => 'Payment failed',
                'message' => 'I tried to upgrade my plan but the payment failed. My card was charged but my plan wasn\'t upgraded.',
                'status' => 'in_progress',
                'priority' => 'critical',
                'user_id' => 2,
                'assigned_to' => 1,
                'created_at' => now()->subDays(3),
                'updated_at' => now()->subDay(),
                'user' => (object) [
                    'id' => 2,
                    'name' => 'John Doe',
                    'email' => '<EMAIL>',
                ],
                'assignedTo' => (object) [
                    'id' => 1,
                    'name' => 'Admin User',
                ],
                'replies' => [
                    (object) [
                        'id' => 1,
                        'ticket_id' => 2,
                        'user_id' => 1,
                        'message' => 'I\'m looking into this issue. Could you please provide your transaction ID?',
                        'is_private' => false,
                        'created_at' => now()->subDays(2),
                        'user' => (object) [
                            'id' => 1,
                            'name' => 'Admin User',
                        ],
                    ],
                    (object) [
                        'id' => 2,
                        'ticket_id' => 2,
                        'user_id' => 2,
                        'message' => 'The transaction ID is TXN-12345.',
                        'is_private' => false,
                        'created_at' => now()->subDays(1)->addHours(2),
                        'user' => (object) [
                            'id' => 2,
                            'name' => 'John Doe',
                        ],
                    ],
                    (object) [
                        'id' => 3,
                        'ticket_id' => 2,
                        'user_id' => 1,
                        'message' => 'I\'ve found the transaction. It\'s pending on our end. I\'ll escalate this to our payment processor.',
                        'is_private' => true,
                        'created_at' => now()->subDay(),
                        'user' => (object) [
                            'id' => 1,
                            'name' => 'Admin User',
                        ],
                    ],
                ],
            ],
            (object) [
                'id' => 3,
                'ticket_id' => 'TKT-1003',
                'subject' => 'How to create a quiz?',
                'message' => 'I\'m trying to create a quiz for my course but I can\'t find the option. Can you help?',
                'status' => 'resolved',
                'priority' => 'medium',
                'user_id' => 3,
                'assigned_to' => 1,
                'created_at' => now()->subDays(5),
                'updated_at' => now()->subDays(3),
                'user' => (object) [
                    'id' => 3,
                    'name' => 'Jane Smith',
                    'email' => '<EMAIL>',
                ],
                'assignedTo' => (object) [
                    'id' => 1,
                    'name' => 'Admin User',
                ],
                'replies' => [
                    (object) [
                        'id' => 4,
                        'ticket_id' => 3,
                        'user_id' => 1,
                        'message' => 'To create a quiz, go to your course, click on "Add Content", and select "Quiz" from the dropdown menu. Let me know if you need more help!',
                        'is_private' => false,
                        'created_at' => now()->subDays(4),
                        'user' => (object) [
                            'id' => 1,
                            'name' => 'Admin User',
                        ],
                    ],
                    (object) [
                        'id' => 5,
                        'ticket_id' => 3,
                        'user_id' => 3,
                        'message' => 'Thank you! I found it and was able to create my quiz.',
                        'is_private' => false,
                        'created_at' => now()->subDays(3),
                        'user' => (object) [
                            'id' => 3,
                            'name' => 'Jane Smith',
                        ],
                    ],
                ],
            ],
            (object) [
                'id' => 4,
                'ticket_id' => 'TKT-1004',
                'subject' => 'Custom domain setup',
                'message' => 'I\'m having trouble setting up my custom domain. I\'ve added the DNS records as instructed but it\'s not working.',
                'status' => 'open',
                'priority' => 'medium',
                'user_id' => 6,
                'assigned_to' => null,
                'created_at' => now()->subHours(12),
                'updated_at' => now()->subHours(12),
                'user' => (object) [
                    'id' => 6,
                    'name' => 'Charlie Brown',
                    'email' => '<EMAIL>',
                ],
                'assignedTo' => null,
                'replies' => [],
            ],
            (object) [
                'id' => 5,
                'ticket_id' => 'TKT-1005',
                'subject' => 'Feature request: Certificate templates',
                'message' => 'I would like to request a feature to customize certificate templates. Currently, the options are limited.',
                'status' => 'in_progress',
                'priority' => 'low',
                'user_id' => 5,
                'assigned_to' => 1,
                'created_at' => now()->subDays(7),
                'updated_at' => now()->subDays(6),
                'user' => (object) [
                    'id' => 5,
                    'name' => 'Alice Williams',
                    'email' => '<EMAIL>',
                ],
                'assignedTo' => (object) [
                    'id' => 1,
                    'name' => 'Admin User',
                ],
                'replies' => [
                    (object) [
                        'id' => 6,
                        'ticket_id' => 5,
                        'user_id' => 1,
                        'message' => 'Thank you for your suggestion! We\'re actually working on enhancing certificate templates in our next update. I\'ll add your request to our feature list.',
                        'is_private' => false,
                        'created_at' => now()->subDays(6),
                        'user' => (object) [
                            'id' => 1,
                            'name' => 'Admin User',
                        ],
                    ],
                ],
            ],
        ]);
    }
}
