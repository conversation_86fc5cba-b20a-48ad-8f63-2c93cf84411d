import React, { useState, useContext } from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

import Container from '@/components/ui/Container';
import Text from '@/components/ui/Text';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { AuthContext } from '@/context/AuthContext';
import { useTheme } from '@/components/ThemeProvider';

// Mock payment methods data
const mockPaymentMethods = [
  {
    id: 1,
    type: 'credit_card',
    brand: 'Visa',
    last4: '4242',
    expMonth: 12,
    expYear: 2025,
    isDefault: true,
  },
  {
    id: 2,
    type: 'credit_card',
    brand: 'Mastercard',
    last4: '5555',
    expMonth: 8,
    expYear: 2026,
    isDefault: false,
  },
];

export default function PaymentMethodsScreen() {
  const [paymentMethods, setPaymentMethods] = useState(mockPaymentMethods);
  const [loading, setLoading] = useState(false);
  const { isAuthenticated, user } = useContext(AuthContext);
  const { theme } = useTheme();

  const handleBack = () => {
    router.back();
  };

  const handleAddPaymentMethod = () => {
    // In a real app, this would navigate to a form to add a new payment method
    Alert.alert(
      'Add Payment Method',
      'This would open a form to add a new payment method in a real app.'
    );
  };

  const handleSetDefault = (id) => {
    const updatedMethods = paymentMethods.map(method => ({
      ...method,
      isDefault: method.id === id
    }));
    setPaymentMethods(updatedMethods);
  };

  const handleRemove = (id) => {
    Alert.alert(
      'Remove Payment Method',
      'Are you sure you want to remove this payment method?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Remove',
          onPress: () => {
            const updatedMethods = paymentMethods.filter(method => method.id !== id);
            setPaymentMethods(updatedMethods);
          },
          style: 'destructive',
        },
      ]
    );
  };

  const getCardIcon = (brand) => {
    switch (brand.toLowerCase()) {
      case 'visa':
        return 'card-outline';
      case 'mastercard':
        return 'card-outline';
      case 'amex':
        return 'card-outline';
      default:
        return 'card-outline';
    }
  };

  const renderPaymentMethodItem = ({ item }) => (
    <Card style={styles.paymentCard}>
      <View style={styles.paymentCardContent}>
        <View style={styles.cardInfo}>
          <View style={[styles.cardIconContainer, { backgroundColor: theme.primary + '20' }]}>
            <Ionicons name={getCardIcon(item.brand)} size={24} color={theme.primary} />
          </View>
          <View style={styles.cardDetails}>
            <Text variant="subtitle" style={styles.cardTitle}>
              {item.brand} •••• {item.last4}
            </Text>
            <Text variant="caption" color="muted">
              Expires {item.expMonth}/{item.expYear}
            </Text>
            {item.isDefault && (
              <View style={styles.defaultBadge}>
                <Text variant="caption" style={styles.defaultText}>Default</Text>
              </View>
            )}
          </View>
        </View>
        
        <View style={styles.cardActions}>
          {!item.isDefault && (
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => handleSetDefault(item.id)}
            >
              <Text color="primary">Set Default</Text>
            </TouchableOpacity>
          )}
          <TouchableOpacity 
            style={styles.actionButton}
            onPress={() => handleRemove(item.id)}
          >
            <Text color="error">Remove</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Card>
  );

  if (!isAuthenticated) {
    return (
      <Container style={styles.container}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Ionicons name="arrow-back" size={24} color={theme.text} />
        </TouchableOpacity>
        
        <View style={styles.authContainer}>
          <Text variant="h2" style={styles.title}>Payment Methods</Text>
          <Text style={styles.message}>Please log in to manage your payment methods.</Text>
          <Button 
            onPress={() => router.push('/login')}
            style={styles.loginButton}
          >
            Log In
          </Button>
        </View>
      </Container>
    );
  }

  return (
    <Container style={styles.container}>
      <TouchableOpacity style={styles.backButton} onPress={handleBack}>
        <Ionicons name="arrow-back" size={24} color={theme.text} />
      </TouchableOpacity>
      
      <Text variant="h2" style={styles.title}>Payment Methods</Text>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.primary} />
        </View>
      ) : (
        <>
          <FlatList
            data={paymentMethods}
            renderItem={renderPaymentMethodItem}
            keyExtractor={(item) => item.id.toString()}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="card-outline" size={64} color={theme.textSecondary} />
                <Text style={styles.emptyText}>
                  You don't have any payment methods yet.
                </Text>
              </View>
            }
          />
          
          <Button 
            onPress={handleAddPaymentMethod}
            style={styles.addButton}
          >
            <View style={styles.buttonContent}>
              <Ionicons name="add-circle-outline" size={20} color="white" />
              <Text color="light" style={styles.buttonText}>Add Payment Method</Text>
            </View>
          </Button>
        </>
      )}
    </Container>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backButton: {
    position: 'absolute',
    top: 50,
    left: 16,
    zIndex: 10,
    padding: 8,
  },
  title: {
    marginTop: 60,
    marginBottom: 20,
  },
  paymentCard: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  paymentCardContent: {
    padding: 16,
  },
  cardInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  cardDetails: {
    flex: 1,
  },
  cardTitle: {
    fontWeight: '600',
  },
  defaultBadge: {
    backgroundColor: '#10b981',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
    marginTop: 4,
  },
  defaultText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  cardActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  actionButton: {
    marginLeft: 16,
    padding: 4,
  },
  listContent: {
    paddingBottom: 80,
  },
  addButton: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    marginLeft: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    marginTop: 40,
  },
  emptyText: {
    marginTop: 16,
    textAlign: 'center',
  },
  authContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  message: {
    marginBottom: 24,
    textAlign: 'center',
  },
  loginButton: {
    minWidth: 150,
  },
});
