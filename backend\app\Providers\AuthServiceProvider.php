<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        // Define roles
        Gate::define('admin', function ($user) {
            return $user->role === 'admin';
        });

        Gate::define('tenant', function ($user) {
            return $user->role === 'tenant';
        });

        Gate::define('student', function ($user) {
            return $user->role === 'student';
        });
    }
}
