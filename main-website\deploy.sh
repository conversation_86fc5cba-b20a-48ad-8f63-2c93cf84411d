#!/bin/bash

# Deployment script for the unified Naxofy LMS platform
# This script deploys the application to the production server

# Set colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Function to print section headers
print_header() {
    echo -e "\n${YELLOW}=======================================${NC}"
    echo -e "${YELLOW}$1${NC}"
    echo -e "${YELLOW}=======================================${NC}\n"
}

# Function to print success messages
print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

# Function to print error messages
print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# Function to print info messages
print_info() {
    echo -e "$1"
}

# Function to run a command and check its exit status
run_command() {
    print_info "Running: $1"
    eval $1
    if [ $? -eq 0 ]; then
        print_success "$2"
        return 0
    else
        print_error "$3"
        return 1
    fi
}

# Check if the script is run from the project root
if [ ! -f "artisan" ]; then
    print_error "This script must be run from the project root directory"
    exit 1
fi

# Check if the environment is set up for production
if [ ! -f ".env.production" ]; then
    print_error ".env.production file not found"
    exit 1
fi

# Parse command line arguments
SKIP_TESTS=false
SKIP_BACKUP=false
SKIP_FRONTEND=false
SKIP_MOBILE=false
SKIP_BACKEND=false
DEPLOYMENT_TARGET=""

while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --skip-backup)
            SKIP_BACKUP=true
            shift
            ;;
        --skip-frontend)
            SKIP_FRONTEND=true
            shift
            ;;
        --skip-mobile)
            SKIP_MOBILE=true
            shift
            ;;
        --skip-backend)
            SKIP_BACKEND=true
            shift
            ;;
        --target)
            DEPLOYMENT_TARGET="$2"
            shift
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Check if deployment target is specified
if [ -z "$DEPLOYMENT_TARGET" ]; then
    print_error "Deployment target not specified. Use --target option."
    exit 1
fi

# Run tests if not skipped
if [ "$SKIP_TESTS" = false ]; then
    print_header "Running Tests"
    ./run-tests.sh
    if [ $? -ne 0 ]; then
        print_error "Tests failed. Aborting deployment."
        exit 1
    fi
fi

# Create backup if not skipped
if [ "$SKIP_BACKUP" = false ]; then
    print_header "Creating Backup"
    TIMESTAMP=$(date +"%Y%m%d%H%M%S")
    BACKUP_DIR="backups/$TIMESTAMP"
    
    # Create backup directory
    mkdir -p $BACKUP_DIR
    
    # Backup database
    print_info "Backing up database"
    DB_NAME=$(grep DB_DATABASE .env.production | cut -d '=' -f2)
    DB_USER=$(grep DB_USERNAME .env.production | cut -d '=' -f2)
    DB_PASS=$(grep DB_PASSWORD .env.production | cut -d '=' -f2)
    
    run_command "mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/database.sql" "Database backup created" "Failed to create database backup"
    
    # Backup files
    print_info "Backing up files"
    run_command "tar -czf $BACKUP_DIR/storage.tar.gz storage" "Storage backup created" "Failed to create storage backup"
    run_command "cp .env.production $BACKUP_DIR/.env.production" "Environment file backed up" "Failed to backup environment file"
    
    print_success "Backup created in $BACKUP_DIR"
fi

# Build and deploy frontend if not skipped
if [ "$SKIP_FRONTEND" = false ]; then
    print_header "Building Frontend"
    
    # Check if frontend directory exists
    if [ -d "frontend" ]; then
        cd frontend
        
        # Install dependencies
        run_command "npm install" "Frontend dependencies installed" "Failed to install frontend dependencies"
        
        # Build frontend
        run_command "npm run build" "Frontend built successfully" "Failed to build frontend"
        
        # Deploy frontend
        print_info "Deploying frontend to $DEPLOYMENT_TARGET"
        run_command "rsync -avz --delete dist/ $DEPLOYMENT_TARGET:/var/www/naxofy/public/frontend/" "Frontend deployed successfully" "Failed to deploy frontend"
        
        cd ..
    else
        print_info "Frontend directory not found, skipping frontend deployment"
    fi
fi

# Build and deploy mobile app if not skipped
if [ "$SKIP_MOBILE" = false ]; then
    print_header "Building Mobile App"
    
    # Check if mobile directory exists
    if [ -d "mobile" ]; then
        cd mobile
        
        # Install dependencies
        run_command "npm install" "Mobile app dependencies installed" "Failed to install mobile app dependencies"
        
        # Build mobile app
        run_command "npm run build" "Mobile app built successfully" "Failed to build mobile app"
        
        # Deploy mobile app
        print_info "Deploying mobile app to $DEPLOYMENT_TARGET"
        run_command "rsync -avz --delete dist/ $DEPLOYMENT_TARGET:/var/www/naxofy/public/mobile/" "Mobile app deployed successfully" "Failed to deploy mobile app"
        
        cd ..
    else
        print_info "Mobile directory not found, skipping mobile app deployment"
    fi
fi

# Deploy backend if not skipped
if [ "$SKIP_BACKEND" = false ]; then
    print_header "Deploying Backend"
    
    # Optimize composer autoloader
    run_command "composer install --no-dev --optimize-autoloader" "Composer dependencies installed" "Failed to install composer dependencies"
    
    # Deploy backend files
    print_info "Deploying backend to $DEPLOYMENT_TARGET"
    run_command "rsync -avz --exclude='.git' --exclude='node_modules' --exclude='storage/framework/cache' --exclude='storage/framework/sessions' --exclude='storage/framework/views' --exclude='storage/logs' --exclude='vendor' --exclude='.env' --exclude='.env.production' --exclude='backups' . $DEPLOYMENT_TARGET:/var/www/naxofy/" "Backend files deployed successfully" "Failed to deploy backend files"
    
    # Deploy vendor directory
    run_command "rsync -avz vendor/ $DEPLOYMENT_TARGET:/var/www/naxofy/vendor/" "Vendor directory deployed successfully" "Failed to deploy vendor directory"
    
    # Deploy environment file
    run_command "scp .env.production $DEPLOYMENT_TARGET:/var/www/naxofy/.env" "Environment file deployed successfully" "Failed to deploy environment file"
    
    # Run remote commands
    print_info "Running remote commands"
    run_command "ssh $DEPLOYMENT_TARGET 'cd /var/www/naxofy && php artisan migrate --force'" "Database migrations completed" "Failed to run database migrations"
    run_command "ssh $DEPLOYMENT_TARGET 'cd /var/www/naxofy && php artisan config:cache'" "Configuration cached" "Failed to cache configuration"
    run_command "ssh $DEPLOYMENT_TARGET 'cd /var/www/naxofy && php artisan route:cache'" "Routes cached" "Failed to cache routes"
    run_command "ssh $DEPLOYMENT_TARGET 'cd /var/www/naxofy && php artisan view:cache'" "Views cached" "Failed to cache views"
    run_command "ssh $DEPLOYMENT_TARGET 'cd /var/www/naxofy && php artisan storage:link'" "Storage link created" "Failed to create storage link"
    run_command "ssh $DEPLOYMENT_TARGET 'cd /var/www/naxofy && php artisan queue:restart'" "Queue workers restarted" "Failed to restart queue workers"
    run_command "ssh $DEPLOYMENT_TARGET 'sudo systemctl restart php8.1-fpm'" "PHP-FPM restarted" "Failed to restart PHP-FPM"
    run_command "ssh $DEPLOYMENT_TARGET 'sudo systemctl restart nginx'" "Nginx restarted" "Failed to restart Nginx"
fi

# Deployment summary
print_header "Deployment Summary"
print_success "Deployment completed successfully!"
print_info "Application deployed to $DEPLOYMENT_TARGET"
print_info "Timestamp: $(date)"

# Verify deployment
print_header "Verifying Deployment"
run_command "curl -s -o /dev/null -w '%{http_code}' http://$DEPLOYMENT_TARGET" "Application is accessible" "Application is not accessible"

print_info "\nDeployment completed at $(date)"
exit 0
