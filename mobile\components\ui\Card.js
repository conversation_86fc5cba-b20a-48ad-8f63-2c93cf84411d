import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '../ThemeProvider';

/**
 * Custom Card component with consistent styling
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.onPress - If provided, makes the card touchable
 * @param {Object} props.style - Additional style for the card
 */
const Card = ({ 
  children, 
  onPress, 
  style, 
  ...props 
}) => {
  const { theme } = useTheme();
  
  const cardStyle = [
    styles.card, 
    { 
      backgroundColor: theme.card,
      borderColor: theme.border,
      shadowColor: theme.text,
    },
    style
  ];
  
  // If onPress is provided, make the card touchable
  if (onPress) {
    return (
      <TouchableOpacity
        style={cardStyle}
        onPress={onPress}
        activeOpacity={0.7}
        {...props}
      >
        {children}
      </TouchableOpacity>
    );
  }
  
  // Otherwise, render as a regular View
  return (
    <View style={cardStyle} {...props}>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
});

export default Card;
