import React from 'react';
import { View, StyleSheet, Image, TouchableOpacity, ImageBackground } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from './ThemeProvider';
import Text from './ui/Text';
import Button from './ui/Button';

/**
 * Course Card component for displaying course information
 *
 * @param {Object} props - Component props
 * @param {Object} props.course - Course data
 * @param {boolean} props.compact - Whether to show a compact version of the card
 * @param {boolean} props.showProgress - Whether to show progress bar for enrolled courses
 * @param {Function} props.onPress - Custom press handler (optional)
 */
const CourseCard = ({ course, compact = false, showProgress = false, onPress }) => {
  const { theme } = useTheme();

  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      router.push(`/course/${course.id}`);
    }
  };

  if (compact) {
    return (
      <TouchableOpacity
        style={[styles.compactCard, { backgroundColor: theme.card, borderColor: theme.border }]}
        onPress={handlePress}
        activeOpacity={0.7}
      >
        <Image
          source={{ uri: course.thumbnail || 'https://source.unsplash.com/random?education' }}
          style={styles.compactImage}
        />
        <View style={styles.compactContent}>
          <Text variant="subtitle" numberOfLines={1} style={styles.compactTitle}>{course.title}</Text>
          <View style={styles.compactInstructorRow}>
            <Ionicons name="person" size={14} color={theme.textSecondary} />
            <Text variant="caption" color="muted" numberOfLines={1} style={styles.compactInstructor}>
              {course.instructor?.name || 'Instructor'}
            </Text>
          </View>
          <View style={styles.compactFooter}>
            <Text variant="subtitle" color="primary" style={styles.compactPrice}>
              ${course.price?.toFixed(2) || 'Free'}
            </Text>
            {course.progress !== undefined && (
              <View style={styles.progressBadge}>
                <Text variant="caption" style={styles.progressText}>{course.progress}% Complete</Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      style={[styles.card, { backgroundColor: theme.card, borderColor: theme.border }]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <ImageBackground
        source={{ uri: course.thumbnail || 'https://source.unsplash.com/random?education' }}
        style={styles.image}
        imageStyle={styles.imageBackground}
      >
        <View style={styles.imageOverlay}>
          <View style={styles.badgeContainer}>
            {course.lesson_count && (
              <View style={[styles.badge, { backgroundColor: theme.primary }]}>
                <Ionicons name="book-outline" size={12} color="white" />
                <Text variant="caption" style={styles.badgeText}>{course.lesson_count} Lessons</Text>
              </View>
            )}
            {course.rating && (
              <View style={[styles.badge, { backgroundColor: theme.accent }]}>
                <Ionicons name="star" size={12} color="white" />
                <Text variant="caption" style={styles.badgeText}>{course.rating}</Text>
              </View>
            )}
          </View>
        </View>
      </ImageBackground>
      <View style={styles.content}>
        <Text variant="subtitle" numberOfLines={2} style={styles.title}>{course.title}</Text>

        <View style={styles.instructorRow}>
          <Ionicons name="person" size={16} color={theme.textSecondary} />
          <Text variant="caption" color="muted" style={styles.instructorName}>
            {course.instructor?.name || 'Instructor'}
          </Text>
        </View>

        <Text variant="body" numberOfLines={2} style={styles.description}>
          {course.description}
        </Text>

        <View style={styles.statsRow}>
          {course.enrollment_count && (
            <View style={styles.statItem}>
              <Ionicons name="people-outline" size={16} color={theme.textSecondary} />
              <Text variant="caption" color="muted">{course.enrollment_count} Students</Text>
            </View>
          )}
          {course.review_count && (
            <View style={styles.statItem}>
              <Ionicons name="chatbubble-outline" size={16} color={theme.textSecondary} />
              <Text variant="caption" color="muted">{course.review_count} Reviews</Text>
            </View>
          )}
        </View>

        {showProgress && course.progress !== undefined ? (
          <View style={styles.progressContainer}>
            <View style={styles.progressRow}>
              <Text variant="caption" color="muted">Progress: {course.progress}%</Text>
              <Text variant="caption" color="muted">{course.progress}% Complete</Text>
            </View>
            <View style={styles.progressBarContainer}>
              <View
                style={[
                  styles.progressBar,
                  {
                    width: `${course.progress}%`,
                    backgroundColor: theme.primary
                  }
                ]}
              />
            </View>
          </View>
        ) : (
          <View style={styles.footer}>
            <Text variant="subtitle" color="primary" style={styles.price}>
              ${course.price?.toFixed(2) || 'Free'}
            </Text>
            <Button
              variant="primary"
              size="small"
              onPress={handlePress}
              style={styles.viewButton}
            >
              {course.is_enrolled ? 'Continue' : 'View Course'}
            </Button>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  // Full card styles
  card: {
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 20,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  image: {
    width: '100%',
    height: 180,
  },
  imageBackground: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  imageOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.2)',
    justifyContent: 'space-between',
    padding: 12,
  },
  badgeContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 8,
  },
  badgeText: {
    color: 'white',
    marginLeft: 4,
    fontSize: 12,
  },
  content: {
    padding: 16,
  },
  title: {
    fontWeight: '700',
    marginBottom: 8,
  },
  instructorRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  instructorName: {
    marginLeft: 6,
  },
  description: {
    marginBottom: 16,
    lineHeight: 20,
  },
  statsRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  price: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  viewButton: {
    minWidth: 100,
  },

  // Compact styles
  compactCard: {
    flexDirection: 'row',
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
    borderWidth: 1,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  compactImage: {
    width: 100,
    height: 100,
  },
  compactContent: {
    flex: 1,
    padding: 12,
    justifyContent: 'space-between',
  },
  compactTitle: {
    fontWeight: '600',
  },
  compactInstructorRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  compactInstructor: {
    marginLeft: 4,
  },
  compactFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  compactPrice: {
    fontWeight: 'bold',
  },
  progressBadge: {
    backgroundColor: '#10b981',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  progressText: {
    color: 'white',
    fontSize: 10,
    fontWeight: '500',
  },
  progressContainer: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  progressRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: '#e5e7eb',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 4,
  },
});

export default CourseCard;
