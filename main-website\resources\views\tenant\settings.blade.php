@extends('tenant.layouts.app')

@section('title', 'Settings')

@section('content')
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 bg-white border-b border-gray-200">
                <h1 class="text-2xl font-semibold text-gray-900 mb-6">Settings</h1>
                
                <!-- Tabs -->
                <div class="border-b border-gray-200 mb-6">
                    <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                        <a href="#" class="border-primary-500 text-primary-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" aria-current="page">
                            General
                        </a>
                        <a href="#" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            Appearance
                        </a>
                        <a href="#" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            Email
                        </a>
                        <a href="#" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            Payments
                        </a>
                        <a href="#" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            Integrations
                        </a>
                    </nav>
                </div>
                
                <!-- General Settings Form -->
                <form action="#" method="POST">
                    @csrf
                    
                    <div class="space-y-8 divide-y divide-gray-200">
                        <!-- Academy Information -->
                        <div class="space-y-6">
                            <div>
                                <h3 class="text-lg leading-6 font-medium text-gray-900">Academy Information</h3>
                                <p class="mt-1 text-sm text-gray-500">Update your academy's basic information.</p>
                            </div>
                            
                            <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                                <div class="sm:col-span-4">
                                    <label for="academy_name" class="block text-sm font-medium text-gray-700">Academy Name</label>
                                    <div class="mt-1">
                                        <input type="text" name="academy_name" id="academy_name" value="{{ $tenant->name }}" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                    </div>
                                </div>
                                
                                <div class="sm:col-span-4">
                                    <label for="domain" class="block text-sm font-medium text-gray-700">Domain</label>
                                    <div class="mt-1 flex rounded-md shadow-sm">
                                        <input type="text" name="domain" id="domain" value="{{ $tenant->domain }}" class="focus:ring-primary-500 focus:border-primary-500 flex-1 block w-full rounded-none rounded-l-md sm:text-sm border-gray-300">
                                        <span class="inline-flex items-center px-3 rounded-r-md border border-l-0 border-gray-300 bg-gray-50 text-gray-500 sm:text-sm">
                                            .lmsplatform.com
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="sm:col-span-6">
                                    <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                                    <div class="mt-1">
                                        <textarea id="description" name="description" rows="3" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md">{{ $tenant->description ?? '' }}</textarea>
                                    </div>
                                    <p class="mt-2 text-sm text-gray-500">Brief description of your academy.</p>
                                </div>
                                
                                <div class="sm:col-span-6">
                                    <label for="logo" class="block text-sm font-medium text-gray-700">Logo</label>
                                    <div class="mt-1 flex items-center">
                                        <span class="h-12 w-12 rounded-md overflow-hidden bg-gray-100">
                                            @if($tenant->logo)
                                                <img src="{{ asset('storage/' . $tenant->logo) }}" alt="{{ $tenant->name }} Logo" class="h-12 w-12 object-cover">
                                            @else
                                                <svg class="h-12 w-12 text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                                                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                                                </svg>
                                            @endif
                                        </span>
                                        <button type="button" class="ml-5 bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                            Change
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Contact Information -->
                        <div class="pt-8 space-y-6">
                            <div>
                                <h3 class="text-lg leading-6 font-medium text-gray-900">Contact Information</h3>
                                <p class="mt-1 text-sm text-gray-500">This information will be displayed on your academy's website.</p>
                            </div>
                            
                            <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                                <div class="sm:col-span-3">
                                    <label for="contact_email" class="block text-sm font-medium text-gray-700">Contact Email</label>
                                    <div class="mt-1">
                                        <input type="email" name="contact_email" id="contact_email" value="contact@{{ $tenant->domain }}.lmsplatform.com" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                    </div>
                                </div>
                                
                                <div class="sm:col-span-3">
                                    <label for="contact_phone" class="block text-sm font-medium text-gray-700">Contact Phone</label>
                                    <div class="mt-1">
                                        <input type="text" name="contact_phone" id="contact_phone" value="+****************" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                    </div>
                                </div>
                                
                                <div class="sm:col-span-6">
                                    <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
                                    <div class="mt-1">
                                        <textarea id="address" name="address" rows="3" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md">123 Main St, Suite 100
New York, NY 10001
United States</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Social Media -->
                        <div class="pt-8 space-y-6">
                            <div>
                                <h3 class="text-lg leading-6 font-medium text-gray-900">Social Media</h3>
                                <p class="mt-1 text-sm text-gray-500">Connect your social media accounts.</p>
                            </div>
                            
                            <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                                <div class="sm:col-span-3">
                                    <label for="facebook" class="block text-sm font-medium text-gray-700">Facebook</label>
                                    <div class="mt-1 flex rounded-md shadow-sm">
                                        <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 sm:text-sm">
                                            facebook.com/
                                        </span>
                                        <input type="text" name="facebook" id="facebook" value="{{ $tenant->domain }}" class="focus:ring-primary-500 focus:border-primary-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300">
                                    </div>
                                </div>
                                
                                <div class="sm:col-span-3">
                                    <label for="twitter" class="block text-sm font-medium text-gray-700">Twitter</label>
                                    <div class="mt-1 flex rounded-md shadow-sm">
                                        <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 sm:text-sm">
                                            twitter.com/
                                        </span>
                                        <input type="text" name="twitter" id="twitter" value="{{ $tenant->domain }}" class="focus:ring-primary-500 focus:border-primary-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300">
                                    </div>
                                </div>
                                
                                <div class="sm:col-span-3">
                                    <label for="instagram" class="block text-sm font-medium text-gray-700">Instagram</label>
                                    <div class="mt-1 flex rounded-md shadow-sm">
                                        <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 sm:text-sm">
                                            instagram.com/
                                        </span>
                                        <input type="text" name="instagram" id="instagram" value="{{ $tenant->domain }}" class="focus:ring-primary-500 focus:border-primary-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300">
                                    </div>
                                </div>
                                
                                <div class="sm:col-span-3">
                                    <label for="youtube" class="block text-sm font-medium text-gray-700">YouTube</label>
                                    <div class="mt-1 flex rounded-md shadow-sm">
                                        <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 sm:text-sm">
                                            youtube.com/
                                        </span>
                                        <input type="text" name="youtube" id="youtube" value="{{ $tenant->domain }}" class="focus:ring-primary-500 focus:border-primary-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="pt-8 flex justify-end">
                        <button type="button" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            Cancel
                        </button>
                        <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            Save
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
