import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  Button,
  TextField,
  InputAdornment,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Chip,
  CircularProgress,
  Tabs,
  Tab,
  Divider,
  Alert
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  ContentCopy as ContentCopyIcon,
  Public as PublicIcon,
  PublicOff as PublicOffIcon,
  School as SchoolIcon,
  People as PeopleIcon,
  Star as StarIcon
} from '@mui/icons-material';
import { Link as RouterLink } from 'react-router-dom';
import { courseService } from '../../services/api';

const CourseManagement = () => {
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [tabValue, setTabValue] = useState(0);
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [duplicateDialogOpen, setDuplicateDialogOpen] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    fetchCourses();
  }, []);

  const fetchCourses = async () => {
    try {
      setLoading(true);
      const response = await courseService.getAllCourses();
      setCourses(response.data);
      setError(null);
    } catch (error) {
      console.error('Error fetching courses:', error);
      setError('Failed to load courses. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value);
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleMenuOpen = (event, course) => {
    setAnchorEl(event.currentTarget);
    setSelectedCourse(course);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleDeleteDialogOpen = () => {
    setDeleteDialogOpen(true);
    handleMenuClose();
  };

  const handleDeleteDialogClose = () => {
    setDeleteDialogOpen(false);
    setSelectedCourse(null);
  };

  const handleDuplicateDialogOpen = () => {
    setDuplicateDialogOpen(true);
    handleMenuClose();
  };

  const handleDuplicateDialogClose = () => {
    setDuplicateDialogOpen(false);
    setSelectedCourse(null);
  };

  const handleDeleteCourse = async () => {
    if (!selectedCourse) return;
    
    try {
      setActionLoading(true);
      await courseService.deleteCourse(selectedCourse.id);
      
      // Remove the course from the local state
      setCourses(courses.filter(course => course.id !== selectedCourse.id));
      
      handleDeleteDialogClose();
    } catch (error) {
      console.error('Error deleting course:', error);
      setError('Failed to delete course. Please try again later.');
    } finally {
      setActionLoading(false);
    }
  };

  const handleDuplicateCourse = async () => {
    if (!selectedCourse) return;
    
    try {
      setActionLoading(true);
      const response = await courseService.duplicateCourse(selectedCourse.id);
      
      // Add the new course to the local state
      setCourses([...courses, response.data]);
      
      handleDuplicateDialogClose();
    } catch (error) {
      console.error('Error duplicating course:', error);
      setError('Failed to duplicate course. Please try again later.');
    } finally {
      setActionLoading(false);
    }
  };

  const handleTogglePublish = async (course) => {
    try {
      const updatedCourse = { ...course, is_published: !course.is_published };
      await courseService.updateCourse(course.id, updatedCourse);
      
      // Update the course in the local state
      setCourses(courses.map(c => 
        c.id === course.id ? updatedCourse : c
      ));
    } catch (error) {
      console.error('Error updating course:', error);
      setError('Failed to update course. Please try again later.');
    }
  };

  const filteredCourses = courses.filter(course => {
    const matchesSearch = course.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
                          course.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    if (tabValue === 0) return matchesSearch; // All courses
    if (tabValue === 1) return matchesSearch && course.is_published; // Published courses
    if (tabValue === 2) return matchesSearch && !course.is_published; // Draft courses
    
    return matchesSearch;
  });

  const getStatusChip = (isPublished) => {
    return isPublished ? 
      <Chip label="Published" color="success" size="small" /> : 
      <Chip label="Draft" color="default" size="small" />;
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Course Management
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          component={RouterLink}
          to="/tenant/courses/create"
        >
          Create Course
        </Button>
      </Box>
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      <Paper sx={{ p: 3, mb: 4 }}>
        <Grid container spacing={2} alignItems="center" sx={{ mb: 3 }}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search courses..."
              variant="outlined"
              value={searchQuery}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              variant="fullWidth"
            >
              <Tab label="All Courses" />
              <Tab label="Published" />
              <Tab label="Drafts" />
            </Tabs>
          </Grid>
        </Grid>
        
        {filteredCourses.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="h6" gutterBottom>
              No courses found
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {searchQuery ? 'Try a different search term' : 'Create your first course to get started'}
            </Typography>
            {!searchQuery && (
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                component={RouterLink}
                to="/tenant/courses/create"
                sx={{ mt: 2 }}
              >
                Create Course
              </Button>
            )}
          </Box>
        ) : (
          <Grid container spacing={3}>
            {filteredCourses.map((course) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={course.id}>
                <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                  <CardMedia
                    component="img"
                    height="140"
                    image={course.thumbnail || `https://source.unsplash.com/random?education&sig=${course.id}`}
                    alt={course.title}
                  />
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <Typography variant="h6" component="div" gutterBottom noWrap sx={{ maxWidth: '80%' }}>
                        {course.title}
                      </Typography>
                      <IconButton
                        size="small"
                        onClick={(e) => handleMenuOpen(e, course)}
                      >
                        <MoreVertIcon />
                      </IconButton>
                    </Box>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2, height: 40, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                      {course.description}
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <PeopleIcon fontSize="small" color="action" sx={{ mr: 0.5 }} />
                        <Typography variant="body2" color="text.secondary">
                          {course.enrollment_count || 0}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <SchoolIcon fontSize="small" color="action" sx={{ mr: 0.5 }} />
                        <Typography variant="body2" color="text.secondary">
                          {course.lesson_count || 0} lessons
                        </Typography>
                      </Box>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <StarIcon fontSize="small" color="warning" sx={{ mr: 0.5 }} />
                        <Typography variant="body2">
                          {course.rating || '0.0'} ({course.review_count || 0})
                        </Typography>
                      </Box>
                      {getStatusChip(course.is_published)}
                    </Box>
                  </CardContent>
                  <Divider />
                  <CardActions>
                    <Button
                      size="small"
                      startIcon={<VisibilityIcon />}
                      component={RouterLink}
                      to={`/tenant/courses/${course.id}`}
                    >
                      View
                    </Button>
                    <Button
                      size="small"
                      startIcon={<EditIcon />}
                      component={RouterLink}
                      to={`/tenant/courses/${course.id}/edit`}
                    >
                      Edit
                    </Button>
                    <Button
                      size="small"
                      startIcon={course.is_published ? <PublicOffIcon /> : <PublicIcon />}
                      onClick={() => handleTogglePublish(course)}
                    >
                      {course.is_published ? 'Unpublish' : 'Publish'}
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </Paper>
      
      {/* Course Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem
          component={RouterLink}
          to={selectedCourse ? `/tenant/courses/${selectedCourse.id}` : '#'}
          onClick={handleMenuClose}
        >
          <ListItemIcon>
            <VisibilityIcon fontSize="small" />
          </ListItemIcon>
          View Details
        </MenuItem>
        <MenuItem
          component={RouterLink}
          to={selectedCourse ? `/tenant/courses/${selectedCourse.id}/edit` : '#'}
          onClick={handleMenuClose}
        >
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          Edit Course
        </MenuItem>
        <MenuItem onClick={handleDuplicateDialogOpen}>
          <ListItemIcon>
            <ContentCopyIcon fontSize="small" />
          </ListItemIcon>
          Duplicate
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleDeleteDialogOpen} sx={{ color: 'error.main' }}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          Delete
        </MenuItem>
      </Menu>
      
      {/* Delete Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteDialogClose}
      >
        <DialogTitle>Delete Course</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete "{selectedCourse?.title}"? This action cannot be undone and will remove all associated lessons, quizzes, and student progress.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteDialogClose} disabled={actionLoading}>
            Cancel
          </Button>
          <Button 
            onClick={handleDeleteCourse} 
            color="error" 
            variant="contained"
            disabled={actionLoading}
          >
            {actionLoading ? <CircularProgress size={24} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Duplicate Dialog */}
      <Dialog
        open={duplicateDialogOpen}
        onClose={handleDuplicateDialogClose}
      >
        <DialogTitle>Duplicate Course</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to duplicate "{selectedCourse?.title}"? This will create a copy of the course with all its lessons and quizzes, but without student data.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDuplicateDialogClose} disabled={actionLoading}>
            Cancel
          </Button>
          <Button 
            onClick={handleDuplicateCourse} 
            color="primary" 
            variant="contained"
            disabled={actionLoading}
          >
            {actionLoading ? <CircularProgress size={24} /> : 'Duplicate'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

// Add missing ListItemIcon component
const ListItemIcon = ({ children }) => (
  <Box sx={{ mr: 2, display: 'inline-flex', alignItems: 'center' }}>
    {children}
  </Box>
);

export default CourseManagement;
