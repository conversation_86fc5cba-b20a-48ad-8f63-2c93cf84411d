<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Theme;
use App\Models\ThemeTemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ThemeTemplateController extends Controller
{
    /**
     * Display a listing of the templates for a theme.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $themeId
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request, $themeId)
    {
        $theme = Theme::find($themeId);
        
        if (!$theme) {
            return response()->json([
                'success' => false,
                'message' => 'Theme not found'
            ], 404);
        }
        
        $user = Auth::user();
        
        // Check if user has access to this theme
        if ($user->role === 'tenant' && $theme->tenant_id !== null && $theme->tenant_id !== $user->tenant_id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }
        
        $type = $request->input('type');
        $query = ThemeTemplate::where('theme_id', $themeId);
        
        if ($type) {
            $query->ofType($type);
        }
        
        $templates = $query->get();
        
        return response()->json([
            'success' => true,
            'data' => $templates
        ]);
    }
    
    /**
     * Store a newly created template in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $themeId
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request, $themeId)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'type' => 'required|string|max:50',
            'description' => 'nullable|string',
            'content' => 'required|string',
            'variables' => 'nullable|array',
            'settings' => 'nullable|array',
            'is_default' => 'nullable|boolean',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
        
        $theme = Theme::find($themeId);
        
        if (!$theme) {
            return response()->json([
                'success' => false,
                'message' => 'Theme not found'
            ], 404);
        }
        
        $user = Auth::user();
        
        // Check if user has permission to create templates for this theme
        if ($user->role !== 'admin' && ($user->role !== 'tenant' || $theme->tenant_id !== $user->tenant_id)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }
        
        // Create the template
        $template = new ThemeTemplate();
        $template->theme_id = $themeId;
        $template->name = $request->input('name');
        $template->slug = Str::slug($request->input('name'));
        $template->type = $request->input('type');
        $template->description = $request->input('description');
        $template->content = $request->input('content');
        $template->variables = $request->input('variables');
        $template->settings = $request->input('settings');
        $template->is_default = $request->input('is_default', false);
        
        // If this is set as default, unset any other default template of the same type
        if ($template->is_default) {
            ThemeTemplate::where('theme_id', $themeId)
                ->where('type', $template->type)
                ->where('is_default', true)
                ->update(['is_default' => false]);
        }
        
        $template->save();
        
        return response()->json([
            'success' => true,
            'message' => 'Template created successfully',
            'data' => $template
        ], 201);
    }
    
    /**
     * Display the specified template.
     *
     * @param  int  $themeId
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($themeId, $id)
    {
        $theme = Theme::find($themeId);
        
        if (!$theme) {
            return response()->json([
                'success' => false,
                'message' => 'Theme not found'
            ], 404);
        }
        
        $user = Auth::user();
        
        // Check if user has access to this theme
        if ($user->role === 'tenant' && $theme->tenant_id !== null && $theme->tenant_id !== $user->tenant_id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }
        
        $template = ThemeTemplate::where('theme_id', $themeId)
            ->where('id', $id)
            ->first();
            
        if (!$template) {
            return response()->json([
                'success' => false,
                'message' => 'Template not found'
            ], 404);
        }
        
        return response()->json([
            'success' => true,
            'data' => $template
        ]);
    }
    
    /**
     * Update the specified template in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $themeId
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $themeId, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'content' => 'sometimes|required|string',
            'variables' => 'nullable|array',
            'settings' => 'nullable|array',
            'is_default' => 'nullable|boolean',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
        
        $theme = Theme::find($themeId);
        
        if (!$theme) {
            return response()->json([
                'success' => false,
                'message' => 'Theme not found'
            ], 404);
        }
        
        $user = Auth::user();
        
        // Check if user has permission to update templates for this theme
        if ($user->role !== 'admin' && ($user->role !== 'tenant' || $theme->tenant_id !== $user->tenant_id)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }
        
        $template = ThemeTemplate::where('theme_id', $themeId)
            ->where('id', $id)
            ->first();
            
        if (!$template) {
            return response()->json([
                'success' => false,
                'message' => 'Template not found'
            ], 404);
        }
        
        // Update the template
        if ($request->has('name')) {
            $template->name = $request->input('name');
            $template->slug = Str::slug($request->input('name'));
        }
        
        if ($request->has('description')) {
            $template->description = $request->input('description');
        }
        
        if ($request->has('content')) {
            $template->content = $request->input('content');
        }
        
        if ($request->has('variables')) {
            $template->variables = $request->input('variables');
        }
        
        if ($request->has('settings')) {
            $template->settings = $request->input('settings');
        }
        
        if ($request->has('is_default')) {
            $isDefault = $request->input('is_default');
            
            // If this is set as default, unset any other default template of the same type
            if ($isDefault && !$template->is_default) {
                ThemeTemplate::where('theme_id', $themeId)
                    ->where('type', $template->type)
                    ->where('is_default', true)
                    ->update(['is_default' => false]);
            }
            
            $template->is_default = $isDefault;
        }
        
        $template->save();
        
        return response()->json([
            'success' => true,
            'message' => 'Template updated successfully',
            'data' => $template
        ]);
    }
    
    /**
     * Remove the specified template from storage.
     *
     * @param  int  $themeId
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($themeId, $id)
    {
        $theme = Theme::find($themeId);
        
        if (!$theme) {
            return response()->json([
                'success' => false,
                'message' => 'Theme not found'
            ], 404);
        }
        
        $user = Auth::user();
        
        // Check if user has permission to delete templates for this theme
        if ($user->role !== 'admin' && ($user->role !== 'tenant' || $theme->tenant_id !== $user->tenant_id)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }
        
        $template = ThemeTemplate::where('theme_id', $themeId)
            ->where('id', $id)
            ->first();
            
        if (!$template) {
            return response()->json([
                'success' => false,
                'message' => 'Template not found'
            ], 404);
        }
        
        // Check if template has customizations
        if ($template->customizations()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete template with existing customizations'
            ], 400);
        }
        
        $template->delete();
        
        return response()->json([
            'success' => true,
            'message' => 'Template deleted successfully'
        ]);
    }
}
