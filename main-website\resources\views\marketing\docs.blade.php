@extends('marketing.layouts.app')

@section('title', 'Documentation')

@section('content')
    <!-- Documentation Header -->
    <div class="bg-gray-50 py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl font-extrabold text-gray-900 sm:text-5xl sm:tracking-tight lg:text-6xl">Documentation</h1>
                <p class="mt-5 max-w-xl mx-auto text-xl text-gray-500">Everything you need to know about the LMS platform.</p>
            </div>
        </div>
    </div>

    <!-- Documentation Content -->
    <div class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Sidebar -->
                <div class="md:col-span-1">
                    <nav class="space-y-1" aria-label="Sidebar">
                        @foreach($categories as $index => $category)
                            <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider mt-4 first:mt-0">
                                {{ $category['name'] }}
                            </h3>
                            <div class="space-y-1">
                                @foreach($category['docs'] as $docIndex => $doc)
                                    <a href="#{{ Str::slug($category['name']) }}-{{ Str::slug($doc['title']) }}" 
                                       class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50">
                                        {{ $doc['title'] }}
                                    </a>
                                @endforeach
                            </div>
                        @endforeach
                    </nav>
                </div>

                <!-- Main Content -->
                <div class="md:col-span-3">
                    @foreach($categories as $index => $category)
                        <div class="mb-12">
                            <h2 class="text-2xl font-bold text-gray-900 mb-6">{{ $category['name'] }}</h2>
                            
                            @foreach($category['docs'] as $docIndex => $doc)
                                <div id="{{ Str::slug($category['name']) }}-{{ Str::slug($doc['title']) }}" class="mb-8 pb-8 border-b border-gray-200 last:border-b-0">
                                    <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ $doc['title'] }}</h3>
                                    <p class="text-gray-600 mb-4">{{ $doc['description'] }}</p>
                                    
                                    <!-- Placeholder content - would be replaced with actual documentation -->
                                    <div class="prose max-w-none">
                                        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl. Sed euismod, nisl vel ultricies lacinia, nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.</p>
                                        
                                        <h4>Getting Started</h4>
                                        <p>Phasellus vestibulum lorem sed risus ultricies tristique nulla. Quis blandit turpis cursus in hac habitasse platea dictumst. Quis blandit turpis cursus in hac habitasse platea dictumst.</p>
                                        
                                        <pre><code>// Example code
function example() {
  console.log('Hello world!');
}</code></pre>
                                        
                                        <h4>Configuration</h4>
                                        <p>Phasellus vestibulum lorem sed risus ultricies tristique nulla. Quis blandit turpis cursus in hac habitasse platea dictumst.</p>
                                        
                                        <ul>
                                            <li>First item</li>
                                            <li>Second item</li>
                                            <li>Third item</li>
                                        </ul>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- CTA -->
    <div class="bg-indigo-700">
        <div class="max-w-2xl mx-auto text-center py-16 px-4 sm:py-20 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-extrabold text-white sm:text-4xl">
                <span class="block">Ready to get started?</span>
                <span class="block">Start your free trial today.</span>
            </h2>
            <p class="mt-4 text-lg leading-6 text-indigo-200">
                No credit card required. Try all features free for 14 days.
            </p>
            <a href="{{ route('signup') }}" class="mt-8 w-full inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-indigo-50 sm:w-auto">
                Sign up for free
            </a>
        </div>
    </div>
@endsection
