<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Theme;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ThemeController extends Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        // Check if user is admin for all methods
        $this->middleware(function ($request, $next) {
            if (!Auth::check() || !Auth::user()->isAdmin()) {
                return redirect()->route('login')->with('error', 'You do not have permission to access this area.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of the themes.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get themes from database or use mock data if table doesn't exist yet
        try {
            $themes = Theme::withCount('tenants')->get();
            
            if ($themes->isEmpty()) {
                $themes = $this->getMockThemes();
            }
        } catch (\Exception $e) {
            $themes = $this->getMockThemes();
        }

        return view('admin.themes.index', compact('themes'));
    }

    /**
     * Show the form for creating a new theme.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('admin.themes.create');
    }

    /**
     * Store a newly created theme in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:themes,name',
            'description' => 'required|string',
            'thumbnail' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'is_premium' => 'boolean',
            'price' => 'required_if:is_premium,1|nullable|numeric|min:0',
            'version' => 'required|string|max:20',
            'author' => 'required|string|max:255',
            'author_website' => 'nullable|url',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Handle thumbnail upload
            $thumbnailPath = null;
            if ($request->hasFile('thumbnail')) {
                $thumbnailPath = $request->file('thumbnail')->store('themes/thumbnails', 'public');
            }

            $theme = Theme::create([
                'name' => $request->name,
                'slug' => Str::slug($request->name),
                'description' => $request->description,
                'thumbnail' => $thumbnailPath,
                'is_active' => $request->has('is_active'),
                'is_premium' => $request->has('is_premium'),
                'price' => $request->price ?? 0,
                'version' => $request->version,
                'author' => $request->author,
                'author_website' => $request->author_website,
                'settings' => [
                    'colors' => [
                        'primary' => '#ff7700',
                        'secondary' => '#0369a1',
                        'accent' => '#f59e0b',
                        'background' => '#ffffff',
                        'text' => '#111827',
                    ],
                    'fonts' => [
                        'heading' => 'Inter',
                        'body' => 'Inter',
                    ],
                    'layout' => 'default',
                ],
            ]);

            return redirect()->route('admin.themes.index')
                ->with('success', 'Theme created successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to create theme: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified theme.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        try {
            $theme = Theme::with('tenants')->findOrFail($id);
        } catch (\Exception $e) {
            // Use mock data if theme not found
            $theme = collect($this->getMockThemes())->firstWhere('id', $id);
            
            if (!$theme) {
                return redirect()->route('admin.themes.index')
                    ->with('error', 'Theme not found.');
            }
        }

        return view('admin.themes.show', compact('theme'));
    }

    /**
     * Show the form for editing the specified theme.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        try {
            $theme = Theme::findOrFail($id);
        } catch (\Exception $e) {
            // Use mock data if theme not found
            $theme = collect($this->getMockThemes())->firstWhere('id', $id);
            
            if (!$theme) {
                return redirect()->route('admin.themes.index')
                    ->with('error', 'Theme not found.');
            }
        }

        return view('admin.themes.edit', compact('theme'));
    }

    /**
     * Update the specified theme in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:themes,name,' . $id,
            'description' => 'required|string',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
            'is_premium' => 'boolean',
            'price' => 'required_if:is_premium,1|nullable|numeric|min:0',
            'version' => 'required|string|max:20',
            'author' => 'required|string|max:255',
            'author_website' => 'nullable|url',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $theme = Theme::findOrFail($id);
            
            // Handle thumbnail upload
            $thumbnailPath = $theme->thumbnail;
            if ($request->hasFile('thumbnail')) {
                // Delete old thumbnail
                if ($thumbnailPath && Storage::disk('public')->exists($thumbnailPath)) {
                    Storage::disk('public')->delete($thumbnailPath);
                }
                
                $thumbnailPath = $request->file('thumbnail')->store('themes/thumbnails', 'public');
            }

            $theme->update([
                'name' => $request->name,
                'slug' => Str::slug($request->name),
                'description' => $request->description,
                'thumbnail' => $thumbnailPath,
                'is_active' => $request->has('is_active'),
                'is_premium' => $request->has('is_premium'),
                'price' => $request->price ?? 0,
                'version' => $request->version,
                'author' => $request->author,
                'author_website' => $request->author_website,
            ]);

            return redirect()->route('admin.themes.show', $theme->id)
                ->with('success', 'Theme updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update theme: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified theme from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        try {
            $theme = Theme::findOrFail($id);
            
            // Check if theme is being used by tenants
            if ($theme->tenants()->count() > 0) {
                return redirect()->back()
                    ->with('error', 'Cannot delete theme that is being used by tenants.');
            }
            
            // Delete thumbnail
            if ($theme->thumbnail && Storage::disk('public')->exists($theme->thumbnail)) {
                Storage::disk('public')->delete($theme->thumbnail);
            }
            
            $theme->delete();
            
            return redirect()->route('admin.themes.index')
                ->with('success', 'Theme deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to delete theme: ' . $e->getMessage());
        }
    }

    /**
     * Get mock themes data.
     *
     * @return \Illuminate\Support\Collection
     */
    private function getMockThemes()
    {
        return collect([
            (object) [
                'id' => 1,
                'name' => 'Modern',
                'slug' => 'modern',
                'description' => 'A clean, modern theme with a focus on readability and user experience.',
                'thumbnail' => 'themes/thumbnails/modern.jpg',
                'is_active' => true,
                'is_premium' => false,
                'price' => 0,
                'version' => '1.0.0',
                'author' => 'Naxofy',
                'author_website' => 'https://naxofy.com',
                'tenants_count' => 45,
                'created_at' => now()->subMonths(3),
                'updated_at' => now()->subWeeks(2),
                'settings' => [
                    'colors' => [
                        'primary' => '#ff7700',
                        'secondary' => '#0369a1',
                        'accent' => '#f59e0b',
                        'background' => '#ffffff',
                        'text' => '#111827',
                    ],
                    'fonts' => [
                        'heading' => 'Inter',
                        'body' => 'Inter',
                    ],
                    'layout' => 'default',
                ],
            ],
            (object) [
                'id' => 2,
                'name' => 'Classic',
                'slug' => 'classic',
                'description' => 'A traditional academic theme with a professional look and feel.',
                'thumbnail' => 'themes/thumbnails/classic.jpg',
                'is_active' => true,
                'is_premium' => false,
                'price' => 0,
                'version' => '1.0.0',
                'author' => 'Naxofy',
                'author_website' => 'https://naxofy.com',
                'tenants_count' => 32,
                'created_at' => now()->subMonths(3),
                'updated_at' => now()->subWeeks(2),
                'settings' => [
                    'colors' => [
                        'primary' => '#1e40af',
                        'secondary' => '#6b7280',
                        'accent' => '#d97706',
                        'background' => '#f9fafb',
                        'text' => '#111827',
                    ],
                    'fonts' => [
                        'heading' => 'Merriweather',
                        'body' => 'Source Sans Pro',
                    ],
                    'layout' => 'default',
                ],
            ],
            (object) [
                'id' => 3,
                'name' => 'Minimalist',
                'slug' => 'minimalist',
                'description' => 'A minimalist theme with a focus on content and distraction-free learning.',
                'thumbnail' => 'themes/thumbnails/minimalist.jpg',
                'is_active' => true,
                'is_premium' => true,
                'price' => 29.99,
                'version' => '1.0.0',
                'author' => 'Design Studio',
                'author_website' => 'https://designstudio.com',
                'tenants_count' => 18,
                'created_at' => now()->subMonths(2),
                'updated_at' => now()->subWeeks(1),
                'settings' => [
                    'colors' => [
                        'primary' => '#000000',
                        'secondary' => '#6b7280',
                        'accent' => '#f59e0b',
                        'background' => '#ffffff',
                        'text' => '#111827',
                    ],
                    'fonts' => [
                        'heading' => 'Montserrat',
                        'body' => 'Open Sans',
                    ],
                    'layout' => 'minimal',
                ],
            ],
        ]);
    }
}
