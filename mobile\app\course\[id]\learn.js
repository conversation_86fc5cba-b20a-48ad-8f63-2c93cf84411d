import React, { useState, useEffect } from 'react';
import { StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Alert } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { courseService, lessonService } from '@/services/api';

export default function CourseLearningScreen() {
  const { id } = useLocalSearchParams();
  const [course, setCourse] = useState(null);
  const [lessons, setLessons] = useState([]);
  const [currentLesson, setCurrentLesson] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchCourseAndLessons();
  }, [id]);

  const fetchCourseAndLessons = async () => {
    try {
      setLoading(true);
      console.log('Fetching course and lessons for ID:', id);
      
      // Fetch course details
      const courseResponse = await courseService.getCourseById(id);
      const courseData = courseResponse.data.course;
      setCourse(courseData);
      
      // Fetch or use lessons from course data
      let lessonData = [];
      if (courseData.lessons && courseData.lessons.length > 0) {
        console.log('Using lessons from course data');
        lessonData = courseData.lessons;
      } else {
        console.log('Fetching lessons separately');
        const lessonsResponse = await lessonService.getLessonsByCourse(id);
        lessonData = lessonsResponse.data;
      }
      
      setLessons(lessonData);
      
      // Set the first lesson as current if available
      if (lessonData.length > 0) {
        setCurrentLesson(lessonData[0]);
      }
      
      setError(null);
    } catch (error) {
      console.error('Error fetching course and lessons:', error);
      setError('Failed to load course content. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleLessonSelect = (lesson) => {
    console.log('Selected lesson:', lesson.title);
    setCurrentLesson(lesson);
  };

  const handleUpdateProgress = async (lessonId) => {
    try {
      console.log('Updating progress for lesson:', lessonId);
      await courseService.updateProgress(id, { lesson_id: lessonId, completed: true });
      Alert.alert('Progress Updated', 'Your progress has been saved.');
    } catch (error) {
      console.error('Error updating progress:', error);
      Alert.alert('Error', 'Failed to update progress. Please try again.');
    }
  };

  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2563eb" />
      </ThemedView>
    );
  }

  if (error || !course) {
    return (
      <ThemedView style={styles.errorContainer}>
        <ThemedText style={styles.errorText}>{error || 'Course not found'}</ThemedText>
        <TouchableOpacity style={styles.retryButton} onPress={fetchCourseAndLessons}>
          <ThemedText style={styles.retryButtonText}>Retry</ThemedText>
        </TouchableOpacity>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedText type="title">{course.title}</ThemedText>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ThemedText style={styles.backButtonText}>Back to Course</ThemedText>
        </TouchableOpacity>
      </ThemedView>

      <ThemedView style={styles.content}>
        {currentLesson ? (
          <ThemedView style={styles.lessonContent}>
            <ThemedText type="subtitle">{currentLesson.title}</ThemedText>
            
            <ThemedView style={styles.lessonDetails}>
              {currentLesson.duration && (
                <ThemedText style={styles.lessonMeta}>Duration: {currentLesson.duration}</ThemedText>
              )}
              
              <ThemedText style={styles.lessonDescription}>
                {currentLesson.description || 'No description available for this lesson.'}
              </ThemedText>
              
              <ThemedView style={styles.videoPlaceholder}>
                <ThemedText style={styles.videoPlaceholderText}>
                  Video content would appear here
                </ThemedText>
              </ThemedView>
              
              <TouchableOpacity 
                style={styles.completeButton}
                onPress={() => handleUpdateProgress(currentLesson.id)}
              >
                <ThemedText style={styles.completeButtonText}>Mark as Completed</ThemedText>
              </TouchableOpacity>
            </ThemedView>
          </ThemedView>
        ) : (
          <ThemedView style={styles.noLessonContainer}>
            <ThemedText>No lessons available for this course.</ThemedText>
          </ThemedView>
        )}
        
        <ThemedView style={styles.lessonList}>
          <ThemedText type="subtitle" style={styles.lessonListTitle}>Lessons</ThemedText>
          <ScrollView>
            {lessons.map((lesson, index) => (
              <TouchableOpacity 
                key={lesson.id} 
                style={[
                  styles.lessonItem,
                  currentLesson?.id === lesson.id && styles.activeLesson
                ]}
                onPress={() => handleLessonSelect(lesson)}
              >
                <ThemedText 
                  style={[
                    styles.lessonItemText,
                    currentLesson?.id === lesson.id && styles.activeLessonText
                  ]}
                >
                  {index + 1}. {lesson.title}
                </ThemedText>
                {lesson.duration && (
                  <ThemedText style={styles.lessonDuration}>{lesson.duration}</ThemedText>
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </ThemedView>
      </ThemedView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#2563eb',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    marginTop: 8,
  },
  backButtonText: {
    color: '#2563eb',
  },
  content: {
    flex: 1,
    flexDirection: 'row',
  },
  lessonContent: {
    flex: 2,
    padding: 16,
    borderRightWidth: 1,
    borderRightColor: '#e5e7eb',
  },
  lessonDetails: {
    marginTop: 16,
  },
  lessonMeta: {
    marginBottom: 8,
    opacity: 0.7,
  },
  lessonDescription: {
    marginBottom: 16,
    lineHeight: 22,
  },
  videoPlaceholder: {
    height: 200,
    backgroundColor: '#f3f4f6',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    borderRadius: 8,
  },
  videoPlaceholderText: {
    opacity: 0.5,
  },
  completeButton: {
    backgroundColor: '#10b981',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  completeButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  lessonList: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f9fafb',
  },
  lessonListTitle: {
    marginBottom: 16,
  },
  lessonItem: {
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  activeLesson: {
    backgroundColor: '#e6f7ff',
    borderRadius: 8,
  },
  lessonItemText: {
    fontWeight: '500',
  },
  activeLessonText: {
    color: '#2563eb',
    fontWeight: 'bold',
  },
  lessonDuration: {
    fontSize: 12,
    opacity: 0.7,
    marginTop: 4,
  },
  noLessonContainer: {
    flex: 2,
    padding: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
