@extends('marketing.layouts.app')

@section('title', 'Blog - Naxofy')

@section('content')
    <!-- Hero Section -->
    <div class="bg-white">
        <div class="max-w-7xl mx-auto py-16 px-4 sm:py-24 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl font-extrabold tracking-tight text-gray-900 sm:text-5xl md:text-6xl">
                    <span class="block">Naxofy</span>
                    <span class="block text-primary-500">Blog</span>
                </h1>
                <p class="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">
                    Insights, tips, and news about online education, learning platforms, and educational technology.
                </p>
            </div>
        </div>
    </div>

    <!-- Featured Post -->
    <div class="bg-gray-50 py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="lg:text-center mb-10">
                <h2 class="text-base text-primary-500 font-semibold tracking-wide uppercase">Featured Post</h2>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="md:flex">
                    <div class="md:flex-shrink-0">
                        <img class="h-48 w-full object-cover md:w-48" src="https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=2850&q=80" alt="Featured post image">
                    </div>
                    <div class="p-8">
                        <div class="uppercase tracking-wide text-sm text-primary-500 font-semibold">Educational Technology</div>
                        <a href="{{ route('blog.show', 'the-future-of-online-learning-platforms') }}" class="block mt-1 text-lg leading-tight font-medium text-gray-900 hover:underline">The Future of Online Learning Platforms in 2025 and Beyond</a>
                        <p class="mt-2 text-gray-500">
                            Explore the emerging trends and technologies that will shape the future of online learning platforms in the coming years.
                        </p>
                        <div class="mt-4 flex items-center">
                            <div class="flex-shrink-0">
                                <img class="h-10 w-10 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="Author profile picture">
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-900">John Doe</p>
                                <div class="flex space-x-1 text-sm text-gray-500">
                                    <time datetime="2023-03-16">Mar 16, 2023</time>
                                    <span aria-hidden="true">&middot;</span>
                                    <span>8 min read</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Blog Posts -->
    <div class="bg-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="lg:text-center mb-10">
                <h2 class="text-base text-primary-500 font-semibold tracking-wide uppercase">Latest Articles</h2>
            </div>

            <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                @foreach($posts as $post)
                <!-- Blog Post -->
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <img class="h-48 w-full object-cover" src="{{ $post['image'] }}" alt="{{ $post['title'] }}">
                    <div class="p-6">
                        <div class="uppercase tracking-wide text-sm text-primary-500 font-semibold">{{ $post['category'] }}</div>
                        <a href="{{ route('blog.show', $post['slug']) }}" class="block mt-1 text-lg leading-tight font-medium text-gray-900 hover:underline">{{ $post['title'] }}</a>
                        <p class="mt-2 text-gray-500">
                            {{ $post['excerpt'] }}
                        </p>
                        <div class="mt-4 flex items-center">
                            <div class="flex-shrink-0">
                                <img class="h-10 w-10 rounded-full" src="{{ $post['author_avatar'] }}" alt="{{ $post['author'] }}">
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-900">{{ $post['author'] }}</p>
                                <div class="flex space-x-1 text-sm text-gray-500">
                                    <time datetime="{{ $post['published_at'] }}">{{ \Carbon\Carbon::parse($post['published_at'])->format('M d, Y') }}</time>
                                    <span aria-hidden="true">&middot;</span>
                                    <span>{{ $post['reading_time'] }} min read</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-12 flex justify-center">
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <span class="sr-only">Previous</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                    </a>
                    <a href="#" aria-current="page" class="z-10 bg-primary-50 border-primary-500 text-primary-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                        1
                    </a>
                    <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                        2
                    </a>
                    <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 hidden md:inline-flex relative items-center px-4 py-2 border text-sm font-medium">
                        3
                    </a>
                    <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                        ...
                    </span>
                    <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 hidden md:inline-flex relative items-center px-4 py-2 border text-sm font-medium">
                        8
                    </a>
                    <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                        9
                    </a>
                    <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                        10
                    </a>
                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <span class="sr-only">Next</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </a>
                </nav>
            </div>
        </div>
    </div>

    <!-- Newsletter Signup -->
    <div class="bg-gray-50 py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="max-w-2xl mx-auto text-center">
                <h2 class="text-3xl font-extrabold text-gray-900 sm:text-4xl">
                    Subscribe to our newsletter
                </h2>
                <p class="mt-4 text-lg leading-6 text-gray-500">
                    Get the latest articles, tips, and resources delivered straight to your inbox.
                </p>
                <div class="mt-8 sm:flex sm:justify-center">
                    <div class="rounded-md shadow sm:flex-1 sm:max-w-md">
                        <form class="sm:flex">
                            <label for="email-address" class="sr-only">Email address</label>
                            <input id="email-address" name="email" type="email" autocomplete="email" required class="w-full px-5 py-3 border-gray-300 focus:ring-primary-500 focus:border-primary-500 sm:max-w-xs rounded-md" placeholder="Enter your email">
                            <div class="mt-3 rounded-md shadow sm:mt-0 sm:ml-3 sm:flex-shrink-0">
                                <button type="submit" class="w-full flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                    Subscribe
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
