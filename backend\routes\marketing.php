<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Marketing\HomeController;
use App\Http\Controllers\Marketing\PricingController;
use App\Http\Controllers\Marketing\FeaturesController;
use App\Http\Controllers\Marketing\MobileAppController;
use App\Http\Controllers\Marketing\ThemesController;
use App\Http\Controllers\Marketing\ExtensionsController;
use App\Http\Controllers\Marketing\SignupController;
use App\Http\Controllers\Marketing\DocsController;
use App\Http\Controllers\Marketing\AboutController;
use App\Http\Controllers\Marketing\ContactController;
use App\Http\Controllers\Marketing\BlogController;

/*
|--------------------------------------------------------------------------
| Marketing Website Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for the marketing website. These
| routes are loaded by the RouteServiceProvider and assigned to the "web"
| middleware group.
|
*/

// Home page
Route::get('/', [HomeController::class, 'index'])->name('marketing.home');

// Pricing page
Route::get('/pricing', [PricingController::class, 'index'])->name('marketing.pricing');

// Features page
Route::get('/features', [FeaturesController::class, 'index'])->name('marketing.features');

// Mobile App page
Route::get('/mobile-app', [MobileAppController::class, 'index'])->name('marketing.mobile-app');

// Themes page
Route::get('/themes', [ThemesController::class, 'index'])->name('marketing.themes');

// Extensions/Apps Marketplace page
Route::get('/extensions', [ExtensionsController::class, 'index'])->name('marketing.extensions');

// Free Trial Signup page
Route::get('/signup', [SignupController::class, 'index'])->name('marketing.signup');
Route::post('/signup', [SignupController::class, 'store'])->name('marketing.signup.store');

// Documentation / Help Center
Route::get('/docs', [DocsController::class, 'index'])->name('marketing.docs');
Route::get('/docs/{slug}', [DocsController::class, 'show'])->name('marketing.docs.show');

// About Us page
Route::get('/about', [AboutController::class, 'index'])->name('marketing.about');

// Contact Us page
Route::get('/contact', [ContactController::class, 'index'])->name('marketing.contact');
Route::post('/contact', [ContactController::class, 'store'])->name('marketing.contact.store');

// Blog pages
Route::get('/blog', [BlogController::class, 'index'])->name('marketing.blog');
Route::get('/blog/{slug}', [BlogController::class, 'show'])->name('marketing.blog.show');
