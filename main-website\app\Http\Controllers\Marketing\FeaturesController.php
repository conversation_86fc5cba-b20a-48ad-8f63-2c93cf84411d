<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class FeaturesController extends Controller
{
    /**
     * Display the features page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $features = [
            [
                'name' => 'Course Management',
                'description' => 'Create and manage engaging courses with our intuitive course builder.',
                'icon' => 'academic-cap',
                'details' => [
                    'Drag-and-drop course builder',
                    'Multiple content types (video, text, quizzes, etc.)',
                    'Course templates',
                    'Drip content scheduling',
                    'Student progress tracking',
                    'Certificates of completion',
                ],
                'image' => 'https://tailwindui.com/img/component-images/inbox-app-screenshot-1.jpg',
            ],
            [
                'name' => 'Theme Customization',
                'description' => 'Customize the look and feel of your learning platform to match your brand.',
                'icon' => 'color-swatch',
                'details' => [
                    'Visual theme editor',
                    'Pre-built themes',
                    'Custom CSS/HTML',
                    'White-labeling options',
                    'Responsive design',
                    'Custom domain support',
                ],
                'image' => 'https://tailwindui.com/img/component-images/inbox-app-screenshot-2.jpg',
            ],
            [
                'name' => 'Mobile App',
                'description' => 'Offer your students a seamless learning experience on mobile devices.',
                'icon' => 'device-mobile',
                'details' => [
                    'White-label mobile app',
                    'Offline access',
                    'Push notifications',
                    'Native video playback',
                    'Course bookmarking',
                    'Mobile-optimized quizzes',
                ],
                'image' => 'https://tailwindui.com/img/component-images/inbox-app-screenshot-3.jpg',
            ],
            [
                'name' => 'Extension Marketplace',
                'description' => 'Extend your platform with powerful add-ons from our marketplace.',
                'icon' => 'puzzle',
                'details' => [
                    'Certificate generator',
                    'Gamification',
                    'Social learning',
                    'Advanced analytics',
                    'Live streaming',
                    'Integration with third-party services',
                ],
                'image' => 'https://tailwindui.com/img/component-images/inbox-app-screenshot-4.jpg',
            ],
        ];

        return view('marketing.features', compact('features'));
    }
}
