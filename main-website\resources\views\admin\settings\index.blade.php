@extends('admin.layouts.app')

@section('title', 'Settings')

@section('content')
<div class="container mx-auto px-6 py-8">
    <div class="flex items-center justify-between">
        <h3 class="text-3xl font-medium text-gray-700">Settings</h3>
    </div>

    <div class="mt-8">
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                    General Settings
                </h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                    Configure the general settings for your platform.
                </p>
            </div>
            <div class="border-t border-gray-200">
                <form action="#" method="POST">
                    @csrf
                    <div class="px-4 py-5 bg-white sm:p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="site_name" class="block text-sm font-medium text-gray-700">Site Name</label>
                                <input type="text" name="site_name" id="site_name" value="{{ $settings->site_name }}" class="mt-1 focus:ring-primary focus:border-primary block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                            </div>

                            <div>
                                <label for="contact_email" class="block text-sm font-medium text-gray-700">Contact Email</label>
                                <input type="email" name="contact_email" id="contact_email" value="{{ $settings->contact_email }}" class="mt-1 focus:ring-primary focus:border-primary block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                            </div>

                            <div>
                                <label for="primary_color" class="block text-sm font-medium text-gray-700">Primary Color</label>
                                <div class="mt-1 flex rounded-md shadow-sm">
                                    <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                                        #
                                    </span>
                                    <input type="text" name="primary_color" id="primary_color" value="{{ ltrim($settings->primary_color, '#') }}" class="focus:ring-primary focus:border-primary flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300">
                                </div>
                            </div>

                            <div>
                                <label for="secondary_color" class="block text-sm font-medium text-gray-700">Secondary Color</label>
                                <div class="mt-1 flex rounded-md shadow-sm">
                                    <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                                        #
                                    </span>
                                    <input type="text" name="secondary_color" id="secondary_color" value="{{ ltrim($settings->secondary_color, '#') }}" class="focus:ring-primary focus:border-primary flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300">
                                </div>
                            </div>

                            <div>
                                <label for="contact_phone" class="block text-sm font-medium text-gray-700">Contact Phone</label>
                                <input type="text" name="contact_phone" id="contact_phone" value="{{ $settings->contact_phone }}" class="mt-1 focus:ring-primary focus:border-primary block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                            </div>

                            <div class="md:col-span-2">
                                <label for="site_description" class="block text-sm font-medium text-gray-700">Site Description</label>
                                <textarea id="site_description" name="site_description" rows="3" class="mt-1 focus:ring-primary focus:border-primary block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">{{ $settings->site_description }}</textarea>
                            </div>

                            <div class="md:col-span-2">
                                <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
                                <textarea id="address" name="address" rows="3" class="mt-1 focus:ring-primary focus:border-primary block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">{{ $settings->address }}</textarea>
                            </div>
                        </div>
                    </div>
                    <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
                        <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                            Save Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                    Social Media Links
                </h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                    Configure your social media links.
                </p>
            </div>
            <div class="border-t border-gray-200">
                <form action="#" method="POST">
                    @csrf
                    <div class="px-4 py-5 bg-white sm:p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="facebook" class="block text-sm font-medium text-gray-700">Facebook</label>
                                <input type="url" name="facebook" id="facebook" value="{{ $settings->social_links['facebook'] }}" class="mt-1 focus:ring-primary focus:border-primary block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                            </div>

                            <div>
                                <label for="twitter" class="block text-sm font-medium text-gray-700">Twitter</label>
                                <input type="url" name="twitter" id="twitter" value="{{ $settings->social_links['twitter'] }}" class="mt-1 focus:ring-primary focus:border-primary block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                            </div>

                            <div>
                                <label for="instagram" class="block text-sm font-medium text-gray-700">Instagram</label>
                                <input type="url" name="instagram" id="instagram" value="{{ $settings->social_links['instagram'] }}" class="mt-1 focus:ring-primary focus:border-primary block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                            </div>

                            <div>
                                <label for="linkedin" class="block text-sm font-medium text-gray-700">LinkedIn</label>
                                <input type="url" name="linkedin" id="linkedin" value="{{ $settings->social_links['linkedin'] }}" class="mt-1 focus:ring-primary focus:border-primary block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                            </div>
                        </div>
                    </div>
                    <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
                        <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                            Save Social Links
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
