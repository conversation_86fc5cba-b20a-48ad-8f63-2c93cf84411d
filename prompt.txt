🧾 Project Prompt: "Build a Shopify-like SaaS LMS with Web & Mobile App Support"
📌 Objective:
Build a full-fledged SaaS-based Learning Management System (LMS), inspired by Shopify's model, where each institute, instructor, or training center can sign up, manage their own branded portal, and sell courses. The platform must also include a mobile app for students.

💻 Tech Stack
Frontend (Web Dashboards): React.js

Backend (API): Laravel 10 (RESTful API + JWT)

Mobile App: React Native (for Students)

Database: MySQL

Storage: AWS S3 (for videos, PDFs)

Authentication: JWT (role-based)

Payments: Razorpay

Hosting:

Frontend: Vercel

Backend: Render

Optional Add-ons: Cloudflare, Redis, Firebase (for push notifications)

👥 User Roles
Super Admin

Manages all tenants (instructors/schools)

Approves new tenant registration

Views global analytics and revenue

Tenant (Instructor/School Owner)

Registers and creates a custom portal

Uploads & manages courses

Sees enrolled students

Manages earnings and profile

Student

Registers and browses courses

Enrolls and watches content

Tracks progress

Takes quizzes

📦 Core Features (MVP)
🔐 Authentication (JWT-based)
Login, Register (Student/Instructor/Admin)

Forgot password

Role + Tenant-based access control

🏫 Multi-Tenant Support
Each tenant gets their own dashboard and branding

All data is tenant-isolated via tenant_id

Tenant-aware routing (tenantname.domain.com or subpath like /t/tenantname)

📚 Course Management (Instructor)
Create, update, delete courses

Upload video lessons (to AWS S3)

Upload PDFs, add quizzes

Set course price (free/paid)

🎓 Student Dashboard
Browse/enroll in courses

View progress (video tracking, quiz results)

Download resources

Payment history

💳 Payments (Razorpay)
One-time purchase per course OR

Subscription model per tenant (Optional)

Transaction history (Admin & Instructor)

📈 Admin Dashboard
View tenants, users, courses, earnings

Analytics by month/tenant

Deactivate/ban users or tenants

📱 Mobile App (React Native)
Login/Register (Student)

Browse and enroll in courses

Watch videos via streaming

Download PDFs

Track course progress

Push notifications (Optional via Firebase)

📊 Database Tables Overview
users (id, name, email, password, role, tenant_id)

tenants (id, name, domain, logo, is_active)

courses (id, title, description, price, tenant_id, instructor_id)

lessons (id, course_id, video_url, type)

resources (id, course_id, file_url, type)

quizzes, quiz_questions, quiz_answers

enrollments (user_id, course_id, progress, payment_status)

payments (user_id, course_id, amount, razorpay_id)

notifications, messages, reviews, etc. (later stages)

🚀 Hosting & Deployment
Frontend (React): Deploy on Vercel

Backend (Laravel API): Deploy on Render or Railway

Mobile App: Deploy on Google Play (Expo EAS or native build)

Assets (S3): Secure file upload using signed URLs

SSL & CDN: Use Cloudflare for free SSL and global delivery

🧪 Post-MVP Features (For Scaling)
Issue certificates on course completion

Institute-wise white-labeled portals

Affiliate/referral system

Ratings & reviews

Live class support (Zoom API or Agora)

Chat support (WebSocket or Firebase)

Analytics (course performance, student insights)

Email notifications (SendGrid/Mailgun)

💡 Development Plan (Suggested Order)
Setup Laravel backend with tenant-aware APIs + JWT

Build React frontend for Instructor & Admin

Add course & media management (with S3 integration)

Setup payments via Razorpay

Develop student dashboard

Build & integrate React Native app (for students)

Finalize deployment + domain setup