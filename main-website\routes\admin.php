<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\TenantController;
use App\Http\Controllers\Admin\SubscriptionController;
use App\Http\Controllers\Admin\ThemeController;
use App\Http\Controllers\Admin\SettingController;
use App\Http\Controllers\Admin\AnalyticsController;
use App\Http\Controllers\Admin\ExtensionController;
use App\Http\Controllers\Admin\SupportController;

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| These routes are for the admin dashboard and functionality.
| They are protected by the 'admin' middleware.
|
*/

// Dashboard
Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

// Tenant Management
Route::resource('tenants', TenantController::class);

// User Management
Route::get('/users', [DashboardController::class, 'users'])->name('users.index');
Route::get('/users/create', [DashboardController::class, 'createUser'])->name('users.create');
Route::get('/users/{id}', [DashboardController::class, 'showUser'])->name('users.show');
Route::get('/users/{id}/edit', [DashboardController::class, 'editUser'])->name('users.edit');

// Subscription & Plans
Route::resource('plans', SubscriptionController::class);
Route::resource('subscriptions', SubscriptionController::class);

// Payments
Route::get('/payments', [DashboardController::class, 'payments'])->name('payments.index');
Route::get('/payments/{id}', [DashboardController::class, 'showPayment'])->name('payments.show');
Route::get('/payment-settings', [DashboardController::class, 'paymentSettings'])->name('payments.settings');

// Themes & Templates
Route::resource('themes', ThemeController::class);
Route::get('/templates', [DashboardController::class, 'templates'])->name('templates.index');
Route::get('/templates/create', [DashboardController::class, 'createTemplate'])->name('templates.create');
Route::get('/templates/{id}', [DashboardController::class, 'showTemplate'])->name('templates.show');
Route::get('/templates/{id}/edit', [DashboardController::class, 'editTemplate'])->name('templates.edit');

// Extensions
Route::resource('extensions', ExtensionController::class);
Route::post('/extensions/{id}/enable', [ExtensionController::class, 'enable'])->name('extensions.enable');
Route::post('/extensions/{id}/disable', [ExtensionController::class, 'disable'])->name('extensions.disable');
Route::post('/extensions/{id}/plans', [ExtensionController::class, 'updatePlans'])->name('extensions.plans.update');
Route::get('/extension-marketplace', [ExtensionController::class, 'marketplace'])->name('extensions.marketplace');
Route::get('/extension-requests', [ExtensionController::class, 'requests'])->name('extensions.requests');
Route::post('/extension-requests/{id}/approve', [ExtensionController::class, 'approveRequest'])->name('extensions.requests.approve');
Route::post('/extension-requests/{id}/reject', [ExtensionController::class, 'rejectRequest'])->name('extensions.requests.reject');

// Settings
Route::get('/settings', [SettingController::class, 'index'])->name('settings.index');
Route::get('/settings/email', [SettingController::class, 'email'])->name('settings.email');
Route::get('/settings/storage', [SettingController::class, 'storage'])->name('settings.storage');
Route::get('/settings/security', [SettingController::class, 'security'])->name('settings.security');
Route::get('/settings/api', [SettingController::class, 'api'])->name('settings.api');

// Support & Tickets
Route::resource('support/tickets', SupportController::class);
Route::get('/support/email-campaigns', [SupportController::class, 'emailCampaigns'])->name('support.email-campaigns');
Route::get('/support/email-campaigns/create', [SupportController::class, 'createEmailCampaign'])->name('support.email-campaigns.create');
Route::get('/support/email-campaigns/{id}', [SupportController::class, 'showEmailCampaign'])->name('support.email-campaigns.show');

// Notifications
Route::get('/notifications', [DashboardController::class, 'notifications'])->name('notifications.index');
Route::get('/notifications/{id}', [DashboardController::class, 'showNotification'])->name('notifications.show');
Route::post('/notifications/{id}/mark-as-read', [DashboardController::class, 'markNotificationAsRead'])->name('notifications.mark-as-read');
Route::post('/notifications/mark-all-as-read', [DashboardController::class, 'markAllNotificationsAsRead'])->name('notifications.mark-all-as-read');
Route::delete('/notifications/{id}', [DashboardController::class, 'deleteNotification'])->name('notifications.delete');

// Analytics
Route::get('/analytics', [AnalyticsController::class, 'index'])->name('analytics.index');
Route::get('/analytics/users', [AnalyticsController::class, 'users'])->name('analytics.users');
Route::get('/analytics/tenants', [AnalyticsController::class, 'tenants'])->name('analytics.tenants');
Route::get('/analytics/revenue', [AnalyticsController::class, 'revenue'])->name('analytics.revenue');
Route::get('/analytics/usage', [AnalyticsController::class, 'usage'])->name('analytics.usage');
Route::get('/analytics/export', [AnalyticsController::class, 'export'])->name('analytics.export');
