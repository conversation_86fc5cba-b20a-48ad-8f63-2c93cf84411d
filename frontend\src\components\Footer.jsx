import { Box, Container, Grid, Typography, <PERSON>, Divider, IconButton } from '@mui/material';
import FacebookIcon from '@mui/icons-material/Facebook';
import TwitterIcon from '@mui/icons-material/Twitter';
import InstagramIcon from '@mui/icons-material/Instagram';
import LinkedInIcon from '@mui/icons-material/LinkedIn';
import { Link as RouterLink } from 'react-router-dom';

const Footer = () => {
  return (
    <Box
      sx={{
        bgcolor: '#1e293b',
        color: 'white',
        py: { xs: 6, md: 8 },
        mt: 'auto',
      }}
      component="footer"
    >
      <Container maxWidth={false} sx={{ px: { xs: 2, sm: 4, md: 6 }, maxWidth: '1600px' }}>
        <Grid container spacing={4} sx={{ mb: 6 }}>
          <Grid item xs={12} md={4} sx={{ pr: { md: 8 } }}>
            <Typography variant="h5" gutterBottom sx={{ fontWeight: 700, mb: 3 }}>
              Naxofy
            </Typography>
            <Typography variant="body2" sx={{ mb: 3, opacity: 0.8, lineHeight: 1.8 }}>
              A comprehensive learning management system for online education. Create, share, and monetize your knowledge with our cutting-edge platform.
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <IconButton color="inherit" aria-label="Facebook" size="small" sx={{ opacity: 0.8, '&:hover': { opacity: 1 } }}>
                <FacebookIcon />
              </IconButton>
              <IconButton color="inherit" aria-label="Twitter" size="small" sx={{ opacity: 0.8, '&:hover': { opacity: 1 } }}>
                <TwitterIcon />
              </IconButton>
              <IconButton color="inherit" aria-label="Instagram" size="small" sx={{ opacity: 0.8, '&:hover': { opacity: 1 } }}>
                <InstagramIcon />
              </IconButton>
              <IconButton color="inherit" aria-label="LinkedIn" size="small" sx={{ opacity: 0.8, '&:hover': { opacity: 1 } }}>
                <LinkedInIcon />
              </IconButton>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, mb: 3 }}>
              Quick Links
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
              <Link component={RouterLink} to="/" color="inherit" sx={{ opacity: 0.8, '&:hover': { opacity: 1 } }}>
                Home
              </Link>
              <Link component={RouterLink} to="/login" color="inherit" sx={{ opacity: 0.8, '&:hover': { opacity: 1 } }}>
                Login
              </Link>
              <Link component={RouterLink} to="/register" color="inherit" sx={{ opacity: 0.8, '&:hover': { opacity: 1 } }}>
                Register
              </Link>
              <Link component={RouterLink} to="/tenant/register" color="inherit" sx={{ opacity: 0.8, '&:hover': { opacity: 1 } }}>
                Become an Instructor
              </Link>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, mb: 3 }}>
              Resources
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
              <Link component={RouterLink} to="#" color="inherit" sx={{ opacity: 0.8, '&:hover': { opacity: 1 } }}>
                Blog
              </Link>
              <Link component={RouterLink} to="#" color="inherit" sx={{ opacity: 0.8, '&:hover': { opacity: 1 } }}>
                Documentation
              </Link>
              <Link component={RouterLink} to="#" color="inherit" sx={{ opacity: 0.8, '&:hover': { opacity: 1 } }}>
                Help Center
              </Link>
              <Link component={RouterLink} to="#" color="inherit" sx={{ opacity: 0.8, '&:hover': { opacity: 1 } }}>
                Community
              </Link>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 600, mb: 3 }}>
              Contact Us
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
              <Typography variant="body2" sx={{ opacity: 0.8 }}>
                Email: <EMAIL>
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.8 }}>
                Phone: +****************
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.8 }}>
                Address: 123 Education St, Learning City, 10001
              </Typography>
            </Box>
          </Grid>
        </Grid>
        <Divider sx={{ borderColor: 'rgba(255,255,255,0.1)', my: 4 }} />
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
          <Typography variant="body2" sx={{ opacity: 0.8 }}>
            © {new Date().getFullYear()} Naxofy. All rights reserved.
          </Typography>
          <Box sx={{ display: 'flex', gap: 3 }}>
            <Link component={RouterLink} to="#" color="inherit" sx={{ opacity: 0.8, fontSize: '0.875rem', '&:hover': { opacity: 1 } }}>
              Privacy Policy
            </Link>
            <Link component={RouterLink} to="#" color="inherit" sx={{ opacity: 0.8, fontSize: '0.875rem', '&:hover': { opacity: 1 } }}>
              Terms of Service
            </Link>
            <Link component={RouterLink} to="#" color="inherit" sx={{ opacity: 0.8, fontSize: '0.875rem', '&:hover': { opacity: 1 } }}>
              Cookie Policy
            </Link>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
