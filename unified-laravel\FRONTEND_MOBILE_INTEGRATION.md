# Frontend and Mobile Integration Guide

This document provides instructions for updating the React frontend and React Native mobile app to work with the unified Laravel backend.

## 1. API Endpoint Updates

### React Frontend

1. **Update API Base URL**

   Open `frontend/src/services/api.js` and update the API URL:

   ```javascript
   // Before
   const API_URL = 'http://localhost:8000/api';
   
   // After
   const API_URL = 'http://localhost:8000/api';
   ```

   Note: The URL remains the same, but now it points to the unified backend.

2. **Update API Service Functions**

   Review all API service functions to ensure they match the updated API endpoints. Most endpoints should remain the same, but verify any that might have changed in the consolidation.

### React Native Mobile App

1. **Update API Base URL**

   Open `mobile/services/api.js` and update the API URL:

   ```javascript
   // Before
   const YOUR_IP_ADDRESS = 'localhost';
   const API_URL = USE_MOCK_DATA ? null : `http://${YOUR_IP_ADDRESS}:8000/api`;
   
   // After
   const YOUR_IP_ADDRESS = 'localhost';
   const API_URL = USE_MOCK_DATA ? null : `http://${YOUR_IP_ADDRESS}:8000/api`;
   ```

   Note: The URL remains the same, but now it points to the unified backend.

2. **Update API Service Functions**

   Review all API service functions to ensure they match the updated API endpoints.

## 2. Authentication Updates

### React Frontend

1. **Update Authentication Flow**

   The authentication flow remains largely the same, but ensure that the token handling is consistent with the unified backend:

   ```javascript
   // In frontend/src/services/api.js
   export const authService = {
     login: (credentials) => api.post('/auth/login', credentials),
     register: (userData) => api.post('/auth/register', userData),
     registerTenant: (tenantData) => api.post('/auth/register-tenant', tenantData),
     logout: () => api.post('/auth/logout'),
     getProfile: () => api.get('/auth/profile'),
   };
   ```

2. **Update Auth Context**

   Ensure that the authentication context is properly handling the JWT token from the unified backend.

### React Native Mobile App

1. **Update Authentication Flow**

   Similar to the frontend, ensure that the token handling is consistent:

   ```javascript
   // In mobile/services/api.js
   export const authService = {
     login: async (credentials) => {
       if (USE_MOCK_DATA) {
         // Mock implementation
       } else {
         return api.post('/auth/login', credentials);
       }
     },
     // Other auth methods...
   };
   ```

2. **Update Token Storage**

   Ensure that AsyncStorage is properly storing and retrieving the JWT token.

## 3. Tenant-Specific Routes

### React Frontend

1. **Update Tenant Routes**

   If your frontend has tenant-specific routes, ensure they point to the correct endpoints:

   ```javascript
   // Example tenant course listing
   export const tenantService = {
     getCourses: (tenantId) => api.get(`/tenants/${tenantId}/courses`),
     // Other tenant methods...
   };
   ```

### React Native Mobile App

1. **Update Tenant Routes**

   Similarly, update any tenant-specific routes in the mobile app.

## 4. Environment Configuration

### React Frontend

1. **Update Environment Variables**

   Update the `.env` file in the frontend project:

   ```
   VITE_API_URL=http://localhost:8000/api
   VITE_TENANT_DOMAIN=localhost
   ```

2. **Update Vite Configuration**

   Ensure that the Vite configuration is properly loading environment variables.

### React Native Mobile App

1. **Update Environment Variables**

   Update the `.env` file in the mobile project:

   ```
   EXPO_PUBLIC_API_URL=http://localhost:8000/api
   EXPO_PUBLIC_TENANT_DOMAIN=localhost
   ```

## 5. Testing the Integration

### React Frontend

1. **Test Authentication**
   - Test user login
   - Test user registration
   - Test tenant registration
   - Test profile retrieval

2. **Test API Endpoints**
   - Test course listing
   - Test tenant-specific endpoints
   - Test data creation and updates

### React Native Mobile App

1. **Test Authentication**
   - Test user login on mobile
   - Test token persistence
   - Test automatic login

2. **Test API Endpoints**
   - Test course listing
   - Test offline functionality
   - Test data synchronization

## 6. Handling Tenant Subdomains

### React Frontend

1. **Update Subdomain Handling**

   If your frontend needs to handle tenant subdomains, update the logic:

   ```javascript
   // Example function to get tenant domain
   export const getTenantDomain = (tenantSlug) => {
     return `${tenantSlug}.${import.meta.env.VITE_TENANT_DOMAIN}`;
   };
   ```

### React Native Mobile App

1. **Update Tenant Configuration**

   Ensure the mobile app can properly connect to tenant-specific APIs:

   ```javascript
   // Example function to get tenant API URL
   export const getTenantApiUrl = (tenantSlug) => {
     return `http://${tenantSlug}.${process.env.EXPO_PUBLIC_TENANT_DOMAIN}/api`;
   };
   ```

## 7. Error Handling

1. **Consistent Error Handling**

   Ensure that both the frontend and mobile app handle API errors consistently:

   ```javascript
   // Example error interceptor
   api.interceptors.response.use(
     (response) => response,
     (error) => {
       // Handle different error types
       if (error.response) {
         // Server responded with an error status
         console.error('API Error:', error.response.data);
         
         // Handle authentication errors
         if (error.response.status === 401) {
           // Handle unauthorized
         }
       }
       return Promise.reject(error);
     }
   );
   ```

## 8. Deployment Considerations

1. **Build Process**
   - Update build scripts if necessary
   - Ensure environment variables are properly set during build

2. **CORS Configuration**
   - Verify that CORS is properly configured in the unified backend
   - Test cross-origin requests

3. **Production URLs**
   - Update production API URLs
   - Test with production endpoints

## Conclusion

By following these steps, you can successfully integrate the React frontend and React Native mobile app with the unified Laravel backend. This integration will provide a more consistent and maintainable architecture for the entire Naxofy LMS platform.
