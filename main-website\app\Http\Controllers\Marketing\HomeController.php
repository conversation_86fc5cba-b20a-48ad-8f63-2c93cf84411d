<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Display the home page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $featuredCourses = [
            [
                'id' => 1,
                'title' => 'Introduction to Web Development',
                'description' => 'Learn the basics of HTML, CSS, and JavaScript',
                'instructor' => '<PERSON>',
                'price' => 49.99,
                'thumbnail' => 'https://via.placeholder.com/300x200',
                'rating' => 4.5,
                'ratings_count' => 120,
            ],
            [
                'id' => 2,
                'title' => 'Advanced React Development',
                'description' => 'Master React hooks, context, and advanced patterns',
                'instructor' => '<PERSON>',
                'price' => 69.99,
                'thumbnail' => 'https://via.placeholder.com/300x200',
                'rating' => 4.8,
                'ratings_count' => 85,
            ],
            [
                'id' => 3,
                'title' => 'Laravel for Beginners',
                'description' => 'Build your first web application with <PERSON><PERSON>',
                'instructor' => '<PERSON>',
                'price' => 59.99,
                'thumbnail' => 'https://via.placeholder.com/300x200',
                'rating' => 4.6,
                'ratings_count' => 95,
            ],
        ];

        $testimonials = [
            [
                'name' => 'Sarah Thompson',
                'role' => 'Founder, CodeAcademy',
                'content' => 'We launched our LMS with this platform and grew 3x in just 6 months. The customization options are incredible!',
                'avatar' => 'https://via.placeholder.com/100x100',
            ],
            [
                'name' => 'David Wilson',
                'role' => 'Director, TechEd Institute',
                'content' => 'The white-label mobile app feature helped us reach students who prefer learning on their phones. Our enrollment increased by 40%.',
                'avatar' => 'https://via.placeholder.com/100x100',
            ],
            [
                'name' => 'Emily Rodriguez',
                'role' => 'CEO, SkillMaster',
                'content' => 'The theme customization and extension marketplace made it easy to create a unique learning experience for our students.',
                'avatar' => 'https://via.placeholder.com/100x100',
            ],
        ];

        return view('marketing.home', compact('featuredCourses', 'testimonials'));
    }
}
