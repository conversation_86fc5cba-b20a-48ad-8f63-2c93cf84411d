<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ExtensionsController extends Controller
{
    /**
     * Display the extensions/marketplace page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $categories = [
            'Assessment',
            'Communication',
            'Content',
            'Engagement',
            'Integration',
            'Marketing',
            'Payment',
            'Reporting',
        ];

        $extensions = [
            [
                'name' => 'Certificate Generator',
                'description' => 'Create beautiful certificates for your students upon course completion.',
                'thumbnail' => 'img/marketing/extensions/certificate.jpg',
                'category' => 'Assessment',
                'price' => 0,
                'popular' => true,
            ],
            [
                'name' => 'WhatsApp Integration',
                'description' => 'Send course updates and notifications directly to students via WhatsApp.',
                'thumbnail' => 'img/marketing/extensions/whatsapp.jpg',
                'category' => 'Communication',
                'price' => 29,
                'popular' => true,
            ],
            [
                'name' => 'Advanced Quiz Builder',
                'description' => 'Create interactive quizzes with various question types and scoring options.',
                'thumbnail' => 'img/marketing/extensions/quiz.jpg',
                'category' => 'Assessment',
                'price' => 49,
                'popular' => false,
            ],
            [
                'name' => 'Zoom Integration',
                'description' => 'Schedule and host live classes directly through Zoom within your platform.',
                'thumbnail' => 'img/marketing/extensions/zoom.jpg',
                'category' => 'Communication',
                'price' => 39,
                'popular' => true,
            ],
            [
                'name' => 'Gamification',
                'description' => 'Add points, badges, and leaderboards to increase student engagement.',
                'thumbnail' => 'img/marketing/extensions/gamification.jpg',
                'category' => 'Engagement',
                'price' => 59,
                'popular' => false,
            ],
            [
                'name' => 'Social Login',
                'description' => 'Allow students to sign up and log in using their social media accounts.',
                'thumbnail' => 'img/marketing/extensions/social-login.jpg',
                'category' => 'Integration',
                'price' => 19,
                'popular' => false,
            ],
        ];

        return view('marketing.extensions', compact('categories', 'extensions'));
    }
}
