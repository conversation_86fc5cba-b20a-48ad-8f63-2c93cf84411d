import React, { useState, useEffect } from 'react';
import { Link as RouterLink, useNavigate, useParams, useLocation } from 'react-router-dom';
import {
  Container,
  Box,
  Typography,
  Button,
  Link,
  Paper,
  Alert,
  CircularProgress
} from '@mui/material';
import { authService } from '../../services/api';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';

const EmailVerification = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const navigate = useNavigate();
  const { token } = useParams();
  const location = useLocation();
  const email = new URLSearchParams(location.search).get('email');

  useEffect(() => {
    const verifyEmail = async () => {
      if (!token || !email) {
        setError('Invalid verification link. Please request a new one.');
        setLoading(false);
        return;
      }

      try {
        await authService.verifyEmail({ email, token });
        setSuccess(true);
      } catch (error) {
        console.error('Email verification error:', error);
        setError(error.response?.data?.message || 'Failed to verify email. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    verifyEmail();
  }, [token, email]);

  const handleResendVerification = async () => {
    setLoading(true);
    try {
      await authService.resendVerification({ email });
      setError('A new verification link has been sent to your email address.');
    } catch (error) {
      console.error('Resend verification error:', error);
      setError(error.response?.data?.message || 'Failed to resend verification email. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="sm">
      <Box sx={{ mt: 8, mb: 4 }}>
        <Paper elevation={3} sx={{ p: 4, borderRadius: 2 }}>
          <Box sx={{ textAlign: 'center', mb: 3 }}>
            <Typography variant="h4" component="h1" gutterBottom>
              Email Verification
            </Typography>
          </Box>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
              <CircularProgress />
            </Box>
          ) : success ? (
            <Box sx={{ textAlign: 'center' }}>
              <CheckCircleOutlineIcon color="success" sx={{ fontSize: 80, mb: 2 }} />
              <Typography variant="h5" gutterBottom>
                Email Verified Successfully!
              </Typography>
              <Typography variant="body1" color="text.secondary" paragraph>
                Your email has been verified. You can now access all features of the platform.
              </Typography>
              <Button
                variant="contained"
                component={RouterLink}
                to="/login"
                sx={{ mt: 2 }}
              >
                Go to Login
              </Button>
            </Box>
          ) : (
            <Box sx={{ textAlign: 'center' }}>
              <ErrorOutlineIcon color="error" sx={{ fontSize: 80, mb: 2 }} />
              <Typography variant="h5" gutterBottom>
                Verification Failed
              </Typography>
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
              <Typography variant="body1" paragraph>
                If your verification link has expired, you can request a new one.
              </Typography>
              <Button
                variant="contained"
                onClick={handleResendVerification}
                disabled={loading}
                sx={{ mt: 2, mr: 2 }}
              >
                Resend Verification
              </Button>
              <Button
                variant="outlined"
                component={RouterLink}
                to="/login"
                sx={{ mt: 2 }}
              >
                Back to Login
              </Button>
            </Box>
          )}
        </Paper>
      </Box>
    </Container>
  );
};

export default EmailVerification;
