<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Module;
use App\Models\Theme;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MarketplaceController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    /**
     * Get featured items from the marketplace.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function featured()
    {
        // Get featured themes
        $themes = Theme::where('is_featured', true)
            ->where('status', 'approved')
            ->whereNull('tenant_id')
            ->take(6)
            ->get();

        // Get featured modules
        $modules = Module::where('is_featured', true)
            ->where('status', 'approved')
            ->whereNull('tenant_id')
            ->take(6)
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'themes' => $themes,
                'modules' => $modules,
            ]
        ]);
    }

    /**
     * Get themes from the marketplace.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function themes(Request $request)
    {
        $query = Theme::query()
            ->where('status', 'approved')
            ->whereNull('tenant_id');

        // Filter by premium status
        if ($request->has('is_premium')) {
            $query->where('is_premium', $request->boolean('is_premium'));
        }

        // Filter by featured status
        if ($request->has('is_featured')) {
            $query->where('is_featured', $request->boolean('is_featured'));
        }

        // Filter by price range
        if ($request->has('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }
        if ($request->has('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        // Search by name or description
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Sort by
        $sortBy = $request->input('sort_by', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Paginate
        $perPage = $request->input('per_page', 10);
        $themes = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $themes
        ]);
    }

    /**
     * Get modules from the marketplace.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function modules(Request $request)
    {
        $query = Module::query()
            ->where('status', 'approved')
            ->whereNull('tenant_id');

        // Filter by premium status
        if ($request->has('is_premium')) {
            $query->where('is_premium', $request->boolean('is_premium'));
        }

        // Filter by featured status
        if ($request->has('is_featured')) {
            $query->where('is_featured', $request->boolean('is_featured'));
        }

        // Filter by price range
        if ($request->has('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }
        if ($request->has('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        // Search by name or description
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Sort by
        $sortBy = $request->input('sort_by', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Paginate
        $perPage = $request->input('per_page', 10);
        $modules = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $modules
        ]);
    }

    /**
     * Get a specific theme from the marketplace.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function showTheme(string $id)
    {
        $theme = Theme::where('id', $id)
            ->where('status', 'approved')
            ->whereNull('tenant_id')
            ->first();

        if (!$theme) {
            return response()->json([
                'success' => false,
                'message' => 'Theme not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $theme
        ]);
    }

    /**
     * Get a specific module from the marketplace.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function showModule(string $id)
    {
        $module = Module::where('id', $id)
            ->where('status', 'approved')
            ->whereNull('tenant_id')
            ->first();

        if (!$module) {
            return response()->json([
                'success' => false,
                'message' => 'Module not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $module
        ]);
    }

    /**
     * Get installed themes for the authenticated tenant.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function installedThemes()
    {
        $user = Auth::user();

        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can view installed themes'
            ], 403);
        }

        $themes = Theme::where('tenant_id', $user->tenant_id)->get();

        return response()->json([
            'success' => true,
            'data' => $themes
        ]);
    }

    /**
     * Get installed modules for the authenticated tenant.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function installedModules()
    {
        $user = Auth::user();

        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can view installed modules'
            ], 403);
        }

        $modules = $user->tenant->installedModules;

        return response()->json([
            'success' => true,
            'data' => $modules
        ]);
    }
}
