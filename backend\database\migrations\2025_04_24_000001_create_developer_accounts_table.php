<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('developer_accounts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('company_name')->nullable();
            $table->string('website')->nullable();
            $table->text('bio')->nullable();
            $table->string('logo')->nullable();
            $table->string('api_key')->unique()->nullable();
            $table->string('api_secret')->nullable();
            $table->boolean('is_verified')->default(false);
            $table->boolean('has_accepted_agreement')->default(false);
            $table->timestamp('agreement_accepted_at')->nullable();
            $table->enum('status', ['pending', 'approved', 'rejected', 'suspended'])->default('pending');
            $table->text('rejection_reason')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('developer_accounts');
    }
};
