<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class AboutController extends Controller
{
    /**
     * Display the about page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $team = [
            [
                'name' => '<PERSON>',
                'role' => 'CEO & Co-Founder',
                'bio' => '<PERSON> has over 15 years of experience in education technology and previously founded two successful EdTech startups.',
                'avatar' => 'https://source.unsplash.com/random/400x400/?man,suit',
            ],
            [
                'name' => '<PERSON>',
                'role' => 'CTO & Co-Founder',
                'bio' => '<PERSON> is a former Google engineer with a passion for creating technology that makes education more accessible and effective.',
                'avatar' => 'https://source.unsplash.com/random/400x400/?woman,tech',
            ],
            [
                'name' => '<PERSON>',
                'role' => 'Head of Product',
                'bio' => '<PERSON> brings his experience as a former educator and instructional designer to ensure our platform meets the needs of both educators and learners.',
                'avatar' => 'https://source.unsplash.com/random/400x400/?man,asian',
            ],
            [
                'name' => '<PERSON>',
                'role' => 'Head of Customer Success',
                'bio' => '<PERSON> ensures that our customers have everything they need to succeed with our platform, from onboarding to ongoing support.',
                'avatar' => 'https://source.unsplash.com/random/400x400/?woman,latina',
            ],
            [
                'name' => 'David Wilson',
                'role' => 'Head of Marketing',
                'bio' => 'David has helped numerous SaaS companies scale their marketing efforts and brings his expertise to help us reach more educators worldwide.',
                'avatar' => 'https://source.unsplash.com/random/400x400/?man,marketing',
            ],
            [
                'name' => 'Lisa Patel',
                'role' => 'Head of Engineering',
                'bio' => 'Lisa leads our engineering team with a focus on building scalable, reliable, and secure software that powers thousands of learning platforms.',
                'avatar' => 'https://source.unsplash.com/random/400x400/?woman,indian',
            ],
        ];

        $milestones = [
            [
                'year' => '2018',
                'title' => 'Company Founded',
                'description' => 'Our company was founded with a mission to make it easy for anyone to create and sell online courses.',
            ],
            [
                'year' => '2019',
                'title' => 'First 100 Customers',
                'description' => 'We reached our first 100 customers, helping them create learning platforms for their audiences.',
            ],
            [
                'year' => '2020',
                'title' => 'Mobile App Launch',
                'description' => 'We launched our white-label mobile app solution, allowing our customers to offer mobile learning experiences.',
            ],
            [
                'year' => '2021',
                'title' => 'Series A Funding',
                'description' => 'We raised $10M in Series A funding to accelerate our growth and product development.',
            ],
            [
                'year' => '2022',
                'title' => 'Theme Marketplace',
                'description' => 'We launched our theme marketplace, allowing designers to create and sell themes for our platform.',
            ],
            [
                'year' => '2023',
                'title' => 'Global Expansion',
                'description' => 'We expanded our operations globally, with customers in over 50 countries and support for multiple languages and currencies.',
            ],
        ];

        $values = [
            [
                'title' => 'Customer Success',
                'description' => 'We are committed to the success of our customers. Their success is our success.',
                'icon' => 'user-group',
            ],
            [
                'title' => 'Innovation',
                'description' => 'We continuously innovate to provide the best learning platform for our customers and their students.',
                'icon' => 'light-bulb',
            ],
            [
                'title' => 'Quality',
                'description' => 'We are committed to delivering high-quality software that is reliable, secure, and easy to use.',
                'icon' => 'badge-check',
            ],
            [
                'title' => 'Accessibility',
                'description' => 'We believe that education should be accessible to everyone, regardless of their location or abilities.',
                'icon' => 'globe',
            ],
        ];

        return view('marketing.about', compact('team', 'milestones', 'values'));
    }
}
