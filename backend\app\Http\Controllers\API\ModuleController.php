<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Module;
use App\Models\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ModuleController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $query = Module::query();

        // Filter by tenant if specified
        if ($request->has('tenant_id')) {
            $query->where('tenant_id', $request->tenant_id);
        }

        // If user is a tenant, only show their modules and global modules
        if ($user->role === 'tenant') {
            $query->where(function($q) use ($user) {
                $q->where('tenant_id', $user->tenant_id)
                  ->orWhereNull('tenant_id');
            });
        }

        // Filter by active status if specified
        if ($request->has('is_active')) {
            $query->where('is_active', $request->is_active);
        }

        $modules = $query->get();

        return response()->json([
            'success' => true,
            'data' => $modules
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'version' => 'required|string|max:50',
            'author' => 'nullable|string|max:255',
            'author_url' => 'nullable|url',
            'thumbnail' => 'nullable|image|max:2048',
            'settings' => 'nullable|json',
            'permissions' => 'nullable|json',
            'hooks' => 'nullable|json',
            'dependencies' => 'nullable|json',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();

        // Only admins and tenants can create modules
        if (!in_array($user->role, ['admin', 'tenant'])) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Create the module
        $module = new Module();
        $module->name = $request->name;
        $module->slug = Str::slug($request->name);
        $module->version = $request->version;
        $module->description = $request->description;
        $module->author = $request->author;
        $module->author_url = $request->author_url;

        // Handle thumbnail upload
        if ($request->hasFile('thumbnail')) {
            $path = $request->file('thumbnail')->store('modules/thumbnails', 'public');
            $module->thumbnail = Storage::url($path);
        }

        // Handle JSON fields
        $module->settings = json_decode($request->settings);
        $module->permissions = json_decode($request->permissions);
        $module->hooks = json_decode($request->hooks);
        $module->dependencies = json_decode($request->dependencies);

        // Set tenant ID for tenant users
        if ($user->role === 'tenant') {
            $module->tenant_id = $user->tenant_id;
        }

        $module->save();

        return response()->json([
            'success' => true,
            'message' => 'Module created successfully',
            'data' => $module
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(string $id)
    {
        $module = Module::find($id);

        if (!$module) {
            return response()->json([
                'success' => false,
                'message' => 'Module not found'
            ], 404);
        }

        $user = Auth::user();

        // Check if user has access to this module
        if ($user->role === 'tenant' && $module->tenant_id !== null && $module->tenant_id !== $user->tenant_id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $module
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, string $id)
    {
        $module = Module::find($id);

        if (!$module) {
            return response()->json([
                'success' => false,
                'message' => 'Module not found'
            ], 404);
        }

        $user = Auth::user();

        // Check if user has permission to update this module
        if ($user->role === 'tenant' && $module->tenant_id !== $user->tenant_id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'version' => 'sometimes|required|string|max:50',
            'author' => 'nullable|string|max:255',
            'author_url' => 'nullable|url',
            'thumbnail' => 'nullable|image|max:2048',
            'settings' => 'nullable|json',
            'permissions' => 'nullable|json',
            'hooks' => 'nullable|json',
            'dependencies' => 'nullable|json',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Update the module
        if ($request->has('name')) {
            $module->name = $request->name;
            $module->slug = Str::slug($request->name);
        }

        if ($request->has('version')) $module->version = $request->version;
        if ($request->has('description')) $module->description = $request->description;
        if ($request->has('author')) $module->author = $request->author;
        if ($request->has('author_url')) $module->author_url = $request->author_url;

        // Handle thumbnail upload
        if ($request->hasFile('thumbnail')) {
            // Delete old thumbnail if exists
            if ($module->thumbnail) {
                $oldPath = str_replace('/storage/', '', $module->thumbnail);
                Storage::disk('public')->delete($oldPath);
            }

            $path = $request->file('thumbnail')->store('modules/thumbnails', 'public');
            $module->thumbnail = Storage::url($path);
        }

        // Handle JSON fields
        if ($request->has('settings')) $module->settings = json_decode($request->settings);
        if ($request->has('permissions')) $module->permissions = json_decode($request->permissions);
        if ($request->has('hooks')) $module->hooks = json_decode($request->hooks);
        if ($request->has('dependencies')) $module->dependencies = json_decode($request->dependencies);

        $module->save();

        return response()->json([
            'success' => true,
            'message' => 'Module updated successfully',
            'data' => $module
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(string $id)
    {
        $module = Module::find($id);

        if (!$module) {
            return response()->json([
                'success' => false,
                'message' => 'Module not found'
            ], 404);
        }

        $user = Auth::user();

        // Check if user has permission to delete this module
        if ($user->role !== 'admin' && ($user->role !== 'tenant' || $module->tenant_id !== $user->tenant_id)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Don't allow deleting active modules
        if ($module->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete an active module'
            ], 400);
        }

        // Don't allow deleting system modules
        if ($module->is_system) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete a system module'
            ], 400);
        }

        // Delete thumbnail if exists
        if ($module->thumbnail) {
            $path = str_replace('/storage/', '', $module->thumbnail);
            Storage::disk('public')->delete($path);
        }

        $module->delete();

        return response()->json([
            'success' => true,
            'message' => 'Module deleted successfully'
        ]);
    }

    /**
     * Activate the specified module.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function activate(string $id)
    {
        $module = Module::find($id);

        if (!$module) {
            return response()->json([
                'success' => false,
                'message' => 'Module not found'
            ], 404);
        }

        $user = Auth::user();

        // Check if user has permission to activate this module
        if ($user->role === 'tenant' && $module->tenant_id !== null && $module->tenant_id !== $user->tenant_id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Check dependencies
        if (!$module->checkDependencies()) {
            return response()->json([
                'success' => false,
                'message' => 'Module dependencies are not satisfied'
            ], 400);
        }

        // Activate the module
        $module->activate();

        return response()->json([
            'success' => true,
            'message' => 'Module activated successfully',
            'data' => $module
        ]);
    }

    /**
     * Deactivate the specified module.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deactivate(string $id)
    {
        $module = Module::find($id);

        if (!$module) {
            return response()->json([
                'success' => false,
                'message' => 'Module not found'
            ], 404);
        }

        $user = Auth::user();

        // Check if user has permission to deactivate this module
        if ($user->role === 'tenant' && $module->tenant_id !== $user->tenant_id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Check if other modules depend on this one
        $dependentModules = Module::where('is_active', true)
            ->get()
            ->filter(function($m) use ($module) {
                return !empty($m->dependencies) && isset($m->dependencies[$module->slug]);
            });

        if ($dependentModules->count() > 0) {
            $dependentNames = $dependentModules->pluck('name')->implode(', ');
            return response()->json([
                'success' => false,
                'message' => "Cannot deactivate module because it is required by: {$dependentNames}"
            ], 400);
        }

        // Deactivate the module
        $module->deactivate();

        return response()->json([
            'success' => true,
            'message' => 'Module deactivated successfully',
            'data' => $module
        ]);
    }

    /**
     * Install a module for a tenant.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function install(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'module_id' => 'required|exists:modules,id',
            'tenant_id' => 'required|exists:tenants,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $module = Module::find($request->module_id);
        $tenant = Tenant::find($request->tenant_id);

        // Check if user has permission to install modules for this tenant
        if ($user->role !== 'admin' && ($user->role !== 'tenant' || $user->tenant_id !== $tenant->id)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Check if module is already installed for this tenant
        if ($tenant->installedModules()->where('module_id', $module->id)->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Module is already installed for this tenant'
            ], 400);
        }

        // Install the module for the tenant
        $tenant->installedModules()->attach($module->id, [
            'is_active' => true,
            'settings' => json_encode([])
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Module installed successfully'
        ]);
    }

    /**
     * Uninstall a module from a tenant.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uninstall(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'module_id' => 'required|exists:modules,id',
            'tenant_id' => 'required|exists:tenants,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $module = Module::find($request->module_id);
        $tenant = Tenant::find($request->tenant_id);

        // Check if user has permission to uninstall modules for this tenant
        if ($user->role !== 'admin' && ($user->role !== 'tenant' || $user->tenant_id !== $tenant->id)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Check if module is installed for this tenant
        if (!$tenant->installedModules()->where('module_id', $module->id)->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Module is not installed for this tenant'
            ], 400);
        }

        // Uninstall the module from the tenant
        $tenant->installedModules()->detach($module->id);

        return response()->json([
            'success' => true,
            'message' => 'Module uninstalled successfully'
        ]);
    }

    /**
     * Install a module from the marketplace.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function installFromMarketplace(string $id)
    {
        $module = Module::find($id);

        if (!$module) {
            return response()->json([
                'success' => false,
                'message' => 'Module not found'
            ], 404);
        }

        $user = Auth::user();

        // Only tenants can install modules
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can install modules'
            ], 403);
        }

        // Check if module is approved
        if ($module->status !== 'approved') {
            return response()->json([
                'success' => false,
                'message' => 'This module is not available for installation'
            ], 400);
        }

        // Check if module is free or has been purchased
        if ($module->price > 0) {
            // In a real implementation, you would check if the user has purchased this module
            // For now, we'll just return an error
            return response()->json([
                'success' => false,
                'message' => 'Please purchase this module before installing'
            ], 400);
        }

        // Create a copy of the module for the tenant
        $tenantModule = $module->replicate();
        $tenantModule->tenant_id = $user->tenant_id;
        $tenantModule->is_active = false; // Don't activate by default
        $tenantModule->is_system = false;
        $tenantModule->save();

        // Increment download count
        $module->incrementDownloads();

        return response()->json([
            'success' => true,
            'message' => 'Module installed successfully',
            'data' => $tenantModule
        ]);
    }

    /**
     * Purchase a module from the marketplace.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function purchase(string $id)
    {
        $module = Module::find($id);

        if (!$module) {
            return response()->json([
                'success' => false,
                'message' => 'Module not found'
            ], 404);
        }

        $user = Auth::user();

        // Only tenants can purchase modules
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can purchase modules'
            ], 403);
        }

        // Check if module is approved
        if ($module->status !== 'approved') {
            return response()->json([
                'success' => false,
                'message' => 'This module is not available for purchase'
            ], 400);
        }

        // Check if module is paid
        if ($module->price <= 0) {
            return response()->json([
                'success' => false,
                'message' => 'This module is free and does not require purchase'
            ], 400);
        }

        // In a real implementation, you would process the payment here
        // For now, we'll just create a copy of the module for the tenant

        // Create a copy of the module for the tenant
        $tenantModule = $module->replicate();
        $tenantModule->tenant_id = $user->tenant_id;
        $tenantModule->is_active = false; // Don't activate by default
        $tenantModule->is_system = false;
        $tenantModule->save();

        // Increment download count
        $module->incrementDownloads();

        return response()->json([
            'success' => true,
            'message' => 'Module purchased and installed successfully',
            'data' => $tenantModule
        ]);
    }
}
