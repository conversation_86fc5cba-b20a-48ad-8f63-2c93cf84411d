<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login Information</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-top: 50px;
        }
        h1 {
            color: #ff7700;
            margin-bottom: 20px;
        }
        .credentials {
            background-color: #f8f9fa;
            border-left: 4px solid #ff7700;
            padding: 15px;
            margin-bottom: 20px;
        }
        .credentials p {
            margin: 5px 0;
        }
        .btn {
            display: inline-block;
            background-color: #ff7700;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            font-weight: 500;
            margin-top: 20px;
        }
        .btn:hover {
            background-color: #e66a00;
        }
        .note {
            margin-top: 20px;
            padding: 15px;
            background-color: #e6f7ff;
            border-left: 4px solid #1890ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Admin Login Information</h1>
        <p>Use the following credentials to log in to the admin dashboard:</p>

        <div class="credentials">
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Password:</strong> password</p>
        </div>

        <p>Click the button below to go to the admin login page:</p>

        <a href="{{ route('login') }}" class="btn">Go to Admin Login</a>

        <div class="note">
            <p><strong>Note:</strong> These credentials are for demonstration purposes only. In a production environment, you should use strong, unique passwords and enable additional security measures like two-factor authentication.</p>
        </div>
    </div>
</body>
</html>
