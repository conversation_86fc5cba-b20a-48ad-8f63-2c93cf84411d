# Naxofy LMS Platform

Naxofy is a comprehensive Learning Management System (LMS) platform designed to function similar to Shopify, but for online learning. It allows users (instructors, institutes, academies) to create their own customized learning platforms with branded websites and mobile apps.

## Project Architecture

The project follows a unified architecture with the following components:

1. **Marketing Website**: Public-facing website for potential tenants
2. **Admin Dashboard**: System administration for platform managers
3. **Tenant Dashboard**: Management interface for tenant administrators
4. **API**: RESTful API endpoints for frontend and mobile app
5. **Mobile App**: White-label mobile app for course consumption

## Technology Stack

- **Backend**: Laravel 12+
- **Frontend**: React 18+ with Material UI
- **Mobile**: React Native with Expo
- **Database**: MySQL
- **Authentication**: JWT + Google OAuth
- **Payment Processing**: Razorpay

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/naxofy-lms.git
   cd naxofy-lms
   ```

2. Install PHP dependencies:
   ```bash
   composer install
   ```

3. Create a copy of the environment file:
   ```bash
   cp .env.example .env
   ```

4. Generate an application key:
   ```bash
   php artisan key:generate
   ```

5. Configure the database connection in the `.env` file:
   ```
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=lms
   DB_USERNAME=root
   DB_PASSWORD=your_password
   ```

6. Run database migrations and seed the database:
   ```bash
   php artisan migrate --seed
   ```

7. Create a symbolic link for storage:
   ```bash
   php artisan storage:link
   ```

8. Install JavaScript dependencies and build assets:
   ```bash
   npm install
   npm run build
   ```

9. Start the development server:
   ```bash
   php artisan serve
   ```

## Project Structure

- `app/` - Core application code
  - `Http/Controllers/API/` - API controllers
  - `Http/Controllers/Admin/` - Admin dashboard controllers
  - `Http/Controllers/Marketing/` - Marketing website controllers
  - `Http/Controllers/Tenant/` - Tenant dashboard controllers
  - `Models/` - Eloquent models
  - `Services/` - Service classes
- `resources/` - Frontend resources
  - `views/marketing/` - Marketing website views
  - `views/admin/` - Admin dashboard views
  - `views/tenant/` - Tenant dashboard views
- `routes/` - Route definitions
  - `web.php` - Web routes (marketing website)
  - `api.php` - API routes
  - `admin.php` - Admin dashboard routes
  - `tenant.php` - Tenant dashboard routes
- `database/` - Database migrations and seeders
- `public/` - Publicly accessible assets
- `frontend/` - React frontend for the LMS platform
- `mobile/` - React Native mobile app

## Key Features

- Multi-tenancy system
- Theme customization
- Module/extension system
- White-label mobile app
- Course management
- Student enrollment and progress tracking
- Payment processing
- Analytics and reporting

## API Documentation

API documentation is available at `/docs/api` when running the application.

## Testing

Run the test suite with:

```bash
php artisan test
```

## Contributing

Please see [CONTRIBUTING.md](CONTRIBUTING.md) for details.

## License

The Naxofy LMS Platform is open-sourced software licensed under the [MIT license](LICENSE.md).
