# Deployment Guide

This guide provides instructions for deploying the Naxofy LMS platform to production.

## Prerequisites

- A server with at least 4GB RAM and 2 CPU cores
- Ubuntu 20.04 LTS or later
- Docker and Docker Compose installed
- Domain name(s) configured with DNS
- SSL certificates (Let's Encrypt recommended)

## Architecture Overview

The deployment architecture consists of the following components:

1. **Nginx**: Web server and reverse proxy
2. **PHP-FPM**: PHP FastCGI Process Manager
3. **MySQL**: Database server
4. **Redis**: Cache and session storage
5. **Node.js**: Frontend server (for SSR)
6. **Supervisor**: Process manager for queue workers

## Deployment Options

### Option 1: Manual Deployment

#### 1. Server Setup

```bash
# Update system packages
sudo apt update
sudo apt upgrade -y

# Install required packages
sudo apt install -y nginx mysql-server redis-server supervisor

# Install PHP and extensions
sudo apt install -y php8.1-fpm php8.1-mysql php8.1-mbstring php8.1-xml php8.1-curl php8.1-zip php8.1-gd php8.1-redis

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt install -y nodejs

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

#### 2. Database Setup

```bash
# Secure MySQL installation
sudo mysql_secure_installation

# Create database and user
sudo mysql -e "CREATE DATABASE lms_unified CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
sudo mysql -e "CREATE USER 'lms_user'@'localhost' IDENTIFIED BY 'your_secure_password';"
sudo mysql -e "GRANT ALL PRIVILEGES ON lms_unified.* TO 'lms_user'@'localhost';"
sudo mysql -e "FLUSH PRIVILEGES;"
```

#### 3. Application Deployment

```bash
# Clone the repository
git clone https://github.com/your-org/naxofy-lms.git /var/www/naxofy

# Set permissions
sudo chown -R www-data:www-data /var/www/naxofy
sudo chmod -R 755 /var/www/naxofy/storage /var/www/naxofy/bootstrap/cache

# Install backend dependencies
cd /var/www/naxofy
composer install --no-dev --optimize-autoloader

# Install frontend dependencies
cd /var/www/naxofy/frontend
npm install
npm run build

# Install mobile app dependencies
cd /var/www/naxofy/mobile
npm install
npm run build

# Configure environment
cd /var/www/naxofy
cp .env.example .env
php artisan key:generate
php artisan jwt:secret

# Edit .env file with production settings
nano .env

# Run migrations
php artisan migrate --force

# Seed the database
php artisan db:seed --force

# Optimize the application
php artisan optimize
php artisan route:cache
php artisan config:cache
php artisan view:cache

# Set up storage link
php artisan storage:link
```

#### 4. Nginx Configuration

Create a new Nginx configuration file:

```bash
sudo nano /etc/nginx/sites-available/naxofy
```

Add the following configuration:

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:10m;
    ssl_session_tickets off;
    ssl_stapling on;
    ssl_stapling_verify on;
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload";

    root /var/www/naxofy/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

    location ~* \.(jpg|jpeg|png|gif|ico|css|js|svg)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }
}
```

Enable the site:

```bash
sudo ln -s /etc/nginx/sites-available/naxofy /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

#### 5. SSL Certificate

```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
```

#### 6. Queue Worker Setup

Create a supervisor configuration file:

```bash
sudo nano /etc/supervisor/conf.d/naxofy-worker.conf
```

Add the following configuration:

```ini
[program:naxofy-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/naxofy/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/www/naxofy/storage/logs/worker.log
stopwaitsecs=3600
```

Start the worker:

```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start naxofy-worker:*
```

#### 7. Cron Job Setup

```bash
sudo crontab -e
```

Add the following line:

```
* * * * * cd /var/www/naxofy && php artisan schedule:run >> /dev/null 2>&1
```

### Option 2: Docker Deployment

#### 1. Create Docker Compose File

Create a `docker-compose.yml` file:

```yaml
version: '3'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./:/var/www/html
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - ./docker/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - php
    networks:
      - naxofy

  php:
    build:
      context: ./docker/php
      dockerfile: Dockerfile
    volumes:
      - ./:/var/www/html
    depends_on:
      - mysql
      - redis
    networks:
      - naxofy

  mysql:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      MYSQL_DATABASE: lms_unified
      MYSQL_USER: lms_user
      MYSQL_PASSWORD: your_secure_password
      MYSQL_ROOT_PASSWORD: your_secure_root_password
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - naxofy

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - naxofy

  node:
    image: node:16-alpine
    working_dir: /var/www/html/frontend
    volumes:
      - ./:/var/www/html
    command: sh -c "npm install && npm run build"
    networks:
      - naxofy

  queue:
    build:
      context: ./docker/php
      dockerfile: Dockerfile
    volumes:
      - ./:/var/www/html
    command: php artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
    depends_on:
      - mysql
      - redis
    networks:
      - naxofy

networks:
  naxofy:
    driver: bridge

volumes:
  mysql_data:
  redis_data:
```

#### 2. Create Nginx Configuration

Create a file at `docker/nginx/conf.d/default.conf`:

```nginx
server {
    listen 80;
    server_name localhost;
    root /var/www/html/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass php:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

#### 3. Create PHP Dockerfile

Create a file at `docker/php/Dockerfile`:

```dockerfile
FROM php:8.1-fpm

# Install dependencies
RUN apt-get update && apt-get install -y \
    libzip-dev \
    zip \
    unzip \
    git \
    curl

# Install PHP extensions
RUN docker-php-ext-install pdo_mysql zip

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www/html

# Copy application files
COPY . /var/www/html

# Set permissions
RUN chown -R www-data:www-data /var/www/html/storage /var/www/html/bootstrap/cache

# Install dependencies
RUN composer install --no-dev --optimize-autoloader

# Optimize the application
RUN php artisan optimize
```

#### 4. Deploy with Docker Compose

```bash
# Start the containers
docker-compose up -d

# Run migrations
docker-compose exec php php artisan migrate --force

# Seed the database
docker-compose exec php php artisan db:seed --force

# Set up storage link
docker-compose exec php php artisan storage:link
```

## Post-Deployment Tasks

1. **Verify Application**:
   - Check that the application is accessible
   - Test login and registration
   - Test key functionality

2. **Set Up Monitoring**:
   - Install and configure monitoring tools (e.g., New Relic, Datadog)
   - Set up alerts for critical issues

3. **Set Up Backups**:
   - Configure database backups
   - Configure file system backups
   - Test backup restoration

4. **Set Up CI/CD Pipeline**:
   - Configure automated testing
   - Configure automated deployment

## Troubleshooting

### Common Issues

1. **500 Internal Server Error**:
   - Check PHP error logs: `/var/log/php8.1-fpm.log`
   - Check Laravel logs: `/var/www/naxofy/storage/logs/laravel.log`
   - Check Nginx error logs: `/var/log/nginx/error.log`

2. **Database Connection Issues**:
   - Verify database credentials in `.env`
   - Check MySQL service status: `sudo systemctl status mysql`
   - Check MySQL logs: `/var/log/mysql/error.log`

3. **Permission Issues**:
   - Verify directory permissions: `sudo chown -R www-data:www-data /var/www/naxofy/storage /var/www/naxofy/bootstrap/cache`
   - Verify file permissions: `sudo chmod -R 755 /var/www/naxofy/storage /var/www/naxofy/bootstrap/cache`

4. **Queue Worker Issues**:
   - Check supervisor logs: `sudo supervisorctl status naxofy-worker:*`
   - Check worker logs: `/var/www/naxofy/storage/logs/worker.log`

## Maintenance

### Regular Maintenance Tasks

1. **Update Dependencies**:
   ```bash
   composer update --no-dev
   npm update
   ```

2. **Update SSL Certificates**:
   ```bash
   sudo certbot renew
   ```

3. **Monitor Disk Space**:
   ```bash
   df -h
   ```

4. **Monitor Database Size**:
   ```bash
   sudo mysql -e "SELECT table_schema AS 'Database', ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)' FROM information_schema.TABLES GROUP BY table_schema;"
   ```

5. **Check for Security Updates**:
   ```bash
   sudo apt update
   sudo apt list --upgradable
   ```

### Scaling Considerations

1. **Horizontal Scaling**:
   - Add more web servers behind a load balancer
   - Configure session sharing with Redis

2. **Vertical Scaling**:
   - Increase server resources (CPU, RAM)
   - Optimize database queries and indexes

3. **Database Scaling**:
   - Implement read replicas
   - Consider sharding for very large datasets

4. **Caching**:
   - Implement page caching
   - Implement object caching
   - Use CDN for static assets
