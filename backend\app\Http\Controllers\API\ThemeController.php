<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Theme;
use App\Models\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ThemeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $query = Theme::query();

        // Filter by tenant if specified
        if ($request->has('tenant_id')) {
            $query->where('tenant_id', $request->tenant_id);
        }

        // If user is a tenant, only show their themes and global themes
        if ($user->role === 'tenant') {
            $query->where(function($q) use ($user) {
                $q->where('tenant_id', $user->tenant_id)
                  ->orWhereNull('tenant_id');
            });
        }

        // Filter by active status if specified
        if ($request->has('is_active')) {
            $query->where('is_active', $request->is_active);
        }

        $themes = $query->get();

        return response()->json([
            'success' => true,
            'data' => $themes
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'version' => 'required|string|max:50',
            'author' => 'nullable|string|max:255',
            'author_url' => 'nullable|url',
            'thumbnail' => 'nullable|image|max:2048',
            'settings' => 'nullable|json',
            'colors' => 'nullable|json',
            'fonts' => 'nullable|json',
            'components' => 'nullable|json',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();

        // Only admins and tenants can create themes
        if (!in_array($user->role, ['admin', 'tenant'])) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Create the theme
        $theme = new Theme();
        $theme->name = $request->name;
        $theme->slug = Str::slug($request->name);
        $theme->version = $request->version;
        $theme->description = $request->description;
        $theme->author = $request->author;
        $theme->author_url = $request->author_url;

        // Handle thumbnail upload
        if ($request->hasFile('thumbnail')) {
            $path = $request->file('thumbnail')->store('themes/thumbnails', 'public');
            $theme->thumbnail = Storage::url($path);
        }

        // Handle JSON fields
        $theme->settings = json_decode($request->settings);
        $theme->colors = json_decode($request->colors);
        $theme->fonts = json_decode($request->fonts);
        $theme->components = json_decode($request->components);

        // Set tenant ID for tenant users
        if ($user->role === 'tenant') {
            $theme->tenant_id = $user->tenant_id;
        }

        $theme->save();

        return response()->json([
            'success' => true,
            'message' => 'Theme created successfully',
            'data' => $theme
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(string $id)
    {
        $theme = Theme::find($id);

        if (!$theme) {
            return response()->json([
                'success' => false,
                'message' => 'Theme not found'
            ], 404);
        }

        $user = Auth::user();

        // Check if user has access to this theme
        if ($user->role === 'tenant' && $theme->tenant_id !== null && $theme->tenant_id !== $user->tenant_id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $theme
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, string $id)
    {
        $theme = Theme::find($id);

        if (!$theme) {
            return response()->json([
                'success' => false,
                'message' => 'Theme not found'
            ], 404);
        }

        $user = Auth::user();

        // Check if user has permission to update this theme
        if ($user->role === 'tenant' && $theme->tenant_id !== $user->tenant_id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'version' => 'sometimes|required|string|max:50',
            'author' => 'nullable|string|max:255',
            'author_url' => 'nullable|url',
            'thumbnail' => 'nullable|image|max:2048',
            'settings' => 'nullable|json',
            'colors' => 'nullable|json',
            'fonts' => 'nullable|json',
            'components' => 'nullable|json',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Update the theme
        if ($request->has('name')) {
            $theme->name = $request->name;
            $theme->slug = Str::slug($request->name);
        }

        if ($request->has('version')) $theme->version = $request->version;
        if ($request->has('description')) $theme->description = $request->description;
        if ($request->has('author')) $theme->author = $request->author;
        if ($request->has('author_url')) $theme->author_url = $request->author_url;

        // Handle thumbnail upload
        if ($request->hasFile('thumbnail')) {
            // Delete old thumbnail if exists
            if ($theme->thumbnail) {
                $oldPath = str_replace('/storage/', '', $theme->thumbnail);
                Storage::disk('public')->delete($oldPath);
            }

            $path = $request->file('thumbnail')->store('themes/thumbnails', 'public');
            $theme->thumbnail = Storage::url($path);
        }

        // Handle JSON fields
        if ($request->has('settings')) $theme->settings = json_decode($request->settings);
        if ($request->has('colors')) $theme->colors = json_decode($request->colors);
        if ($request->has('fonts')) $theme->fonts = json_decode($request->fonts);
        if ($request->has('components')) $theme->components = json_decode($request->components);

        $theme->save();

        return response()->json([
            'success' => true,
            'message' => 'Theme updated successfully',
            'data' => $theme
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(string $id)
    {
        $theme = Theme::find($id);

        if (!$theme) {
            return response()->json([
                'success' => false,
                'message' => 'Theme not found'
            ], 404);
        }

        $user = Auth::user();

        // Check if user has permission to delete this theme
        if ($user->role !== 'admin' && ($user->role !== 'tenant' || $theme->tenant_id !== $user->tenant_id)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Don't allow deleting active themes
        if ($theme->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete an active theme'
            ], 400);
        }

        // Don't allow deleting system themes
        if ($theme->is_system) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete a system theme'
            ], 400);
        }

        // Delete thumbnail if exists
        if ($theme->thumbnail) {
            $path = str_replace('/storage/', '', $theme->thumbnail);
            Storage::disk('public')->delete($path);
        }

        $theme->delete();

        return response()->json([
            'success' => true,
            'message' => 'Theme deleted successfully'
        ]);
    }

    /**
     * Activate the specified theme.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function activate(string $id)
    {
        $theme = Theme::find($id);

        if (!$theme) {
            return response()->json([
                'success' => false,
                'message' => 'Theme not found'
            ], 404);
        }

        $user = Auth::user();

        // Check if user has permission to activate this theme
        if ($user->role === 'tenant' && $theme->tenant_id !== null && $theme->tenant_id !== $user->tenant_id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Activate the theme
        $theme->activate();

        return response()->json([
            'success' => true,
            'message' => 'Theme activated successfully',
            'data' => $theme
        ]);
    }

    /**
     * Deactivate the specified theme.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deactivate(string $id)
    {
        $theme = Theme::find($id);

        if (!$theme) {
            return response()->json([
                'success' => false,
                'message' => 'Theme not found'
            ], 404);
        }

        $user = Auth::user();

        // Check if user has permission to deactivate this theme
        if ($user->role === 'tenant' && $theme->tenant_id !== $user->tenant_id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Don't allow deactivating the only active theme
        $activeThemesCount = Theme::where('is_active', true)
            ->where(function($query) use ($theme) {
                if ($theme->tenant_id) {
                    $query->where('tenant_id', $theme->tenant_id);
                } else {
                    $query->whereNull('tenant_id');
                }
            })
            ->count();

        if ($activeThemesCount <= 1 && $theme->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot deactivate the only active theme'
            ], 400);
        }

        // Deactivate the theme
        $theme->deactivate();

        return response()->json([
            'success' => true,
            'message' => 'Theme deactivated successfully',
            'data' => $theme
        ]);
    }

    /**
     * Install a theme from the marketplace.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function install(string $id)
    {
        $theme = Theme::find($id);

        if (!$theme) {
            return response()->json([
                'success' => false,
                'message' => 'Theme not found'
            ], 404);
        }

        $user = Auth::user();

        // Only tenants can install themes
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can install themes'
            ], 403);
        }

        // Check if theme is approved
        if ($theme->status !== 'approved') {
            return response()->json([
                'success' => false,
                'message' => 'This theme is not available for installation'
            ], 400);
        }

        // Check if theme is free or has been purchased
        if ($theme->price > 0) {
            // In a real implementation, you would check if the user has purchased this theme
            // For now, we'll just return an error
            return response()->json([
                'success' => false,
                'message' => 'Please purchase this theme before installing'
            ], 400);
        }

        // Create a copy of the theme for the tenant
        $tenantTheme = $theme->replicate();
        $tenantTheme->tenant_id = $user->tenant_id;
        $tenantTheme->is_active = false; // Don't activate by default
        $tenantTheme->is_system = false;
        $tenantTheme->save();

        // Increment download count
        $theme->incrementDownloads();

        return response()->json([
            'success' => true,
            'message' => 'Theme installed successfully',
            'data' => $tenantTheme
        ]);
    }

    /**
     * Purchase a theme from the marketplace.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function purchase(string $id)
    {
        $theme = Theme::find($id);

        if (!$theme) {
            return response()->json([
                'success' => false,
                'message' => 'Theme not found'
            ], 404);
        }

        $user = Auth::user();

        // Only tenants can purchase themes
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can purchase themes'
            ], 403);
        }

        // Check if theme is approved
        if ($theme->status !== 'approved') {
            return response()->json([
                'success' => false,
                'message' => 'This theme is not available for purchase'
            ], 400);
        }

        // Check if theme is paid
        if ($theme->price <= 0) {
            return response()->json([
                'success' => false,
                'message' => 'This theme is free and does not require purchase'
            ], 400);
        }

        // In a real implementation, you would process the payment here
        // For now, we'll just create a copy of the theme for the tenant

        // Create a copy of the theme for the tenant
        $tenantTheme = $theme->replicate();
        $tenantTheme->tenant_id = $user->tenant_id;
        $tenantTheme->is_active = false; // Don't activate by default
        $tenantTheme->is_system = false;
        $tenantTheme->save();

        // Increment download count
        $theme->incrementDownloads();

        return response()->json([
            'success' => true,
            'message' => 'Theme purchased and installed successfully',
            'data' => $tenantTheme
        ]);
    }
}
