<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Enrollment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class CourseController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        // Authentication is handled at the route level
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Course::query();

        // Filter by tenant if provided
        if ($request->has('tenant_id')) {
            $query->where('tenant_id', $request->tenant_id);
        }

        // Search by title or description
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Sort by price or created_at
        if ($request->has('sort')) {
            $sortField = $request->sort;
            $sortDirection = $request->has('direction') ? $request->direction : 'asc';
            $query->orderBy($sortField, $sortDirection);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $courses = $query->with(['instructor:id,name', 'tenant:id,name'])->paginate(10);

        return response()->json($courses);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // Validate request
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 400);
        }

        // Get authenticated user
        $user = Auth::user();

        // Check if user is a tenant or admin
        if ($user->role !== 'tenant' && $user->role !== 'admin') {
            return response()->json(['error' => 'Unauthorized. Only instructors can create courses.'], 403);
        }

        // Handle thumbnail upload
        $thumbnailPath = null;
        if ($request->hasFile('thumbnail')) {
            $thumbnailPath = $request->file('thumbnail')->store('thumbnails', 's3');
        }

        // Create course
        $course = Course::create([
            'title' => $request->title,
            'description' => $request->description,
            'price' => $request->price,
            'tenant_id' => $user->tenant_id,
            'instructor_id' => $user->id,
            'thumbnail' => $thumbnailPath,
        ]);

        return response()->json([
            'message' => 'Course created successfully',
            'course' => $course
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(string $id)
    {
        $course = Course::with([
            'instructor:id,name,email',
            'tenant:id,name,domain',
            'lessons',
            'resources',
            'quizzes',
        ])->findOrFail($id);

        // Check if user is enrolled
        $isEnrolled = false;
        $enrollment = null;

        if (Auth::check()) {
            $user = Auth::user();
            $enrollment = Enrollment::where('user_id', $user->id)
                ->where('course_id', $id)
                ->first();

            $isEnrolled = $enrollment !== null;
        }

        return response()->json([
            'course' => $course,
            'is_enrolled' => $isEnrolled,
            'enrollment' => $enrollment,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, string $id)
    {
        // Find course
        $course = Course::findOrFail($id);

        // Check if user is authorized to update the course
        $user = Auth::user();
        if ($user->role !== 'admin' && ($user->role !== 'tenant' || $course->instructor_id !== $user->id)) {
            return response()->json(['error' => 'Unauthorized. Only the instructor or admin can update this course.'], 403);
        }

        // Validate request
        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|required|string|max:255',
            'description' => 'sometimes|required|string',
            'price' => 'sometimes|required|numeric|min:0',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 400);
        }

        // Handle thumbnail upload
        if ($request->hasFile('thumbnail')) {
            // Delete old thumbnail if exists
            if ($course->thumbnail) {
                Storage::disk('s3')->delete($course->thumbnail);
            }

            $thumbnailPath = $request->file('thumbnail')->store('thumbnails', 's3');
            $course->thumbnail = $thumbnailPath;
        }

        // Update course
        if ($request->has('title')) {
            $course->title = $request->title;
        }

        if ($request->has('description')) {
            $course->description = $request->description;
        }

        if ($request->has('price')) {
            $course->price = $request->price;
        }

        $course->save();

        return response()->json([
            'message' => 'Course updated successfully',
            'course' => $course
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(string $id)
    {
        // Find course
        $course = Course::findOrFail($id);

        // Check if user is authorized to delete the course
        $user = Auth::user();
        if ($user->role !== 'admin' && ($user->role !== 'tenant' || $course->instructor_id !== $user->id)) {
            return response()->json(['error' => 'Unauthorized. Only the instructor or admin can delete this course.'], 403);
        }

        // Delete thumbnail if exists
        if ($course->thumbnail) {
            Storage::disk('s3')->delete($course->thumbnail);
        }

        // Delete course
        $course->delete();

        return response()->json(['message' => 'Course deleted successfully']);
    }

    /**
     * Enroll a user in a course.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function enroll(string $id)
    {
        // Find course
        $course = Course::findOrFail($id);

        // Get authenticated user
        $user = Auth::user();

        // Check if user is already enrolled
        $enrollment = Enrollment::where('user_id', $user->id)
            ->where('course_id', $id)
            ->first();

        if ($enrollment) {
            return response()->json(['message' => 'User already enrolled in this course']);
        }

        // Create enrollment
        $enrollment = Enrollment::create([
            'user_id' => $user->id,
            'course_id' => $id,
            'progress' => 0,
            'payment_status' => $course->price > 0 ? 'pending' : 'completed',
        ]);

        return response()->json([
            'message' => 'User enrolled successfully',
            'enrollment' => $enrollment
        ], 201);
    }

    /**
     * Update user progress in a course.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateProgress(Request $request, string $id)
    {
        // Validate request
        $validator = Validator::make($request->all(), [
            'progress' => 'required|integer|min:0|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 400);
        }

        // Get authenticated user
        $user = Auth::user();

        // Find enrollment
        $enrollment = Enrollment::where('user_id', $user->id)
            ->where('course_id', $id)
            ->firstOrFail();

        // Update progress
        $enrollment->progress = $request->progress;
        $enrollment->save();

        return response()->json([
            'message' => 'Progress updated successfully',
            'enrollment' => $enrollment
        ]);
    }
}
