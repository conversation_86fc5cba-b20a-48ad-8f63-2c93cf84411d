<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ContactController extends Controller
{
    /**
     * Display the contact page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $offices = [
            [
                'city' => 'San Francisco',
                'address' => '123 Market Street, Suite 456, San Francisco, CA 94105',
                'phone' => '+****************',
                'email' => '<EMAIL>',
                'image' => 'https://source.unsplash.com/random/800x600/?san-francisco',
            ],
            [
                'city' => 'New York',
                'address' => '789 Broadway, 10th Floor, New York, NY 10003',
                'phone' => '+****************',
                'email' => '<EMAIL>',
                'image' => 'https://source.unsplash.com/random/800x600/?new-york',
            ],
            [
                'city' => 'London',
                'address' => '10 Finsbury Square, London EC2A 1AF, United Kingdom',
                'phone' => '+44 20 7123 4567',
                'email' => '<EMAIL>',
                'image' => 'https://source.unsplash.com/random/800x600/?london',
            ],
        ];

        $departments = [
            [
                'name' => 'Sales',
                'email' => '<EMAIL>',
                'description' => 'For questions about pricing, plans, and features.',
            ],
            [
                'name' => 'Support',
                'email' => '<EMAIL>',
                'description' => 'For technical support and assistance with your account.',
            ],
            [
                'name' => 'Partnerships',
                'email' => '<EMAIL>',
                'description' => 'For partnership opportunities and integrations.',
            ],
            [
                'name' => 'Press',
                'email' => '<EMAIL>',
                'description' => 'For media inquiries and press releases.',
            ],
        ];

        return view('marketing.contact', compact('offices', 'departments'));
    }

    /**
     * Process the contact form.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        // Validate the request
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255',
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',
            'department' => 'required|string|in:Sales,Support,Partnerships,Press',
            'message' => 'required|string',
        ]);

        // Here you would send the email or store the contact request
        // This is just a placeholder for now

        // Redirect back with success message
        return redirect()->route('contact')->with('success', 'Your message has been sent successfully! We will get back to you soon.');
    }
}
