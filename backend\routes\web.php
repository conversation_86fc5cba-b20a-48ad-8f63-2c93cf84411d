<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\AppBuildController;
use App\Http\Controllers\API\AppConfigurationController;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\CourseController;
use App\Http\Controllers\API\DeveloperAccountController;
use App\Http\Controllers\API\DeveloperAnalyticController;
use App\Http\Controllers\API\DeveloperSubmissionController;
use App\Http\Controllers\API\LessonController;
use App\Http\Controllers\API\MarketplaceController;
use App\Http\Controllers\API\MarketplaceReviewController;
use App\Http\Controllers\API\ModuleController;
use App\Http\Controllers\API\PageBuilderController;
use App\Http\Controllers\API\PaymentController;
use App\Http\Controllers\API\RegisterController;
use App\Http\Controllers\API\TenantController;
use App\Http\Controllers\API\ThemeController;
use App\Http\Controllers\API\ThemeCustomizationController;
use App\Http\Controllers\API\ThemeTemplateController;
use App\Http\Controllers\Auth\GoogleController;

Route::get('/', function () {
    return view('welcome');
});

// Direct registration route
Route::post('/api/register', [RegisterController::class, 'register']);

// API Routes
Route::prefix('api')->middleware('api')->group(function () {
    // Auth routes
    Route::prefix('auth')->group(function () {
        Route::post('/login', [AuthController::class, 'login']);
        Route::post('/register', [RegisterController::class, 'register']);
        Route::post('/register-tenant', [AuthController::class, 'registerTenant']);
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::post('/refresh', [AuthController::class, 'refresh']);
        Route::get('/profile', [AuthController::class, 'userProfile']);

        // Google OAuth routes
        Route::get('/google', [GoogleController::class, 'redirectToGoogle']);
        Route::get('/google/callback', [GoogleController::class, 'handleGoogleCallback']);
    });

    // Course routes
    Route::apiResource('courses', CourseController::class);
    Route::post('/courses/{id}/enroll', [CourseController::class, 'enroll']);
    Route::post('/courses/{id}/progress', [CourseController::class, 'updateProgress']);

    // Lesson routes
    Route::apiResource('lessons', LessonController::class);

    // Tenant routes
    Route::apiResource('tenants', TenantController::class);
    Route::post('/tenants/{id}/approve', [TenantController::class, 'approve']);

    // Payment routes
    Route::post('/payments/create-order', [PaymentController::class, 'createOrder']);
    Route::post('/payments/verify', [PaymentController::class, 'verifyPayment']);
    Route::get('/payments/history', [PaymentController::class, 'history']);

    // Theme routes
    Route::apiResource('themes', ThemeController::class);
    Route::post('/themes/{id}/activate', [ThemeController::class, 'activate']);
    Route::post('/themes/{id}/deactivate', [ThemeController::class, 'deactivate']);
    Route::post('/themes/{id}/install', [ThemeController::class, 'install']);
    Route::post('/themes/{id}/purchase', [ThemeController::class, 'purchase']);

    // Module routes
    Route::apiResource('modules', ModuleController::class);
    Route::post('/modules/{id}/activate', [ModuleController::class, 'activate']);
    Route::post('/modules/{id}/deactivate', [ModuleController::class, 'deactivate']);
    Route::post('/modules/install', [ModuleController::class, 'install']);
    Route::post('/modules/uninstall', [ModuleController::class, 'uninstall']);
    Route::post('/modules/{id}/install-marketplace', [ModuleController::class, 'installFromMarketplace']);
    Route::post('/modules/{id}/purchase', [ModuleController::class, 'purchase']);

    // Marketplace routes
    Route::prefix('marketplace')->group(function () {
        Route::get('/featured', [MarketplaceController::class, 'featured']);
        Route::get('/categories', [MarketplaceController::class, 'categories']);
        Route::get('/search', [MarketplaceController::class, 'search']);
        Route::get('/themes', [MarketplaceController::class, 'themes']);
        Route::get('/modules', [MarketplaceController::class, 'modules']);

        // Reviews routes
        Route::get('/reviews', [MarketplaceReviewController::class, 'index']);
        Route::middleware('auth:api')->group(function () {
            Route::post('/reviews', [MarketplaceReviewController::class, 'store']);
            Route::put('/reviews/{id}', [MarketplaceReviewController::class, 'update']);
            Route::delete('/reviews/{id}', [MarketplaceReviewController::class, 'destroy']);
        });

        // Admin-only review management routes
        Route::middleware(['auth:api', 'role:admin'])->group(function () {
            Route::post('/reviews/{id}/approve', [MarketplaceReviewController::class, 'approve']);
            Route::post('/reviews/{id}/reject', [MarketplaceReviewController::class, 'reject']);
        });
    });

    // Developer Portal routes
    Route::prefix('developer')->middleware('auth:api')->group(function () {
        // Developer Account routes
        Route::get('/account', [DeveloperAccountController::class, 'show']);
        Route::post('/account/register', [DeveloperAccountController::class, 'register']);
        Route::put('/account', [DeveloperAccountController::class, 'update']);
        Route::post('/account/regenerate-api-credentials', [DeveloperAccountController::class, 'regenerateApiCredentials']);

        // Developer Submission routes
        Route::get('/submissions', [DeveloperSubmissionController::class, 'index']);
        Route::post('/submissions', [DeveloperSubmissionController::class, 'store']);
        Route::get('/submissions/{id}', [DeveloperSubmissionController::class, 'show']);
        Route::put('/submissions/{id}', [DeveloperSubmissionController::class, 'update']);
        Route::post('/submissions/{id}/submit', [DeveloperSubmissionController::class, 'submit']);

        // Developer Analytics routes
        Route::get('/analytics', [DeveloperAnalyticController::class, 'index']);
        Route::get('/analytics/revenue', [DeveloperAnalyticController::class, 'revenueReport']);

        // Admin-only routes
        Route::middleware('role:admin')->group(function () {
            Route::get('/accounts', [DeveloperAccountController::class, 'index']);
            Route::post('/accounts/{id}/approve', [DeveloperAccountController::class, 'approve']);
            Route::post('/accounts/{id}/reject', [DeveloperAccountController::class, 'reject']);
            Route::post('/accounts/{id}/suspend', [DeveloperAccountController::class, 'suspend']);

            Route::get('/all-submissions', [DeveloperSubmissionController::class, 'adminIndex']);
            Route::post('/submissions/{id}/review', [DeveloperSubmissionController::class, 'review']);
        });
    });

    // Analytics tracking endpoint (no auth required)
    Route::post('/analytics/track', [DeveloperAnalyticController::class, 'track']);

    // White-label Mobile Apps routes
    Route::prefix('app-builder')->middleware('auth:api')->group(function () {
        // App Configuration routes
        Route::get('/configuration', [AppConfigurationController::class, 'show']);
        Route::post('/configuration', [AppConfigurationController::class, 'store']);
        Route::get('/configuration/expo-config', [AppConfigurationController::class, 'generateExpoConfig']);

        // App Build routes
        Route::get('/builds', [AppBuildController::class, 'index']);
        Route::post('/builds', [AppBuildController::class, 'store']);
        Route::get('/builds/{id}', [AppBuildController::class, 'show']);

        // Admin-only routes
        Route::middleware('role:admin')->group(function () {
            Route::get('/configurations', [AppConfigurationController::class, 'index']);
            Route::get('/configurations/{id}', [AppConfigurationController::class, 'adminShow']);
            Route::get('/all-builds', [AppBuildController::class, 'adminIndex']);
            Route::get('/all-builds/{id}', [AppBuildController::class, 'adminShow']);
        });
    });

    // Advanced Customization Tools routes
    Route::middleware('auth:api')->group(function () {
        // Theme Templates routes
        Route::get('/themes/{themeId}/templates', [ThemeTemplateController::class, 'index']);
        Route::post('/themes/{themeId}/templates', [ThemeTemplateController::class, 'store']);
        Route::get('/themes/{themeId}/templates/{id}', [ThemeTemplateController::class, 'show']);
        Route::put('/themes/{themeId}/templates/{id}', [ThemeTemplateController::class, 'update']);
        Route::delete('/themes/{themeId}/templates/{id}', [ThemeTemplateController::class, 'destroy']);

        // Theme Customizations routes
        Route::get('/customizations', [ThemeCustomizationController::class, 'index']);
        Route::post('/customizations', [ThemeCustomizationController::class, 'store']);
        Route::get('/customizations/{id}', [ThemeCustomizationController::class, 'show']);
        Route::put('/customizations/{id}', [ThemeCustomizationController::class, 'update']);
        Route::delete('/customizations/{id}', [ThemeCustomizationController::class, 'destroy']);
        Route::post('/customizations/{id}/reset', [ThemeCustomizationController::class, 'reset']);
        Route::post('/customizations/create-from-template', [ThemeCustomizationController::class, 'createFromTemplate']);

        // Page Builder routes
        Route::get('/pages', [PageBuilderController::class, 'index']);
        Route::post('/pages', [PageBuilderController::class, 'store']);
        Route::get('/pages/{id}', [PageBuilderController::class, 'show']);
        Route::put('/pages/{id}', [PageBuilderController::class, 'update']);
        Route::delete('/pages/{id}', [PageBuilderController::class, 'destroy']);
        Route::post('/pages/{id}/publish', [PageBuilderController::class, 'publish']);
        Route::post('/pages/{id}/unpublish', [PageBuilderController::class, 'unpublish']);
        Route::post('/pages/{id}/set-as-homepage', [PageBuilderController::class, 'setAsHomepage']);
        Route::get('/homepage', [PageBuilderController::class, 'getHomepage']);
    });

    // Test route
    Route::get('/test', function () {
        return response()->json(['message' => 'API is working!']);
    });
});
