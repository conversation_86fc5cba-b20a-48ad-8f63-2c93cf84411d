<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class DeveloperAccount extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'company_name',
        'website',
        'bio',
        'logo',
        'api_key',
        'api_secret',
        'is_verified',
        'has_accepted_agreement',
        'agreement_accepted_at',
        'status',
        'rejection_reason',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_verified' => 'boolean',
        'has_accepted_agreement' => 'boolean',
        'agreement_accepted_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'api_secret',
    ];

    /**
     * Get the user that owns the developer account.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the submissions for the developer account.
     */
    public function submissions(): HasMany
    {
        return $this->hasMany(DeveloperSubmission::class);
    }

    /**
     * Get the analytics for the developer account.
     */
    public function analytics(): HasMany
    {
        return $this->hasMany(DeveloperAnalytic::class);
    }

    /**
     * Generate a new API key and secret for the developer account.
     */
    public function generateApiCredentials(): void
    {
        $this->api_key = Str::random(32);
        $this->api_secret = Str::random(64);
        $this->save();
    }

    /**
     * Scope a query to only include verified developer accounts.
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope a query to only include approved developer accounts.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }
}
