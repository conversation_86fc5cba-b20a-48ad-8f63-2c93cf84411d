import { useEffect, useContext } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { CircularProgress, Box, Typography } from '@mui/material';
import { AuthContext } from '../context/AuthContext';
import { authService } from '../services/api';

const GoogleCallback = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { setToken, setUser } = useContext(AuthContext);

  useEffect(() => {
    const processGoogleCallback = async () => {
      try {
        // Get token from URL query params
        const params = new URLSearchParams(location.search);
        const token = params.get('token');

        if (!token) {
          throw new Error('No token received from Google authentication');
        }

        // Store token in local storage
        localStorage.setItem('token', token);
        setToken(token);

        // Fetch user profile with the token
        const response = await authService.getProfile();
        const userData = response.data;
        setUser(userData);

        // Redirect to dashboard
        navigate('/dashboard');
      } catch (error) {
        console.error('Google authentication error:', error);
        navigate('/login?error=google_auth_failed');
      }
    };

    processGoogleCallback();
  }, [location, navigate, setToken, setUser]);

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', minHeight: '60vh' }}>
      <CircularProgress size={60} />
      <Typography variant="h6" sx={{ mt: 4 }}>
        Processing your Google login...
      </Typography>
    </Box>
  );
};

export default GoogleCallback;
