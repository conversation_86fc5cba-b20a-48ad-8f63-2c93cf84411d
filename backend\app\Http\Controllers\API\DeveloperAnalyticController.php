<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\DeveloperAccount;
use App\Models\DeveloperAnalytic;
use App\Models\DeveloperSubmission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class DeveloperAnalyticController extends Controller
{
    /**
     * Display analytics for the authenticated developer.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'type' => 'nullable|in:theme,module',
            'event_type' => 'nullable|in:view,install,purchase,uninstall,review',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'submission_id' => 'nullable|exists:developer_submissions,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $developerAccount = DeveloperAccount::where('user_id', $user->id)->first();

        if (!$developerAccount) {
            return response()->json([
                'success' => false,
                'message' => 'Developer account not found'
            ], 404);
        }

        // Build query
        $query = DeveloperAnalytic::where('developer_account_id', $developerAccount->id);

        if ($request->has('type')) {
            $query->where('type', $request->input('type'));
        }

        if ($request->has('event_type')) {
            $query->where('event_type', $request->input('event_type'));
        }

        if ($request->has('submission_id')) {
            $query->where('submission_id', $request->input('submission_id'));
        }

        // Date filtering
        $startDate = $request->input('start_date', Carbon::now()->subDays(30)->startOfDay());
        $endDate = $request->input('end_date', Carbon::now()->endOfDay());
        $query->whereBetween('created_at', [$startDate, $endDate]);

        // Get analytics
        $analytics = $query->get();

        // Calculate summary statistics
        $summary = [
            'total_views' => $analytics->where('event_type', 'view')->count(),
            'total_installs' => $analytics->where('event_type', 'install')->count(),
            'total_purchases' => $analytics->where('event_type', 'purchase')->count(),
            'total_uninstalls' => $analytics->where('event_type', 'uninstall')->count(),
            'total_reviews' => $analytics->where('event_type', 'review')->count(),
            'total_revenue' => $analytics->sum('revenue'),
        ];

        // Group by date for chart data
        $chartData = $analytics->groupBy(function ($item) {
            return Carbon::parse($item->created_at)->format('Y-m-d');
        })->map(function ($items) {
            return [
                'views' => $items->where('event_type', 'view')->count(),
                'installs' => $items->where('event_type', 'install')->count(),
                'purchases' => $items->where('event_type', 'purchase')->count(),
                'uninstalls' => $items->where('event_type', 'uninstall')->count(),
                'reviews' => $items->where('event_type', 'review')->count(),
                'revenue' => $items->sum('revenue'),
            ];
        });

        return response()->json([
            'success' => true,
            'data' => [
                'summary' => $summary,
                'chart_data' => $chartData,
                'analytics' => $analytics,
            ]
        ]);
    }

    /**
     * Track an analytic event.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function track(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|in:theme,module',
            'event_type' => 'required|in:view,install,purchase,uninstall,review',
            'submission_id' => 'required|exists:developer_submissions,id',
            'revenue' => 'nullable|numeric|min:0',
            'metadata' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Get submission and developer account
        $submission = DeveloperSubmission::find($request->input('submission_id'));
        if (!$submission) {
            return response()->json([
                'success' => false,
                'message' => 'Submission not found'
            ], 404);
        }

        // Create analytic
        $analytic = new DeveloperAnalytic();
        $analytic->developer_account_id = $submission->developer_account_id;
        $analytic->submission_id = $submission->id;
        $analytic->type = $request->input('type');
        $analytic->event_type = $request->input('event_type');
        $analytic->revenue = $request->input('revenue', 0);
        $analytic->metadata = $request->input('metadata');
        $analytic->save();

        return response()->json([
            'success' => true,
            'message' => 'Analytic tracked successfully',
            'data' => $analytic
        ], 201);
    }

    /**
     * Get revenue report for the authenticated developer.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function revenueReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'group_by' => 'nullable|in:day,week,month',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $developerAccount = DeveloperAccount::where('user_id', $user->id)->first();

        if (!$developerAccount) {
            return response()->json([
                'success' => false,
                'message' => 'Developer account not found'
            ], 404);
        }

        // Date filtering
        $startDate = $request->input('start_date', Carbon::now()->subDays(30)->startOfDay());
        $endDate = $request->input('end_date', Carbon::now()->endOfDay());
        $groupBy = $request->input('group_by', 'day');

        // Get revenue analytics
        $analytics = DeveloperAnalytic::where('developer_account_id', $developerAccount->id)
            ->where('event_type', 'purchase')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        // Group by date format based on group_by parameter
        $dateFormat = $groupBy === 'day' ? 'Y-m-d' : ($groupBy === 'week' ? 'Y-W' : 'Y-m');
        
        // Group by date for chart data
        $chartData = $analytics->groupBy(function ($item) use ($dateFormat) {
            return Carbon::parse($item->created_at)->format($dateFormat);
        })->map(function ($items) {
            return [
                'count' => $items->count(),
                'revenue' => $items->sum('revenue'),
            ];
        });

        // Calculate summary statistics
        $summary = [
            'total_purchases' => $analytics->count(),
            'total_revenue' => $analytics->sum('revenue'),
            'average_revenue_per_purchase' => $analytics->count() > 0 ? $analytics->sum('revenue') / $analytics->count() : 0,
        ];

        // Get top items by revenue
        $topItems = $analytics->groupBy('submission_id')
            ->map(function ($items, $submissionId) {
                $submission = DeveloperSubmission::find($submissionId);
                return [
                    'submission_id' => $submissionId,
                    'name' => $submission ? $submission->name : 'Unknown',
                    'type' => $submission ? $submission->type : 'Unknown',
                    'count' => $items->count(),
                    'revenue' => $items->sum('revenue'),
                ];
            })
            ->sortByDesc('revenue')
            ->values()
            ->take(5);

        return response()->json([
            'success' => true,
            'data' => [
                'summary' => $summary,
                'chart_data' => $chartData,
                'top_items' => $topItems,
            ]
        ]);
    }
}
