<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Theme extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'version',
        'description',
        'author',
        'author_url',
        'thumbnail',
        'settings',
        'colors',
        'fonts',
        'components',
        'is_active',
        'is_system',
        'tenant_id',
        'category_id',
        'price',
        'downloads',
        'is_featured',
        'screenshots',
        'demo_url',
        'status',
        'rejection_reason',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'settings' => 'array',
        'colors' => 'array',
        'fonts' => 'array',
        'components' => 'array',
        'screenshots' => 'array',
        'is_active' => 'boolean',
        'is_system' => 'boolean',
        'is_featured' => 'boolean',
        'price' => 'decimal:2',
        'downloads' => 'integer',
    ];

    /**
     * Get the tenant that owns the theme.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the category that the theme belongs to.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(MarketplaceCategory::class, 'category_id');
    }

    /**
     * Get the reviews for the theme.
     */
    public function reviews(): MorphMany
    {
        return $this->morphMany(MarketplaceReview::class, 'reviewable');
    }

    /**
     * Activate this theme.
     */
    public function activate(): bool
    {
        // Deactivate all other themes first
        if ($this->tenant_id) {
            // If tenant-specific theme, only deactivate other tenant themes
            self::where('tenant_id', $this->tenant_id)
                ->where('id', '!=', $this->id)
                ->update(['is_active' => false]);
        } else {
            // If global theme, deactivate all other global themes
            self::whereNull('tenant_id')
                ->where('id', '!=', $this->id)
                ->update(['is_active' => false]);
        }

        // Activate this theme
        $this->is_active = true;
        return $this->save();
    }

    /**
     * Deactivate this theme.
     */
    public function deactivate(): bool
    {
        $this->is_active = false;
        return $this->save();
    }

    /**
     * Get the average rating for this theme.
     */
    public function getAverageRatingAttribute()
    {
        return $this->reviews()->approved()->avg('rating') ?: 0;
    }

    /**
     * Get the total number of reviews for this theme.
     */
    public function getReviewsCountAttribute()
    {
        return $this->reviews()->approved()->count();
    }

    /**
     * Scope a query to only include approved themes.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope a query to only include featured themes.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to only include free themes.
     */
    public function scopeFree($query)
    {
        return $query->where('price', 0);
    }

    /**
     * Scope a query to only include paid themes.
     */
    public function scopePaid($query)
    {
        return $query->where('price', '>', 0);
    }

    /**
     * Increment the download count for this theme.
     */
    public function incrementDownloads(): bool
    {
        $this->downloads += 1;
        return $this->save();
    }
}
