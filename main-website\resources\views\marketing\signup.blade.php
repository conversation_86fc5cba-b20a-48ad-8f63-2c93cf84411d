@extends('marketing.layouts.app')

@section('title', 'Sign Up')

@section('content')
    <!-- Signup Header -->
    <div class="bg-gray-50 py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl font-extrabold text-gray-900 sm:text-5xl sm:tracking-tight lg:text-6xl">Start your free trial</h1>
                <p class="mt-5 max-w-xl mx-auto text-xl text-gray-500">Create your account and start building your learning platform today. No credit card required.</p>
            </div>
        </div>
    </div>

    <!-- Signup Form -->
    <div class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="max-w-3xl mx-auto">
                <form action="{{ route('signup.store') }}" method="POST" class="space-y-8">
                    @csrf
                    
                    <!-- Account Information -->
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Account Information</h2>
                        <div class="bg-white shadow overflow-hidden sm:rounded-md">
                            <div class="px-4 py-5 sm:p-6">
                                <div class="grid grid-cols-6 gap-6">
                                    <div class="col-span-6 sm:col-span-3">
                                        <label for="name" class="block text-sm font-medium text-gray-700">Full Name</label>
                                        <input type="text" name="name" id="name" autocomplete="name" value="{{ old('name') }}" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                        @error('name')
                                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div class="col-span-6 sm:col-span-3">
                                        <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                                        <input type="email" name="email" id="email" autocomplete="email" value="{{ old('email') }}" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                        @error('email')
                                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div class="col-span-6 sm:col-span-3">
                                        <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                                        <input type="password" name="password" id="password" autocomplete="new-password" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                        @error('password')
                                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div class="col-span-6 sm:col-span-3">
                                        <label for="password_confirmation" class="block text-sm font-medium text-gray-700">Confirm Password</label>
                                        <input type="password" name="password_confirmation" id="password_confirmation" autocomplete="new-password" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Platform Information -->
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Platform Information</h2>
                        <div class="bg-white shadow overflow-hidden sm:rounded-md">
                            <div class="px-4 py-5 sm:p-6">
                                <div class="grid grid-cols-6 gap-6">
                                    <div class="col-span-6 sm:col-span-3">
                                        <label for="company_name" class="block text-sm font-medium text-gray-700">Academy Name</label>
                                        <input type="text" name="company_name" id="company_name" value="{{ old('company_name') }}" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                                        @error('company_name')
                                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div class="col-span-6 sm:col-span-3">
                                        <label for="subdomain" class="block text-sm font-medium text-gray-700">Subdomain</label>
                                        <div class="mt-1 flex rounded-md shadow-sm">
                                            <input type="text" name="subdomain" id="subdomain" value="{{ old('subdomain') }}" class="focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-l-md sm:text-sm border-gray-300">
                                            <span class="inline-flex items-center px-3 rounded-r-md border border-l-0 border-gray-300 bg-gray-50 text-gray-500 sm:text-sm">.lmsplatform.com</span>
                                        </div>
                                        @error('subdomain')
                                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Plan Selection -->
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Select Your Plan</h2>
                        <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
                            @foreach($plans as $index => $plan)
                                <div class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm flex flex-col items-start hover:border-gray-400 focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500 {{ $plan['popular'] ? 'border-indigo-500 ring-2 ring-indigo-500' : '' }}">
                                    @if($plan['popular'])
                                        <div class="absolute top-0 right-0 -mt-3 mr-3">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                                Most Popular
                                            </span>
                                        </div>
                                    @endif
                                    <div class="w-full">
                                        <h3 class="text-lg font-medium text-gray-900">{{ $plan['name'] }}</h3>
                                        <p class="mt-1 text-sm text-gray-500">{{ $plan['description'] }}</p>
                                        <p class="mt-4">
                                            <span class="text-3xl font-extrabold text-gray-900">${{ $plan['price'] }}</span>
                                            <span class="text-base font-medium text-gray-500">/{{ $plan['billing_period'] }}</span>
                                        </p>
                                        <ul role="list" class="mt-4 space-y-3">
                                            @foreach($plan['features'] as $feature)
                                                <li class="flex items-start">
                                                    <div class="flex-shrink-0">
                                                        <svg class="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                                        </svg>
                                                    </div>
                                                    <p class="ml-2 text-sm text-gray-500">{{ $feature }}</p>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                    <div class="mt-6 w-full">
                                        <div class="flex items-center">
                                            <input id="plan-{{ strtolower($plan['name']) }}" name="plan" type="radio" value="{{ strtolower($plan['name']) }}" class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300" {{ $plan['popular'] ? 'checked' : '' }}>
                                            <label for="plan-{{ strtolower($plan['name']) }}" class="ml-3 block text-sm font-medium text-gray-700">
                                                Select {{ $plan['name'] }}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        @error('plan')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="relative flex items-start">
                        <div class="flex items-center h-5">
                            <input id="terms" name="terms" type="checkbox" class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                        </div>
                        <div class="ml-3 text-sm">
                            <label for="terms" class="font-medium text-gray-700">I agree to the terms and conditions</label>
                            <p class="text-gray-500">By creating an account, you agree to our <a href="#" class="text-indigo-600 hover:text-indigo-500">Terms of Service</a> and <a href="#" class="text-indigo-600 hover:text-indigo-500">Privacy Policy</a>.</p>
                            @error('terms')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div>
                        <button type="submit" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Create Account & Start Free Trial
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
