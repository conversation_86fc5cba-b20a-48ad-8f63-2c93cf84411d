<?php

namespace App\Http\Controllers\Marketing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class BlogController extends Controller
{
    /**
     * Display the blog index page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // In a real application, you would fetch blog posts from a database
        // For now, we'll use static data
        
        $posts = [
            [
                'id' => 1,
                'title' => '10 Tips for Creating Engaging Online Courses',
                'slug' => '10-tips-for-creating-engaging-online-courses',
                'excerpt' => 'Learn how to create online courses that keep students engaged and motivated throughout their learning journey.',
                'author' => '<PERSON>',
                'published_at' => '2023-06-15',
                'category' => 'Course Creation',
                'image' => 'img/marketing/blog/course-creation.jpg',
            ],
            [
                'id' => 2,
                'title' => 'How to Grow Your Online Academy: Marketing Strategies That Work',
                'slug' => 'how-to-grow-your-online-academy',
                'excerpt' => 'Discover effective marketing strategies to attract more students to your online courses and grow your educational business.',
                'author' => '<PERSON>',
                'published_at' => '2023-05-22',
                'category' => 'Marketing',
                'image' => 'img/marketing/blog/marketing.jpg',
            ],
            [
                'id' => 3,
                'title' => 'The Future of E-Learning: Trends to Watch in 2023',
                'slug' => 'future-of-elearning-trends-2023',
                'excerpt' => 'Stay ahead of the curve with these emerging trends in online education and e-learning technology.',
                'author' => 'Michael Johnson',
                'published_at' => '2023-04-10',
                'category' => 'Industry Trends',
                'image' => 'img/marketing/blog/trends.jpg',
            ],
            [
                'id' => 4,
                'title' => 'Building Community in Your Online Courses',
                'slug' => 'building-community-in-online-courses',
                'excerpt' => 'Learn strategies for fostering a sense of community and connection among your online students.',
                'author' => 'Sarah Williams',
                'published_at' => '2023-03-05',
                'category' => 'Student Engagement',
                'image' => 'img/marketing/blog/community.jpg',
            ],
            [
                'id' => 5,
                'title' => 'Pricing Your Online Courses: A Complete Guide',
                'slug' => 'pricing-online-courses-guide',
                'excerpt' => 'Discover strategies for pricing your online courses to maximize both enrollment and revenue.',
                'author' => 'John Doe',
                'published_at' => '2023-02-18',
                'category' => 'Business',
                'image' => 'img/marketing/blog/pricing.jpg',
            ],
            [
                'id' => 6,
                'title' => 'Creating Effective Assessments for Online Learning',
                'slug' => 'creating-effective-assessments',
                'excerpt' => 'Learn how to design assessments that accurately measure student learning in an online environment.',
                'author' => 'Jane Smith',
                'published_at' => '2023-01-30',
                'category' => 'Course Creation',
                'image' => 'img/marketing/blog/assessments.jpg',
            ],
        ];

        $categories = [
            'All',
            'Course Creation',
            'Marketing',
            'Industry Trends',
            'Student Engagement',
            'Business',
            'Technology',
        ];

        return view('marketing.blog.index', compact('posts', 'categories'));
    }

    /**
     * Display a specific blog post.
     *
     * @param  string  $slug
     * @return \Illuminate\View\View
     */
    public function show($slug)
    {
        // In a real application, you would fetch the blog post from a database
        // For now, we'll use static data based on the slug
        
        $post = [
            'title' => '10 Tips for Creating Engaging Online Courses',
            'slug' => '10-tips-for-creating-engaging-online-courses',
            'content' => '<p>Creating engaging online courses is essential for student success and satisfaction. Here are 10 tips to help you create courses that keep students motivated and engaged throughout their learning journey.</p>
                         <h2>1. Start with Clear Learning Objectives</h2>
                         <p>Define what students will be able to do after completing your course. Clear objectives help students understand the value of your course and stay motivated.</p>
                         <h2>2. Use a Variety of Content Types</h2>
                         <p>Mix videos, text, images, infographics, and interactive elements to cater to different learning styles and keep the course interesting.</p>
                         <h2>3. Keep Videos Short and Focused</h2>
                         <p>Aim for videos that are 5-10 minutes long, focusing on a single concept or skill. This helps maintain student attention and makes the content more digestible.</p>
                         <h2>4. Incorporate Interactive Elements</h2>
                         <p>Include quizzes, assignments, discussions, and other interactive elements to encourage active learning and engagement.</p>
                         <h2>5. Provide Regular Feedback</h2>
                         <p>Give students feedback on their progress and performance to help them stay on track and improve.</p>
                         <h2>6. Create a Community</h2>
                         <p>Foster a sense of community through discussion forums, group projects, or live sessions to combat the isolation that can come with online learning.</p>
                         <h2>7. Use Real-World Examples and Case Studies</h2>
                         <p>Connect theoretical concepts to practical applications to make the content more relevant and engaging.</p>
                         <h2>8. Design for Mobile</h2>
                         <p>Ensure your course is accessible and functional on mobile devices, allowing students to learn on the go.</p>
                         <h2>9. Gamify the Learning Experience</h2>
                         <p>Incorporate elements like points, badges, and leaderboards to make learning more fun and motivating.</p>
                         <h2>10. Continuously Update Your Content</h2>
                         <p>Keep your course content fresh and relevant by regularly updating it with new information, examples, and resources.</p>',
            'author' => 'John Doe',
            'author_bio' => 'John is an experienced online educator with over 10 years of experience in course creation and instructional design.',
            'published_at' => '2023-06-15',
            'category' => 'Course Creation',
            'image' => 'img/marketing/blog/course-creation.jpg',
            'related_posts' => [
                [
                    'title' => 'Creating Effective Assessments for Online Learning',
                    'slug' => 'creating-effective-assessments',
                    'image' => 'img/marketing/blog/assessments.jpg',
                ],
                [
                    'title' => 'Building Community in Your Online Courses',
                    'slug' => 'building-community-in-online-courses',
                    'image' => 'img/marketing/blog/community.jpg',
                ],
            ],
        ];

        return view('marketing.blog.show', compact('post'));
    }
}
