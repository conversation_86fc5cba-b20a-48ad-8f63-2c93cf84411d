import { useState, useEffect } from 'react';
import { courseService } from '../services/api';
import { Grid, Card, CardMedia, CardContent, Typography, Button, Box, CircularProgress } from '@mui/material';

const CourseList = () => {
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchCourses = async () => {
      try {
        setLoading(true);
        console.log('Fetching courses...');

        const response = await courseService.getAllCourses();
        console.log('API Response:', response);

        // Handle both paginated and non-paginated responses
        let courseData = [];
        if (response.data && Array.isArray(response.data)) {
          courseData = response.data;
        } else if (response.data && response.data.data) {
          courseData = response.data.data;
        }

        console.log('Course data:', courseData);

        setCourses(courseData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching courses:', err);
        setError(`Failed to fetch courses: ${err.message}`);
        setLoading(false);
      }
    };

    fetchCourses();
  }, []);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 5 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ textAlign: 'center', py: 5 }}>
        <Typography color="error" gutterBottom>{error}</Typography>
      </Box>
    );
  }

  if (!courses || courses.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 5 }}>
        <Typography>No courses available.</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ py: 4 }}>
      <Grid container spacing={4}>
        {courses.map((course) => (
          <Grid item key={course.id} xs={12} sm={6} md={4}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column', boxShadow: 3 }}>
              <CardMedia
                component="img"
                height="200"
                image={course.thumbnail || 'https://source.unsplash.com/random?education'}
                alt={course.title}
              />
              <CardContent sx={{ flexGrow: 1 }}>
                <Typography gutterBottom variant="h5" component="h2">
                  {course.title}
                </Typography>
                <Typography sx={{ mb: 2, overflow: 'hidden', textOverflow: 'ellipsis', display: '-webkit-box', WebkitLineClamp: 3, WebkitBoxOrient: 'vertical' }}>
                  {course.description}
                </Typography>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 'auto' }}>
                  <Typography variant="h6" color="primary">
                    ${course.price}
                  </Typography>
                  <Button variant="contained" color="primary" href={`/course/${course.id}`}>
                    View Course
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default CourseList;
