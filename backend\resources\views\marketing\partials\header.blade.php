<header class="bg-white shadow-sm sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
            <div class="flex">
                <div class="flex-shrink-0 flex items-center">
                    <a href="{{ route('marketing.home') }}" class="flex items-center">
                        <img class="h-8 w-auto" src="{{ asset('img/logo.svg') }}" alt="LMS Platform Logo">
                        <span class="ml-2 text-xl font-bold text-blue-600">LMS Platform</span>
                    </a>
                </div>
                <nav class="hidden sm:ml-6 sm:flex sm:space-x-8" aria-label="Main Navigation">
                    <a href="{{ route('marketing.features') }}" class="inline-flex items-center px-1 pt-1 border-b-2 {{ request()->routeIs('marketing.features') ? 'border-blue-500 text-gray-900' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700' }} text-sm font-medium">
                        Features
                    </a>
                    <a href="{{ route('marketing.pricing') }}" class="inline-flex items-center px-1 pt-1 border-b-2 {{ request()->routeIs('marketing.pricing') ? 'border-blue-500 text-gray-900' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700' }} text-sm font-medium">
                        Pricing
                    </a>
                    <a href="{{ route('marketing.themes') }}" class="inline-flex items-center px-1 pt-1 border-b-2 {{ request()->routeIs('marketing.themes') ? 'border-blue-500 text-gray-900' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700' }} text-sm font-medium">
                        Themes
                    </a>
                    <a href="{{ route('marketing.extensions') }}" class="inline-flex items-center px-1 pt-1 border-b-2 {{ request()->routeIs('marketing.extensions') ? 'border-blue-500 text-gray-900' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700' }} text-sm font-medium">
                        Extensions
                    </a>
                    <a href="{{ route('marketing.mobile-app') }}" class="inline-flex items-center px-1 pt-1 border-b-2 {{ request()->routeIs('marketing.mobile-app') ? 'border-blue-500 text-gray-900' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700' }} text-sm font-medium">
                        Mobile App
                    </a>
                    <a href="{{ route('marketing.docs') }}" class="inline-flex items-center px-1 pt-1 border-b-2 {{ request()->routeIs('marketing.docs') ? 'border-blue-500 text-gray-900' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700' }} text-sm font-medium">
                        Docs
                    </a>
                    <a href="{{ route('marketing.blog') }}" class="inline-flex items-center px-1 pt-1 border-b-2 {{ request()->routeIs('marketing.blog') ? 'border-blue-500 text-gray-900' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700' }} text-sm font-medium">
                        Blog
                    </a>
                </nav>
            </div>
            <div class="hidden sm:ml-6 sm:flex sm:items-center sm:space-x-4">
                <a href="{{ url('/app/login') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Log in
                </a>
                <a href="{{ route('marketing.signup') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Start Free Trial
                </a>
            </div>
            <div class="-mr-2 flex items-center sm:hidden">
                <!-- Mobile menu button -->
                <button type="button" class="mobile-menu-button inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500" aria-expanded="false">
                    <span class="sr-only">Open main menu</span>
                    <!-- Icon when menu is closed -->
                    <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                    <!-- Icon when menu is open -->
                    <svg class="hidden h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile menu, show/hide based on menu state. -->
    <div class="mobile-menu hidden sm:hidden">
        <div class="pt-2 pb-3 space-y-1">
            <a href="{{ route('marketing.features') }}" class="block pl-3 pr-4 py-2 border-l-4 {{ request()->routeIs('marketing.features') ? 'border-blue-500 text-blue-700 bg-blue-50' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800' }} text-base font-medium">
                Features
            </a>
            <a href="{{ route('marketing.pricing') }}" class="block pl-3 pr-4 py-2 border-l-4 {{ request()->routeIs('marketing.pricing') ? 'border-blue-500 text-blue-700 bg-blue-50' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800' }} text-base font-medium">
                Pricing
            </a>
            <a href="{{ route('marketing.themes') }}" class="block pl-3 pr-4 py-2 border-l-4 {{ request()->routeIs('marketing.themes') ? 'border-blue-500 text-blue-700 bg-blue-50' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800' }} text-base font-medium">
                Themes
            </a>
            <a href="{{ route('marketing.extensions') }}" class="block pl-3 pr-4 py-2 border-l-4 {{ request()->routeIs('marketing.extensions') ? 'border-blue-500 text-blue-700 bg-blue-50' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800' }} text-base font-medium">
                Extensions
            </a>
            <a href="{{ route('marketing.mobile-app') }}" class="block pl-3 pr-4 py-2 border-l-4 {{ request()->routeIs('marketing.mobile-app') ? 'border-blue-500 text-blue-700 bg-blue-50' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800' }} text-base font-medium">
                Mobile App
            </a>
            <a href="{{ route('marketing.docs') }}" class="block pl-3 pr-4 py-2 border-l-4 {{ request()->routeIs('marketing.docs') ? 'border-blue-500 text-blue-700 bg-blue-50' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800' }} text-base font-medium">
                Docs
            </a>
            <a href="{{ route('marketing.blog') }}" class="block pl-3 pr-4 py-2 border-l-4 {{ request()->routeIs('marketing.blog') ? 'border-blue-500 text-blue-700 bg-blue-50' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800' }} text-base font-medium">
                Blog
            </a>
        </div>
        <div class="pt-4 pb-3 border-t border-gray-200">
            <div class="flex items-center px-4 space-x-3">
                <a href="{{ url('/app/login') }}" class="block text-base font-medium text-blue-600 hover:text-blue-800">
                    Log in
                </a>
                <a href="{{ route('marketing.signup') }}" class="block px-4 py-2 text-base font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md">
                    Start Free Trial
                </a>
            </div>
        </div>
    </div>
</header>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const mobileMenuButton = document.querySelector('.mobile-menu-button');
        const mobileMenu = document.querySelector('.mobile-menu');
        
        mobileMenuButton.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
            
            // Toggle the icons
            const menuOpenIcon = mobileMenuButton.querySelector('svg:first-child');
            const menuCloseIcon = mobileMenuButton.querySelector('svg:last-child');
            
            menuOpenIcon.classList.toggle('hidden');
            menuOpenIcon.classList.toggle('block');
            menuCloseIcon.classList.toggle('hidden');
            menuCloseIcon.classList.toggle('block');
        });
    });
</script>
@endpush
