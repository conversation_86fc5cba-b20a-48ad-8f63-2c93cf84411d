import React, { useState, useEffect } from 'react';
import { 
  Container, 
  Typo<PERSON>, 
  Box, 
  Grid, 
  Card, 
  CardContent, 
  CardMedia, 
  Button, 
  Tabs, 
  Tab, 
  Chip, 
  TextField, 
  InputAdornment, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  Pagination, 
  CircularProgress, 
  Alert 
} from '@mui/material';
import { 
  Search as SearchIcon, 
  FilterList as FilterListIcon, 
  Star as StarIcon, 
  StarBorder as StarBorderIcon,
  ShoppingCart as ShoppingCartIcon,
  Download as DownloadIcon,
  Palette as PaletteIcon,
  Extension as ExtensionIcon
} from '@mui/icons-material';
import { Link } from 'react-router-dom';
import { marketplaceService } from '../services/api';

const Marketplace = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tabValue, setTabValue] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [priceFilter, setPriceFilter] = useState('all');
  const [sortBy, setSortBy] = useState('newest');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  
  const [featuredItems, setFeaturedItems] = useState({ themes: [], modules: [] });
  const [categories, setCategories] = useState([]);
  const [items, setItems] = useState({ themes: [], modules: [] });
  
  // Fetch featured items on initial load
  useEffect(() => {
    const fetchFeaturedItems = async () => {
      try {
        setLoading(true);
        const response = await marketplaceService.getFeatured();
        setFeaturedItems(response.data.data);
        setError(null);
      } catch (error) {
        console.error('Error fetching featured items:', error);
        setError('Failed to load featured items. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchFeaturedItems();
  }, []);
  
  // Fetch categories on initial load
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await marketplaceService.getCategories({
          type: tabValue === 0 ? 'both' : tabValue === 1 ? 'theme' : 'module'
        });
        setCategories(response.data.data);
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };
    
    fetchCategories();
  }, [tabValue]);
  
  // Fetch items based on filters
  useEffect(() => {
    const fetchItems = async () => {
      try {
        setLoading(true);
        
        const params = {
          page,
          per_page: 12,
          sort: sortBy,
          price: priceFilter
        };
        
        if (categoryFilter) {
          params.category_id = categoryFilter;
        }
        
        if (searchQuery.trim()) {
          params.query = searchQuery.trim();
          const response = await marketplaceService.search({
            ...params,
            type: tabValue === 0 ? 'both' : tabValue === 1 ? 'theme' : 'module'
          });
          setItems(response.data.data);
          
          // Set total pages based on the response
          if (response.data.data.themes) {
            setTotalPages(response.data.data.themes.last_page);
          } else if (response.data.data.modules) {
            setTotalPages(response.data.data.modules.last_page);
          }
        } else {
          if (tabValue === 0 || tabValue === 1) {
            const themesResponse = await marketplaceService.getThemes(params);
            setItems(prev => ({ ...prev, themes: themesResponse.data.data }));
            setTotalPages(themesResponse.data.data.last_page);
          }
          
          if (tabValue === 0 || tabValue === 2) {
            const modulesResponse = await marketplaceService.getModules(params);
            setItems(prev => ({ ...prev, modules: modulesResponse.data.data }));
            if (tabValue === 2) {
              setTotalPages(modulesResponse.data.data.last_page);
            }
          }
        }
        
        setError(null);
      } catch (error) {
        console.error('Error fetching items:', error);
        setError('Failed to load items. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchItems();
  }, [tabValue, searchQuery, categoryFilter, priceFilter, sortBy, page]);
  
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    setPage(1);
  };
  
  const handleSearch = (event) => {
    event.preventDefault();
    setPage(1);
  };
  
  const handlePageChange = (event, value) => {
    setPage(value);
    window.scrollTo(0, 0);
  };
  
  const renderRating = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    
    for (let i = 1; i <= 5; i++) {
      if (i <= fullStars) {
        stars.push(<StarIcon key={i} fontSize="small" color="primary" />);
      } else if (i === fullStars + 1 && hasHalfStar) {
        stars.push(<StarIcon key={i} fontSize="small" color="primary" sx={{ opacity: 0.6 }} />);
      } else {
        stars.push(<StarBorderIcon key={i} fontSize="small" color="primary" />);
      }
    }
    
    return (
      <Box display="flex" alignItems="center">
        {stars}
        <Typography variant="body2" color="text.secondary" sx={{ ml: 0.5 }}>
          ({rating.toFixed(1)})
        </Typography>
      </Box>
    );
  };
  
  const renderItemCard = (item, type) => {
    return (
      <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column', position: 'relative' }}>
        {item.is_featured && (
          <Chip
            label="Featured"
            color="primary"
            size="small"
            sx={{
              position: 'absolute',
              top: 10,
              right: 10,
              zIndex: 1,
            }}
          />
        )}
        <CardMedia
          component="img"
          height="140"
          image={item.thumbnail || `https://source.unsplash.com/random?${type}`}
          alt={item.name}
        />
        <CardContent sx={{ flexGrow: 1 }}>
          <Typography gutterBottom variant="h6" component="div" noWrap>
            {item.name}
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1, height: 40, overflow: 'hidden' }}>
            {item.description}
          </Typography>
          {renderRating(item.average_rating || 0)}
          <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
            <Typography variant="h6" color="primary">
              {item.price > 0 ? `$${item.price.toFixed(2)}` : 'Free'}
            </Typography>
            <Box>
              <Button
                variant="contained"
                size="small"
                startIcon={item.price > 0 ? <ShoppingCartIcon /> : <DownloadIcon />}
                sx={{ mr: 1 }}
              >
                {item.price > 0 ? 'Buy' : 'Install'}
              </Button>
              <Button
                variant="outlined"
                size="small"
                component={Link}
                to={`/marketplace/${type}/${item.id}`}
              >
                Details
              </Button>
            </Box>
          </Box>
        </CardContent>
      </Card>
    );
  };
  
  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom>
        Marketplace
      </Typography>
      <Typography variant="subtitle1" color="text.secondary" paragraph>
        Discover themes and modules to enhance your learning platform
      </Typography>
      
      {/* Search and Filter Bar */}
      <Box component="form" onSubmit={handleSearch} sx={{ mb: 4 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="Search themes and modules..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel id="category-label">Category</InputLabel>
              <Select
                labelId="category-label"
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                label="Category"
              >
                <MenuItem value="">All Categories</MenuItem>
                {categories.map((category) => (
                  <MenuItem key={category.id} value={category.id}>
                    {category.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel id="price-label">Price</InputLabel>
              <Select
                labelId="price-label"
                value={priceFilter}
                onChange={(e) => setPriceFilter(e.target.value)}
                label="Price"
              >
                <MenuItem value="all">All Prices</MenuItem>
                <MenuItem value="free">Free</MenuItem>
                <MenuItem value="paid">Paid</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <FormControl fullWidth>
              <InputLabel id="sort-label">Sort By</InputLabel>
              <Select
                labelId="sort-label"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                label="Sort By"
              >
                <MenuItem value="newest">Newest</MenuItem>
                <MenuItem value="popular">Most Popular</MenuItem>
                <MenuItem value="rating">Highest Rated</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <Button
              fullWidth
              variant="contained"
              type="submit"
              startIcon={<FilterListIcon />}
            >
              Apply Filters
            </Button>
          </Grid>
        </Grid>
      </Box>
      
      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 4 }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="marketplace tabs">
          <Tab label="All" id="tab-0" />
          <Tab label="Themes" icon={<PaletteIcon />} iconPosition="start" id="tab-1" />
          <Tab label="Modules" icon={<ExtensionIcon />} iconPosition="start" id="tab-2" />
        </Tabs>
      </Box>
      
      {error && (
        <Alert severity="error" sx={{ mb: 4 }}>
          {error}
        </Alert>
      )}
      
      {/* Featured Items (only show on first tab and when not searching) */}
      {tabValue === 0 && !searchQuery && (
        <>
          <Typography variant="h4" component="h2" gutterBottom>
            Featured Themes
          </Typography>
          <Grid container spacing={4} sx={{ mb: 6 }}>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : featuredItems.themes?.length > 0 ? (
              featuredItems.themes.map((theme) => (
                <Grid item key={theme.id} xs={12} sm={6} md={4} lg={3}>
                  {renderItemCard(theme, 'theme')}
                </Grid>
              ))
            ) : (
              <Grid item xs={12}>
                <Alert severity="info">No featured themes available.</Alert>
              </Grid>
            )}
          </Grid>
          
          <Typography variant="h4" component="h2" gutterBottom>
            Featured Modules
          </Typography>
          <Grid container spacing={4} sx={{ mb: 6 }}>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : featuredItems.modules?.length > 0 ? (
              featuredItems.modules.map((module) => (
                <Grid item key={module.id} xs={12} sm={6} md={4} lg={3}>
                  {renderItemCard(module, 'module')}
                </Grid>
              ))
            ) : (
              <Grid item xs={12}>
                <Alert severity="info">No featured modules available.</Alert>
              </Grid>
            )}
          </Grid>
        </>
      )}
      
      {/* Main Content */}
      {(tabValue === 0 || tabValue === 1) && (
        <>
          <Typography variant="h4" component="h2" gutterBottom>
            {searchQuery ? 'Search Results - Themes' : 'All Themes'}
          </Typography>
          <Grid container spacing={4} sx={{ mb: 6 }}>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : items.themes?.data?.length > 0 ? (
              items.themes.data.map((theme) => (
                <Grid item key={theme.id} xs={12} sm={6} md={4} lg={3}>
                  {renderItemCard(theme, 'theme')}
                </Grid>
              ))
            ) : (
              <Grid item xs={12}>
                <Alert severity="info">
                  {searchQuery ? 'No themes found matching your search criteria.' : 'No themes available.'}
                </Alert>
              </Grid>
            )}
          </Grid>
        </>
      )}
      
      {(tabValue === 0 || tabValue === 2) && (
        <>
          <Typography variant="h4" component="h2" gutterBottom>
            {searchQuery ? 'Search Results - Modules' : 'All Modules'}
          </Typography>
          <Grid container spacing={4} sx={{ mb: 6 }}>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : items.modules?.data?.length > 0 ? (
              items.modules.data.map((module) => (
                <Grid item key={module.id} xs={12} sm={6} md={4} lg={3}>
                  {renderItemCard(module, 'module')}
                </Grid>
              ))
            ) : (
              <Grid item xs={12}>
                <Alert severity="info">
                  {searchQuery ? 'No modules found matching your search criteria.' : 'No modules available.'}
                </Alert>
              </Grid>
            )}
          </Grid>
        </>
      )}
      
      {/* Pagination */}
      {totalPages > 1 && (
        <Box display="flex" justifyContent="center" mt={4}>
          <Pagination
            count={totalPages}
            page={page}
            onChange={handlePageChange}
            color="primary"
            size="large"
          />
        </Box>
      )}
    </Container>
  );
};

export default Marketplace;
