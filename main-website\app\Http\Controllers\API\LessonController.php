<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Enrollment;
use App\Models\Lesson;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class LessonController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Lesson::query();

        // Filter by course if provided
        if ($request->has('course_id')) {
            $query->where('course_id', $request->course_id);
        }

        // Filter by section if provided
        if ($request->has('section_id')) {
            $query->where('section_id', $request->section_id);
        }

        // Filter by type if provided
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        // Filter by published status
        if ($request->has('is_published')) {
            $query->where('is_published', $request->boolean('is_published'));
        }

        // Filter by free status
        if ($request->has('is_free')) {
            $query->where('is_free', $request->boolean('is_free'));
        }

        // Sort by
        $sortBy = $request->input('sort_by', 'sort_order');
        $sortOrder = $request->input('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        // Paginate
        $perPage = $request->input('per_page', 10);
        $lessons = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $lessons,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|string|in:video,text,quiz,assignment',
            'content' => 'nullable|string',
            'video_url' => 'nullable|string',
            'video_duration' => 'nullable|string',
            'attachment' => 'nullable|string',
            'is_free' => 'nullable|boolean',
            'is_published' => 'nullable|boolean',
            'sort_order' => 'nullable|integer',
            'course_id' => 'required|exists:courses,id',
            'section_id' => 'nullable|exists:sections,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        // Check if the user is the instructor of the course
        $course = Course::findOrFail($request->course_id);
        if (auth()->id() !== $course->instructor_id) {
            return response()->json([
                'success' => false,
                'message' => 'You are not authorized to add lessons to this course',
            ], 403);
        }

        // Create slug from title
        $slug = Str::slug($request->title);
        $count = 1;

        // Ensure slug is unique within the course
        while (Lesson::where('course_id', $request->course_id)
                    ->where('slug', $slug)
                    ->exists()) {
            $slug = Str::slug($request->title) . '-' . $count;
            $count++;
        }

        // Create lesson
        $lesson = Lesson::create(array_merge(
            $validator->validated(),
            ['slug' => $slug]
        ));

        return response()->json([
            'success' => true,
            'message' => 'Lesson created successfully',
            'data' => $lesson,
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $lesson = Lesson::with(['course', 'section'])->findOrFail($id);

        // Check if the lesson is published or the user is the instructor
        if (!$lesson->is_published && 
            (!auth()->check() || auth()->id() !== $lesson->course->instructor_id)) {
            
            return response()->json([
                'success' => false,
                'message' => 'Lesson not found',
            ], 404);
        }

        // Check if the user is enrolled in the course or the lesson is free
        if (!$lesson->is_free && auth()->check()) {
            $isEnrolled = Enrollment::where('user_id', auth()->id())
                ->where('course_id', $lesson->course_id)
                ->exists();
            
            if (!$isEnrolled && auth()->id() !== $lesson->course->instructor_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'You need to enroll in this course to access this lesson',
                ], 403);
            }
        }

        return response()->json([
            'success' => true,
            'data' => $lesson,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $lesson = Lesson::findOrFail($id);

        // Check if the user is the instructor of the course
        if (auth()->id() !== $lesson->course->instructor_id) {
            return response()->json([
                'success' => false,
                'message' => 'You are not authorized to update this lesson',
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|string|max:255',
            'description' => 'nullable|string',
            'type' => 'sometimes|string|in:video,text,quiz,assignment',
            'content' => 'nullable|string',
            'video_url' => 'nullable|string',
            'video_duration' => 'nullable|string',
            'attachment' => 'nullable|string',
            'is_free' => 'nullable|boolean',
            'is_published' => 'nullable|boolean',
            'sort_order' => 'nullable|integer',
            'section_id' => 'nullable|exists:sections,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        // Update slug if title is changed
        if ($request->has('title') && $request->title !== $lesson->title) {
            $slug = Str::slug($request->title);
            $count = 1;

            // Ensure slug is unique within the course
            while (Lesson::where('course_id', $lesson->course_id)
                        ->where('slug', $slug)
                        ->where('id', '!=', $id)
                        ->exists()) {
                $slug = Str::slug($request->title) . '-' . $count;
                $count++;
            }

            $lesson->slug = $slug;
        }

        $lesson->update($validator->validated());

        return response()->json([
            'success' => true,
            'message' => 'Lesson updated successfully',
            'data' => $lesson,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $lesson = Lesson::findOrFail($id);

        // Check if the user is the instructor of the course
        if (auth()->id() !== $lesson->course->instructor_id) {
            return response()->json([
                'success' => false,
                'message' => 'You are not authorized to delete this lesson',
            ], 403);
        }

        $lesson->delete();

        return response()->json([
            'success' => true,
            'message' => 'Lesson deleted successfully',
        ]);
    }
}
