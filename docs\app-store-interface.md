# App Store Interface

The App Store Interface is a Shopify-like marketplace for themes and modules in the LMS platform. It allows tenants to browse, install, and purchase themes and modules to customize their educational websites and mobile apps.

## Features

- Browse and search themes and modules
- Filter by category, price, and rating
- View detailed information about themes and modules
- Install free themes and modules
- Purchase paid themes and modules
- Rate and review themes and modules
- Mobile-friendly interface

## Implementation Details

### Database Structure

The marketplace functionality is built on top of the existing theme and module system, with the following additions:

- **MarketplaceCategory**: Categories for organizing themes and modules
- **MarketplaceReview**: User reviews and ratings for themes and modules
- Additional fields for themes and modules:
  - `category_id`: Category classification
  - `price`: Free or paid amount
  - `downloads`: Number of installations
  - `is_featured`: Featured status
  - `screenshots`: Array of screenshot URLs
  - `demo_url`: URL to demo the theme/module
  - `status`: Approval workflow status (pending, approved, rejected)

### API Endpoints

#### Marketplace Endpoints

- `GET /api/marketplace/featured`: Get featured themes and modules
- `GET /api/marketplace/categories`: Get marketplace categories
- `GET /api/marketplace/search`: Search themes and modules
- `GET /api/marketplace/themes`: Get themes with filtering and pagination
- `GET /api/marketplace/modules`: Get modules with filtering and pagination

#### Review Endpoints

- `GET /api/marketplace/reviews`: Get reviews for a theme or module
- `POST /api/marketplace/reviews`: Submit a review
- `PUT /api/marketplace/reviews/{id}`: Update a review
- `DELETE /api/marketplace/reviews/{id}`: Delete a review
- `POST /api/marketplace/reviews/{id}/approve`: Approve a review (admin only)
- `POST /api/marketplace/reviews/{id}/reject`: Reject a review (admin only)

#### Installation and Purchase Endpoints

- `POST /api/themes/{id}/install`: Install a free theme
- `POST /api/themes/{id}/purchase`: Purchase a paid theme
- `POST /api/modules/{id}/install-marketplace`: Install a free module
- `POST /api/modules/{id}/purchase`: Purchase a paid module

### Frontend Components

- **Marketplace Page**: Main page for browsing themes and modules
- **MarketplaceItemDetail**: Detailed view of a theme or module
- **Mobile Marketplace**: Mobile-friendly version of the marketplace

### Mobile App Integration

The marketplace is fully integrated into the mobile app, allowing users to:

- Browse themes and modules on their mobile devices
- View detailed information about themes and modules
- Install and purchase themes and modules
- Rate and review themes and modules

## Usage

### For Tenants

1. Navigate to the Marketplace page
2. Browse or search for themes and modules
3. Click on a theme or module to view details
4. Click "Install" for free items or "Purchase" for paid items
5. Manage installed themes and modules from the Themes and Modules pages

### For Developers

1. Create a theme or module
2. Submit it to the marketplace
3. Once approved, it will be available for tenants to install or purchase

## Future Enhancements

- Developer portal for theme and module submission
- Analytics dashboard for developers
- Revenue sharing for paid themes and modules
- Theme and module versioning and updates
- More advanced filtering and search options
