🎨 Theme Development Guide

This guide provides comprehensive instructions for creating custom themes for the LMS platform. Themes allow tenants to customize the appearance and layout of their educational websites and mobile apps.

## 📚 Theme Architecture

Themes in our LMS platform follow a component-based architecture that allows for maximum flexibility and customization. A theme consists of:

1. **Theme Metadata**: Information about the theme (name, description, author, etc.)
2. **Templates**: HTML/JSX templates for different pages and components
3. **Styles**: CSS/SCSS files for styling
4. **Assets**: Images, fonts, and other static assets
5. **Configuration**: Theme settings and customization options

## 🏗️ Theme Structure

A typical theme follows this directory structure:

```
theme-name/
├── theme.json             # Theme metadata and configuration
├── templates/             # Page and component templates
│   ├── layouts/           # Layout templates
│   │   ├── default.blade.php
│   │   └── course.blade.php
│   ├── pages/             # Page templates
│   │   ├── home.blade.php
│   │   ├── course.blade.php
│   │   └── ...
│   └── components/        # Component templates
│       ├── header.blade.php
│       ├── footer.blade.php
│       └── ...
├── assets/                # Static assets
│   ├── images/
│   ├── fonts/
│   └── js/
└── styles/                # CSS/SCSS files
    ├── main.scss
    ├── variables.scss
    └── components/
        ├── header.scss
        ├── footer.scss
        └── ...
```

## 📝 Theme Metadata (theme.json)

The `theme.json` file contains metadata about the theme and defines customization options:

```json
{
  "name": "Modern Academy",
  "description": "A clean, modern theme for educational websites",
  "version": "1.0.0",
  "author": "Your Name",
  "website": "https://example.com",
  "thumbnail": "assets/images/thumbnail.jpg",
  "price": 0,
  "tags": ["modern", "clean", "responsive"],
  "customization": {
    "colors": {
      "primary": {
        "label": "Primary Color",
        "default": "#4285F4",
        "type": "color"
      },
      "secondary": {
        "label": "Secondary Color",
        "default": "#34A853",
        "type": "color"
      },
      "text": {
        "label": "Text Color",
        "default": "#333333",
        "type": "color"
      }
    },
    "fonts": {
      "heading": {
        "label": "Heading Font",
        "default": "Poppins, sans-serif",
        "type": "font",
        "options": ["Poppins, sans-serif", "Roboto, sans-serif", "Montserrat, sans-serif"]
      },
      "body": {
        "label": "Body Font",
        "default": "Inter, sans-serif",
        "type": "font",
        "options": ["Inter, sans-serif", "Open Sans, sans-serif", "Lato, sans-serif"]
      }
    },
    "layout": {
      "sidebar": {
        "label": "Show Sidebar",
        "default": true,
        "type": "boolean"
      },
      "headerStyle": {
        "label": "Header Style",
        "default": "standard",
        "type": "select",
        "options": ["standard", "minimal", "centered"]
      }
    }
  }
}
```

## 🖼️ Templates

Templates define the structure and layout of different pages and components. Our platform uses Laravel Blade templates for server-side rendering.

### Layout Template Example (default.blade.php)

```php
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $title ?? config('app.name') }}</title>
    
    <!-- Theme Styles -->
    <link rel="stylesheet" href="{{ theme_asset('styles/main.css') }}">
    
    <!-- Custom Styles -->
    <style>
        :root {
            --color-primary: {{ theme_option('colors.primary', '#4285F4') }};
            --color-secondary: {{ theme_option('colors.secondary', '#34A853') }};
            --color-text: {{ theme_option('colors.text', '#333333') }};
            --font-heading: {{ theme_option('fonts.heading', 'Poppins, sans-serif') }};
            --font-body: {{ theme_option('fonts.body', 'Inter, sans-serif') }};
        }
    </style>
    
    @stack('styles')
</head>
<body class="theme-{{ theme_name() }}">
    <div class="site-wrapper {{ theme_option('layout.sidebar') ? 'has-sidebar' : '' }}">
        @include('theme::components.header', ['style' => theme_option('layout.headerStyle')])
        
        <main class="site-main">
            @yield('content')
        </main>
        
        @include('theme::components.footer')
    </div>
    
    <!-- Theme Scripts -->
    <script src="{{ theme_asset('assets/js/main.js') }}"></script>
    @stack('scripts')
</body>
</html>
```

### Page Template Example (home.blade.php)

```php
@extends('theme::layouts.default')

@section('content')
<div class="home-page">
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <h1>{{ $page->title }}</h1>
            <p>{{ $page->subtitle }}</p>
            
            @if($featuredCourses->count() > 0)
            <div class="featured-courses">
                <h2>Featured Courses</h2>
                <div class="course-grid">
                    @foreach($featuredCourses as $course)
                        @include('theme::components.course-card', ['course' => $course])
                    @endforeach
                </div>
            </div>
            @endif
        </div>
    </section>
    
    <!-- Categories Section -->
    <section class="categories-section">
        <div class="container">
            <h2>Browse Categories</h2>
            <div class="category-grid">
                @foreach($categories as $category)
                    @include('theme::components.category-card', ['category' => $category])
                @endforeach
            </div>
        </div>
    </section>
    
    <!-- Testimonials Section -->
    @if($testimonials->count() > 0)
    <section class="testimonials-section">
        <div class="container">
            <h2>What Our Students Say</h2>
            <div class="testimonial-slider">
                @foreach($testimonials as $testimonial)
                    @include('theme::components.testimonial', ['testimonial' => $testimonial])
                @endforeach
            </div>
        </div>
    </section>
    @endif
</div>
@endsection
```

### Component Template Example (course-card.blade.php)

```php
<div class="course-card">
    <div class="course-thumbnail">
        <img src="{{ $course->thumbnail_url }}" alt="{{ $course->title }}">
        @if($course->is_featured)
            <span class="featured-badge">Featured</span>
        @endif
    </div>
    <div class="course-content">
        <h3 class="course-title">
            <a href="{{ route('courses.show', $course->slug) }}">{{ $course->title }}</a>
        </h3>
        <div class="course-meta">
            <span class="instructor">{{ $course->instructor->name }}</span>
            <span class="duration">{{ $course->duration_formatted }}</span>
        </div>
        <div class="course-description">
            {{ Str::limit($course->description, 100) }}
        </div>
        <div class="course-footer">
            <div class="price">
                @if($course->is_free)
                    <span class="free">Free</span>
                @else
                    <span class="amount">{{ $course->price_formatted }}</span>
                @endif
            </div>
            <div class="rating">
                <span class="stars">★★★★☆</span>
                <span class="count">({{ $course->ratings_count }})</span>
            </div>
        </div>
    </div>
</div>
```

## 🎨 Styles

Styles define the visual appearance of the theme. We recommend using SCSS for better organization and maintainability.

### Main Stylesheet (main.scss)

```scss
// Import variables
@import 'variables';

// Import base styles
@import 'base/reset';
@import 'base/typography';
@import 'base/layout';

// Import component styles
@import 'components/header';
@import 'components/footer';
@import 'components/buttons';
@import 'components/forms';
@import 'components/course-card';
@import 'components/category-card';
@import 'components/testimonial';

// Import page-specific styles
@import 'pages/home';
@import 'pages/course';
@import 'pages/dashboard';
```

### Variables (variables.scss)

```scss
// Colors
$color-primary: var(--color-primary, #4285F4);
$color-secondary: var(--color-secondary, #34A853);
$color-text: var(--color-text, #333333);
$color-background: #FFFFFF;
$color-light: #F5F5F5;
$color-border: #E0E0E0;

// Typography
$font-heading: var(--font-heading, 'Poppins, sans-serif');
$font-body: var(--font-body, 'Inter, sans-serif');
$font-size-base: 16px;
$line-height-base: 1.5;

// Spacing
$spacing-xs: 0.25rem;
$spacing-sm: 0.5rem;
$spacing-md: 1rem;
$spacing-lg: 2rem;
$spacing-xl: 3rem;

// Breakpoints
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;

// Border radius
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 16px;

// Shadows
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
$shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
```

## 🧩 Theme Customization

Themes can be customized by tenants through the Theme Customizer interface. The customization options are defined in the `theme.json` file and are accessible in templates using the `theme_option()` helper function.

### Available Helper Functions

- `theme_name()`: Returns the current theme name
- `theme_asset($path)`: Returns the URL to a theme asset
- `theme_option($key, $default)`: Returns a theme option value
- `theme_path($path)`: Returns the filesystem path to a theme file
- `is_active_theme($name)`: Checks if a theme is active

## 📱 Mobile Theme Considerations

When developing themes, consider how they will appear in the mobile app. The mobile app uses React Native, so you'll need to provide additional configuration for mobile-specific styling.

### Mobile Theme Configuration (mobile.json)

```json
{
  "colors": {
    "primary": "#4285F4",
    "secondary": "#34A853",
    "background": "#FFFFFF",
    "card": "#F5F5F5",
    "text": "#333333",
    "border": "#E0E0E0"
  },
  "typography": {
    "fontFamily": {
      "regular": "Inter-Regular",
      "medium": "Inter-Medium",
      "bold": "Inter-Bold"
    },
    "fontSize": {
      "small": 12,
      "medium": 14,
      "large": 16,
      "xlarge": 20,
      "xxlarge": 24
    }
  },
  "spacing": {
    "xs": 4,
    "sm": 8,
    "md": 16,
    "lg": 24,
    "xl": 32
  },
  "borderRadius": {
    "small": 4,
    "medium": 8,
    "large": 16
  }
}
```

## 📦 Packaging and Distribution

To package a theme for distribution:

1. Ensure all required files are included
2. Compress the theme directory into a ZIP file
3. Upload the ZIP file to the marketplace

### Theme Validation

Before submitting a theme, ensure it passes the following validation checks:

1. All required files are present
2. Theme metadata is complete and valid
3. Templates use the correct syntax and variables
4. Styles are properly organized and use the theme variables
5. The theme is responsive and works on all screen sizes
6. The theme is compatible with the mobile app

## 🧪 Testing Your Theme

To test your theme locally:

1. Place your theme directory in the `themes` directory
2. Activate the theme in the admin dashboard
3. Test all pages and features
4. Test on different screen sizes
5. Test with different customization options

## 📚 Best Practices

1. **Responsive Design**: Ensure your theme works well on all screen sizes
2. **Accessibility**: Follow accessibility best practices (WCAG 2.1)
3. **Performance**: Optimize assets for fast loading
4. **Customization**: Use theme options for customizable elements
5. **Consistency**: Maintain consistent styling throughout the theme
6. **Documentation**: Provide clear documentation for your theme

## 🚀 Advanced Theme Features

### Custom JavaScript

You can include custom JavaScript to enhance your theme's functionality:

```javascript
// assets/js/main.js
document.addEventListener('DOMContentLoaded', function() {
  // Initialize components
  initializeSliders();
  initializeTooltips();
  initializeLightbox();
  
  // Handle responsive navigation
  const menuToggle = document.querySelector('.menu-toggle');
  const navigation = document.querySelector('.main-navigation');
  
  if (menuToggle && navigation) {
    menuToggle.addEventListener('click', function() {
      navigation.classList.toggle('is-active');
      this.setAttribute('aria-expanded', 
        this.getAttribute('aria-expanded') === 'true' ? 'false' : 'true'
      );
    });
  }
});

function initializeSliders() {
  // Implementation for sliders
}

function initializeTooltips() {
  // Implementation for tooltips
}

function initializeLightbox() {
  // Implementation for lightbox
}
```

### Custom Page Templates

You can create custom page templates for specific purposes:

```php
// templates/pages/landing.blade.php
@extends('theme::layouts.default')

@section('content')
<div class="landing-page">
    <!-- Custom landing page content -->
</div>
@endsection
```

### Theme Hooks

You can use theme hooks to extend functionality:

```php
// Register a custom hook
add_theme_hook('before_course_content', function($course) {
    echo '<div class="course-announcement">';
    echo 'New content added recently!';
    echo '</div>';
});
```

## 🔄 Theme Updates

When updating your theme:

1. Increment the version number in `theme.json`
2. Document the changes in a changelog
3. Ensure backward compatibility
4. Test thoroughly before releasing

## 📚 Resources

- [Laravel Blade Documentation](https://laravel.com/docs/blade)
- [SCSS Documentation](https://sass-lang.com/documentation)
- [Responsive Design Guidelines](https://developer.mozilla.org/en-US/docs/Web/Progressive_web_apps/Responsive/responsive_design_building_blocks)
- [Web Accessibility Guidelines](https://www.w3.org/WAI/standards-guidelines/wcag/)
- [React Native Style Guide](https://reactnative.dev/docs/style)
