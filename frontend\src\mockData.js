// Mock data for the frontend to use when the backend is not available

export const mockCourses = [
  {
    id: 1,
    title: "Advanced Web Development",
    description: "Learn modern web development techniques with React, Node.js, and MongoDB. This comprehensive course covers frontend and backend development.",
    price: 99.99,
    thumbnail: "https://source.unsplash.com/random?web&sig=1",
    instructor: {
      name: "<PERSON>",
      id: 1
    },
    tenant_id: 1,
    created_at: "2025-04-15T10:00:00Z",
    updated_at: "2025-04-15T10:00:00Z",
    enrollment_count: 245,
    lesson_count: 24,
    rating: 4.8,
    review_count: 156
  },
  {
    id: 2,
    title: "Data Science Fundamentals",
    description: "Master the basics of data science, including statistics, Python, and machine learning. Perfect for beginners looking to enter the field.",
    price: 89.99,
    thumbnail: "https://source.unsplash.com/random?data&sig=2",
    instructor: {
      name: "<PERSON>",
      id: 2
    },
    tenant_id: 2,
    created_at: "2025-04-14T10:00:00Z",
    updated_at: "2025-04-14T10:00:00Z",
    enrollment_count: 189,
    lesson_count: 32,
    rating: 4.7,
    review_count: 123
  },
  {
    id: 3,
    title: "UI/UX Design Principles",
    description: "Learn the fundamentals of user interface and user experience design. Create beautiful, intuitive designs that users will love.",
    price: 79.99,
    thumbnail: "https://source.unsplash.com/random?design&sig=3",
    instructor: {
      name: "Michael Brown",
      id: 3
    },
    tenant_id: 3,
    created_at: "2025-04-13T10:00:00Z",
    updated_at: "2025-04-13T10:00:00Z",
    enrollment_count: 156,
    lesson_count: 18,
    rating: 4.9,
    review_count: 98
  },
  {
    id: 4,
    title: "Digital Marketing Basics",
    description: "Understand the fundamentals of digital marketing, including SEO, social media, and content marketing. Start growing your online presence.",
    price: 69.99,
    thumbnail: "https://source.unsplash.com/random?marketing&sig=4",
    instructor: {
      name: "Sarah Wilson",
      id: 4
    },
    tenant_id: 4,
    created_at: "2025-04-12T10:00:00Z",
    updated_at: "2025-04-12T10:00:00Z",
    enrollment_count: 134,
    lesson_count: 20,
    rating: 4.6,
    review_count: 87
  },
  {
    id: 5,
    title: "Mobile App Development with Flutter",
    description: "Build cross-platform mobile applications using Flutter and Dart. Create beautiful, responsive apps for iOS and Android.",
    price: 109.99,
    thumbnail: "https://source.unsplash.com/random?mobile&sig=5",
    instructor: {
      name: "David Lee",
      id: 5
    },
    tenant_id: 1,
    created_at: "2025-04-11T10:00:00Z",
    updated_at: "2025-04-11T10:00:00Z",
    enrollment_count: 122,
    lesson_count: 28,
    rating: 4.8,
    review_count: 76
  },
  {
    id: 6,
    title: "Machine Learning Masterclass",
    description: "Dive deep into machine learning algorithms and applications. Learn how to build and deploy ML models in production.",
    price: 129.99,
    thumbnail: "https://source.unsplash.com/random?machine&sig=6",
    instructor: {
      name: "Robert Chen",
      id: 6
    },
    tenant_id: 2,
    created_at: "2025-04-10T10:00:00Z",
    updated_at: "2025-04-10T10:00:00Z",
    enrollment_count: 110,
    lesson_count: 36,
    rating: 4.9,
    review_count: 65
  },
  {
    id: 7,
    title: "Advanced React Patterns",
    description: "Learn advanced React patterns and best practices for building scalable applications. Take your React skills to the next level.",
    price: 99.99,
    thumbnail: "https://source.unsplash.com/random?react&sig=7",
    instructor: {
      name: "Jessica Miller",
      id: 7
    },
    tenant_id: 3,
    created_at: "2025-04-09T10:00:00Z",
    updated_at: "2025-04-09T10:00:00Z",
    enrollment_count: 98,
    lesson_count: 22,
    rating: 4.7,
    review_count: 54
  },
  {
    id: 8,
    title: "DevOps for Developers",
    description: "Master DevOps practices and tools for modern software development. Learn CI/CD, Docker, Kubernetes, and more.",
    price: 119.99,
    thumbnail: "https://source.unsplash.com/random?devops&sig=8",
    instructor: {
      name: "Thomas Wilson",
      id: 8
    },
    tenant_id: 4,
    created_at: "2025-04-08T10:00:00Z",
    updated_at: "2025-04-08T10:00:00Z",
    enrollment_count: 87,
    lesson_count: 30,
    rating: 4.6,
    review_count: 43
  }
];

export const mockUsers = [
  {
    id: 1,
    name: "Admin User",
    email: "<EMAIL>",
    role: "admin",
    avatar: "https://ui-avatars.com/api/?name=Admin+User&background=ff7700&color=fff",
    tenant_id: null,
    created_at: "2025-04-01T10:00:00Z",
    is_banned: false,
    status: "active"
  },
  {
    id: 2,
    name: "Tech Academy Instructor",
    email: "<EMAIL>",
    role: "tenant",
    avatar: "https://ui-avatars.com/api/?name=Tech+Academy&background=0088cc&color=fff",
    tenant_id: 1,
    created_at: "2025-04-02T10:00:00Z",
    is_banned: false,
    status: "active"
  },
  {
    id: 3,
    name: "John Doe",
    email: "<EMAIL>",
    role: "student",
    avatar: "https://ui-avatars.com/api/?name=John+Doe&background=4caf50&color=fff",
    tenant_id: null,
    created_at: "2025-04-03T10:00:00Z",
    is_banned: false,
    status: "active"
  },
  {
    id: 4,
    name: "Jane Smith",
    email: "<EMAIL>",
    role: "student",
    avatar: "https://ui-avatars.com/api/?name=Jane+Smith&background=9c27b0&color=fff",
    tenant_id: null,
    created_at: "2025-04-04T10:00:00Z",
    is_banned: false,
    status: "active"
  },
  {
    id: 5,
    name: "Banned User",
    email: "<EMAIL>",
    role: "student",
    avatar: "https://ui-avatars.com/api/?name=Banned+User&background=f44336&color=fff",
    tenant_id: null,
    created_at: "2025-04-05T10:00:00Z",
    is_banned: true,
    status: "banned"
  },
  {
    id: 6,
    name: "Language School Owner",
    email: "<EMAIL>",
    role: "tenant",
    avatar: "https://ui-avatars.com/api/?name=Language+School&background=4caf50&color=fff",
    tenant_id: 2,
    created_at: "2025-04-06T10:00:00Z",
    is_banned: false,
    status: "active"
  }
];

export const mockTenants = [
  {
    id: 1,
    name: "Tech Academy",
    domain: "techacademy",
    description: "A leading technology education platform",
    logo: "https://ui-avatars.com/api/?name=Tech+Academy&background=0088cc&color=fff",
    created_at: "2025-04-01T10:00:00Z",
    is_active: true,
    owner_id: 2,
    status: "active",
    plan_id: 2
  },
  {
    id: 2,
    name: "Language School",
    domain: "languageschool",
    description: "Learn any language with our expert instructors",
    logo: "https://ui-avatars.com/api/?name=Language+School&background=4caf50&color=fff",
    created_at: "2025-04-02T10:00:00Z",
    is_active: true,
    owner_id: 6,
    status: "active",
    plan_id: 1
  },
  {
    id: 3,
    name: "Design Academy",
    domain: "designacademy",
    description: "Creative design courses for all levels",
    logo: "https://ui-avatars.com/api/?name=Design+Academy&background=9c27b0&color=fff",
    created_at: "2025-04-03T10:00:00Z",
    is_active: true,
    owner_id: 7,
    status: "active",
    plan_id: 2
  },
  {
    id: 4,
    name: "Marketing School",
    domain: "marketingschool",
    description: "Digital marketing education for professionals",
    logo: "https://ui-avatars.com/api/?name=Marketing+School&background=f44336&color=fff",
    created_at: "2025-04-04T10:00:00Z",
    is_active: true,
    owner_id: 8,
    status: "active",
    plan_id: 1
  },
  {
    id: 5,
    name: "Fitness Courses",
    domain: "fitnesscourses",
    description: "Get fit with our online fitness courses",
    logo: "https://ui-avatars.com/api/?name=Fitness+Courses&background=ff9800&color=fff",
    created_at: "2025-04-05T10:00:00Z",
    is_active: false,
    owner_id: 9,
    status: "pending",
    plan_id: 1
  },
  {
    id: 6,
    name: "Cooking Academy",
    domain: "cookingacademy",
    description: "Learn to cook like a professional chef",
    logo: "https://ui-avatars.com/api/?name=Cooking+Academy&background=795548&color=fff",
    created_at: "2025-04-06T10:00:00Z",
    is_active: false,
    owner_id: 10,
    status: "pending",
    plan_id: 1
  }
];

export const mockLessons = [
  {
    id: 1,
    course_id: 1,
    title: "Lesson 1: Introduction to React",
    description: "Learn the basics of React and component-based architecture",
    video_url: "https://www.youtube.com/embed/dQw4w9WgXcQ",
    type: "video",
    order: 1
  },
  {
    id: 2,
    course_id: 1,
    title: "Lesson 2: State and Props",
    description: "Understanding state management and component props in React",
    video_url: "https://www.youtube.com/embed/dQw4w9WgXcQ",
    type: "video",
    order: 2
  },
  {
    id: 3,
    course_id: 1,
    title: "Lesson 3: React Hooks",
    description: "Using hooks for state and side effects in functional components",
    video_url: "https://www.youtube.com/embed/dQw4w9WgXcQ",
    type: "video",
    order: 3
  },
  {
    id: 4,
    course_id: 1,
    title: "Quiz: React Fundamentals",
    description: "Test your knowledge of React fundamentals",
    video_url: null,
    type: "quiz",
    order: 4
  }
];

export default {
  mockCourses,
  mockUsers,
  mockTenants,
  mockLessons
};
