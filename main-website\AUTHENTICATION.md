# Authentication System

This document explains the authentication system used in the Naxofy LMS platform.

## Overview

The authentication system uses a dual approach:

1. **Session-based Authentication**: For web routes (marketing, admin, tenant)
2. **JWT Authentication**: For API routes (frontend, mobile app)
3. **OAuth Integration**: Google authentication support

## JWT Authentication

JWT (JSON Web Token) authentication is used for API routes. It provides a stateless authentication mechanism that is ideal for APIs and mobile applications.

### Configuration

The JWT authentication is configured in the following files:

- `config/jwt.php`: JWT configuration
- `config/auth.php`: Authentication configuration
- `app/Models/User.php`: User model implementing JWTSubject interface

### API Routes

The following API routes are available for authentication:

- `POST /api/auth/login`: Login with email and password
- `POST /api/auth/register`: Register a new user
- `POST /api/auth/register-tenant`: Register a new tenant
- `POST /api/auth/logout`: Logout (invalidate token)
- `POST /api/auth/refresh`: Refresh token
- `GET /api/auth/profile`: Get user profile

### Example Usage

#### Login

```bash
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'
```

Response:

```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 3600,
  "user": {
    "id": 1,
    "name": "User Name",
    "email": "<EMAIL>",
    "role": "student",
    "created_at": "2023-01-01T00:00:00.000000Z",
    "updated_at": "2023-01-01T00:00:00.000000Z"
  }
}
```

#### Register

```bash
curl -X POST http://localhost:8000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"name":"New User","email":"<EMAIL>","password":"password","role":"student"}'
```

#### Using the Token

```bash
curl -X GET http://localhost:8000/api/user \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
```

## Google OAuth Integration

Google OAuth integration is available for both web and API routes.

### Configuration

The Google OAuth integration is configured in the following files:

- `config/services.php`: Google OAuth credentials
- `app/Http/Controllers/Auth/GoogleController.php`: Google OAuth controller

### Routes

The following routes are available for Google OAuth:

- `GET /api/auth/google`: Redirect to Google authentication page
- `GET /api/auth/google/callback`: Handle Google callback

### Environment Variables

The following environment variables need to be set:

```
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:8000/api/auth/google/callback
```

## Role-Based Access Control

The application uses role-based access control to restrict access to certain routes and resources.

### Roles

The following roles are available:

- `admin`: System administrator
- `tenant`: Tenant administrator (school/instructor)
- `student`: Student

### Middleware

The role-based access control is implemented using middleware:

- `auth:api`: Authenticate using JWT
- `role:admin`: Restrict access to admin users
- `role:tenant`: Restrict access to tenant users
- `role:student`: Restrict access to student users

### Example Usage

```php
Route::middleware(['auth:api', 'role:admin'])->group(function () {
    // Admin routes
});

Route::middleware(['auth:api', 'role:tenant'])->group(function () {
    // Tenant routes
});

Route::middleware(['auth:api', 'role:student'])->group(function () {
    // Student routes
});
```

## Testing

The authentication system can be tested using the following test classes:

- `tests/Feature/AuthenticationTest.php`: Tests for JWT authentication
- `tests/Feature/GoogleAuthTest.php`: Tests for Google OAuth integration
- `tests/Feature/RoleBasedAccessTest.php`: Tests for role-based access control

To run the tests:

```bash
php artisan test --filter=AuthenticationTest
php artisan test --filter=GoogleAuthTest
php artisan test --filter=RoleBasedAccessTest
```

## Troubleshooting

### JWT Token Issues

If you encounter issues with JWT tokens, try the following:

1. Regenerate the JWT secret key:
   ```bash
   php artisan jwt:secret
   ```

2. Clear the cache:
   ```bash
   php artisan cache:clear
   ```

3. Check the token expiration time in `config/jwt.php`

### Google OAuth Issues

If you encounter issues with Google OAuth, try the following:

1. Check the Google OAuth credentials in `.env`
2. Verify the redirect URI in the Google Developer Console
3. Check the callback URL in the routes file

## Security Considerations

1. **Token Storage**: Store JWT tokens securely (e.g., in HttpOnly cookies or secure storage on mobile devices)
2. **HTTPS**: Always use HTTPS in production
3. **Token Expiration**: Set appropriate token expiration times
4. **CSRF Protection**: Implement CSRF protection for web routes
5. **Rate Limiting**: Implement rate limiting for authentication routes
