<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Extension;
use App\Models\Plan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ExtensionController extends Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        // Check if user is admin for all methods
        $this->middleware(function ($request, $next) {
            if (!Auth::check() || !Auth::user()->isAdmin()) {
                return redirect()->route('login')->with('error', 'You do not have permission to access this area.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of the extensions.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get extensions from database or use mock data if table doesn't exist yet
        try {
            $extensions = Extension::all();
            
            if ($extensions->isEmpty()) {
                $extensions = $this->getMockExtensions();
            }
        } catch (\Exception $e) {
            $extensions = $this->getMockExtensions();
        }

        // Stats for extension counts
        $extensionStats = [
            'total' => $extensions->count(),
            'active' => $extensions->where('status', 'active')->count(),
            'pending' => $extensions->where('status', 'pending')->count(),
            'inactive' => $extensions->where('status', 'inactive')->count(),
        ];

        return view('admin.extensions.index', compact('extensions', 'extensionStats'));
    }

    /**
     * Show the form for creating a new extension.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        // Get available plans
        try {
            $plans = Plan::where('is_active', true)->get(['id', 'name']);
            
            if ($plans->isEmpty()) {
                $plans = collect(['Basic', 'Standard', 'Premium']);
            }
        } catch (\Exception $e) {
            $plans = collect(['Basic', 'Standard', 'Premium']);
        }

        return view('admin.extensions.create', compact('plans'));
    }

    /**
     * Store a newly created extension in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:extensions,name',
            'description' => 'required|string',
            'long_description' => 'nullable|string',
            'icon' => 'required|string|max:50',
            'version' => 'required|string|max:20',
            'author' => 'required|string|max:255',
            'author_website' => 'nullable|url',
            'status' => 'required|in:active,inactive,pending',
            'is_featured' => 'boolean',
            'plans' => 'nullable|array',
            'plans.*' => 'exists:plans,id',
            'requirements' => 'nullable|array',
            'requirements.*' => 'string',
            'screenshots' => 'nullable|array',
            'screenshots.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Handle screenshots upload
            $screenshotPaths = [];
            if ($request->hasFile('screenshots')) {
                foreach ($request->file('screenshots') as $screenshot) {
                    $screenshotPaths[] = $screenshot->store('extensions/screenshots', 'public');
                }
            }

            $extension = Extension::create([
                'name' => $request->name,
                'slug' => Str::slug($request->name),
                'description' => $request->description,
                'long_description' => $request->long_description,
                'icon' => $request->icon,
                'version' => $request->version,
                'author' => $request->author,
                'author_website' => $request->author_website,
                'status' => $request->status,
                'is_featured' => $request->has('is_featured'),
                'requirements' => $request->requirements ?? [],
                'screenshots' => $screenshotPaths,
                'changelog' => [
                    [
                        'version' => $request->version,
                        'date' => now()->format('Y-m-d'),
                        'changes' => ['Initial release'],
                    ],
                ],
            ]);

            // Attach plans
            if ($request->has('plans')) {
                $extension->plans()->attach($request->plans);
            }

            return redirect()->route('admin.extensions.index')
                ->with('success', 'Extension created successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to create extension: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified extension.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        try {
            $extension = Extension::with('plans')->findOrFail($id);
        } catch (\Exception $e) {
            // Use mock data if extension not found
            $extension = collect($this->getMockExtensions())->firstWhere('id', $id);
            
            if (!$extension) {
                return redirect()->route('admin.extensions.index')
                    ->with('error', 'Extension not found.');
            }
        }

        // Get available plans
        try {
            $plans = Plan::where('is_active', true)->get(['id', 'name']);
            
            if ($plans->isEmpty()) {
                $plans = collect(['Basic', 'Standard', 'Premium']);
            }
        } catch (\Exception $e) {
            $plans = collect(['Basic', 'Standard', 'Premium']);
        }

        return view('admin.extensions.show', compact('extension', 'plans'));
    }

    /**
     * Show the form for editing the specified extension.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        try {
            $extension = Extension::with('plans')->findOrFail($id);
        } catch (\Exception $e) {
            // Use mock data if extension not found
            $extension = collect($this->getMockExtensions())->firstWhere('id', $id);
            
            if (!$extension) {
                return redirect()->route('admin.extensions.index')
                    ->with('error', 'Extension not found.');
            }
        }

        // Get available plans
        try {
            $plans = Plan::where('is_active', true)->get(['id', 'name']);
            
            if ($plans->isEmpty()) {
                $plans = collect(['Basic', 'Standard', 'Premium']);
            }
        } catch (\Exception $e) {
            $plans = collect(['Basic', 'Standard', 'Premium']);
        }

        return view('admin.extensions.edit', compact('extension', 'plans'));
    }

    /**
     * Update the specified extension in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:extensions,name,' . $id,
            'description' => 'required|string',
            'long_description' => 'nullable|string',
            'icon' => 'required|string|max:50',
            'version' => 'required|string|max:20',
            'author' => 'required|string|max:255',
            'author_website' => 'nullable|url',
            'status' => 'required|in:active,inactive,pending',
            'is_featured' => 'boolean',
            'plans' => 'nullable|array',
            'plans.*' => 'exists:plans,id',
            'requirements' => 'nullable|array',
            'requirements.*' => 'string',
            'new_screenshots' => 'nullable|array',
            'new_screenshots.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'remove_screenshots' => 'nullable|array',
            'remove_screenshots.*' => 'string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $extension = Extension::findOrFail($id);
            
            // Handle screenshots upload
            $screenshotPaths = $extension->screenshots ?? [];
            
            // Remove screenshots
            if ($request->has('remove_screenshots')) {
                foreach ($request->remove_screenshots as $screenshot) {
                    if (in_array($screenshot, $screenshotPaths)) {
                        // Delete file
                        if (Storage::disk('public')->exists($screenshot)) {
                            Storage::disk('public')->delete($screenshot);
                        }
                        
                        // Remove from array
                        $screenshotPaths = array_diff($screenshotPaths, [$screenshot]);
                    }
                }
            }
            
            // Add new screenshots
            if ($request->hasFile('new_screenshots')) {
                foreach ($request->file('new_screenshots') as $screenshot) {
                    $screenshotPaths[] = $screenshot->store('extensions/screenshots', 'public');
                }
            }

            $extension->update([
                'name' => $request->name,
                'slug' => Str::slug($request->name),
                'description' => $request->description,
                'long_description' => $request->long_description,
                'icon' => $request->icon,
                'version' => $request->version,
                'author' => $request->author,
                'author_website' => $request->author_website,
                'status' => $request->status,
                'is_featured' => $request->has('is_featured'),
                'requirements' => $request->requirements ?? [],
                'screenshots' => array_values($screenshotPaths), // Reindex array
            ]);

            // Sync plans
            if ($request->has('plans')) {
                $extension->plans()->sync($request->plans);
            } else {
                $extension->plans()->detach();
            }

            return redirect()->route('admin.extensions.show', $extension->id)
                ->with('success', 'Extension updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update extension: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Enable the specified extension.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function enable($id)
    {
        try {
            $extension = Extension::findOrFail($id);
            
            $extension->update([
                'status' => 'active',
            ]);
            
            return redirect()->back()
                ->with('success', 'Extension enabled successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to enable extension: ' . $e->getMessage());
        }
    }

    /**
     * Disable the specified extension.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function disable($id)
    {
        try {
            $extension = Extension::findOrFail($id);
            
            $extension->update([
                'status' => 'inactive',
            ]);
            
            return redirect()->back()
                ->with('success', 'Extension disabled successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to disable extension: ' . $e->getMessage());
        }
    }

    /**
     * Update the plans for the specified extension.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updatePlans(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'plans' => 'nullable|array',
            'plans.*' => 'exists:plans,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $extension = Extension::findOrFail($id);
            
            // Sync plans
            if ($request->has('plans')) {
                $extension->plans()->sync($request->plans);
            } else {
                $extension->plans()->detach();
            }
            
            return redirect()->back()
                ->with('success', 'Extension plans updated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update extension plans: ' . $e->getMessage());
        }
    }

    /**
     * Display the extension marketplace.
     *
     * @return \Illuminate\View\View
     */
    public function marketplace()
    {
        // Get marketplace extensions
        $extensions = $this->getMockMarketplaceExtensions();

        // Categories for filtering
        $categories = [
            'All',
            'Assessment',
            'Communication',
            'Content',
            'Engagement',
            'Analytics',
            'Integration',
        ];

        return view('admin.extensions.marketplace', compact('extensions', 'categories'));
    }

    /**
     * Display the extension requests.
     *
     * @return \Illuminate\View\View
     */
    public function requests()
    {
        // Get extension requests
        $requests = $this->getMockExtensionRequests();

        return view('admin.extensions.requests', compact('requests'));
    }

    /**
     * Approve an extension request.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function approveRequest($id)
    {
        // In a real implementation, this would update the extension request status
        // and possibly create a new extension

        return redirect()->route('admin.extensions.requests')
            ->with('success', 'Extension request approved successfully.');
    }

    /**
     * Reject an extension request.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function rejectRequest($id)
    {
        // In a real implementation, this would update the extension request status

        return redirect()->route('admin.extensions.requests')
            ->with('success', 'Extension request rejected successfully.');
    }

    /**
     * Get mock extensions data.
     *
     * @return \Illuminate\Support\Collection
     */
    private function getMockExtensions()
    {
        return collect([
            (object) [
                'id' => 1,
                'name' => 'Live Class',
                'slug' => 'live-class',
                'description' => 'Enable real-time virtual classrooms with video conferencing, screen sharing, and interactive whiteboard.',
                'version' => '1.2.0',
                'author' => 'Naxofy',
                'status' => 'active',
                'is_featured' => true,
                'icon' => 'video-camera',
                'installations' => 245,
                'rating' => 4.8,
                'created_at' => now()->subMonths(3),
                'updated_at' => now()->subWeeks(2),
                'plans' => ['Premium', 'Standard'],
            ],
            (object) [
                'id' => 2,
                'name' => 'Quiz Builder',
                'slug' => 'quiz-builder',
                'description' => 'Create interactive quizzes with multiple question types, automatic grading, and detailed analytics.',
                'version' => '2.1.0',
                'author' => 'Naxofy',
                'status' => 'active',
                'is_featured' => false,
                'icon' => 'academic-cap',
                'installations' => 312,
                'rating' => 4.6,
                'created_at' => now()->subMonths(5),
                'updated_at' => now()->subWeeks(1),
                'plans' => ['Premium', 'Standard', 'Basic'],
            ],
            (object) [
                'id' => 3,
                'name' => 'Certificate Generator',
                'slug' => 'certificate-generator',
                'description' => 'Automatically generate and issue certificates upon course completion with customizable templates.',
                'version' => '1.5.0',
                'author' => 'EduTech Solutions',
                'status' => 'active',
                'is_featured' => false,
                'icon' => 'document-text',
                'installations' => 189,
                'rating' => 4.5,
                'created_at' => now()->subMonths(2),
                'updated_at' => now()->subDays(10),
                'plans' => ['Premium'],
            ],
            (object) [
                'id' => 4,
                'name' => 'Discussion Forum',
                'slug' => 'discussion-forum',
                'description' => 'Add course-specific discussion forums for student collaboration and instructor moderation.',
                'version' => '1.0.2',
                'author' => 'Community Builders',
                'status' => 'pending',
                'is_featured' => false,
                'icon' => 'chat-alt',
                'installations' => 0,
                'rating' => 0,
                'created_at' => now()->subWeeks(2),
                'updated_at' => now()->subWeeks(2),
                'plans' => [],
            ],
            (object) [
                'id' => 5,
                'name' => 'Assignment Submission',
                'slug' => 'assignment-submission',
                'description' => 'Allow students to submit assignments with file uploads and receive instructor feedback.',
                'version' => '1.3.1',
                'author' => 'Naxofy',
                'status' => 'active',
                'is_featured' => true,
                'icon' => 'clipboard-check',
                'installations' => 278,
                'rating' => 4.7,
                'created_at' => now()->subMonths(4),
                'updated_at' => now()->subWeeks(3),
                'plans' => ['Premium', 'Standard'],
            ],
        ]);
    }

    /**
     * Get mock marketplace extensions data.
     *
     * @return \Illuminate\Support\Collection
     */
    private function getMockMarketplaceExtensions()
    {
        return collect([
            (object) [
                'id' => 1,
                'name' => 'Live Class',
                'slug' => 'live-class',
                'description' => 'Enable real-time virtual classrooms with video conferencing, screen sharing, and interactive whiteboard.',
                'version' => '1.2.0',
                'author' => 'Naxofy',
                'price' => 0,
                'is_free' => true,
                'is_featured' => true,
                'icon' => 'video-camera',
                'installations' => 245,
                'rating' => 4.8,
                'created_at' => now()->subMonths(3),
                'updated_at' => now()->subWeeks(2),
                'category' => 'Communication',
            ],
            (object) [
                'id' => 2,
                'name' => 'Quiz Builder',
                'slug' => 'quiz-builder',
                'description' => 'Create interactive quizzes with multiple question types, automatic grading, and detailed analytics.',
                'version' => '2.1.0',
                'author' => 'Naxofy',
                'price' => 0,
                'is_free' => true,
                'is_featured' => false,
                'icon' => 'academic-cap',
                'installations' => 312,
                'rating' => 4.6,
                'created_at' => now()->subMonths(5),
                'updated_at' => now()->subWeeks(1),
                'category' => 'Assessment',
            ],
            (object) [
                'id' => 3,
                'name' => 'Certificate Generator',
                'slug' => 'certificate-generator',
                'description' => 'Automatically generate and issue certificates upon course completion with customizable templates.',
                'version' => '1.5.0',
                'author' => 'EduTech Solutions',
                'price' => 29.99,
                'is_free' => false,
                'is_featured' => false,
                'icon' => 'document-text',
                'installations' => 189,
                'rating' => 4.5,
                'created_at' => now()->subMonths(2),
                'updated_at' => now()->subDays(10),
                'category' => 'Content',
            ],
            (object) [
                'id' => 5,
                'name' => 'Assignment Submission',
                'slug' => 'assignment-submission',
                'description' => 'Allow students to submit assignments with file uploads and receive instructor feedback.',
                'version' => '1.3.1',
                'author' => 'Naxofy',
                'price' => 0,
                'is_free' => true,
                'is_featured' => true,
                'icon' => 'clipboard-check',
                'installations' => 278,
                'rating' => 4.7,
                'created_at' => now()->subMonths(4),
                'updated_at' => now()->subWeeks(3),
                'category' => 'Assessment',
            ],
            (object) [
                'id' => 6,
                'name' => 'Progress Tracker',
                'slug' => 'progress-tracker',
                'description' => 'Track and visualize student progress through courses with detailed analytics and reports.',
                'version' => '2.0.0',
                'author' => 'Data Insights',
                'price' => 19.99,
                'is_free' => false,
                'is_featured' => false,
                'icon' => 'chart-bar',
                'installations' => 156,
                'rating' => 4.3,
                'created_at' => now()->subMonths(6),
                'updated_at' => now()->subMonths(1),
                'category' => 'Analytics',
            ],
        ]);
    }

    /**
     * Get mock extension requests data.
     *
     * @return \Illuminate\Support\Collection
     */
    private function getMockExtensionRequests()
    {
        return collect([
            (object) [
                'id' => 1,
                'extension_name' => 'Discussion Forum',
                'developer_name' => 'Community Builders',
                'developer_email' => '<EMAIL>',
                'description' => 'Add course-specific discussion forums for student collaboration and instructor moderation.',
                'status' => 'pending',
                'submitted_at' => now()->subWeeks(2),
            ],
            (object) [
                'id' => 2,
                'extension_name' => 'Social Learning',
                'developer_name' => 'Social Edu',
                'developer_email' => '<EMAIL>',
                'description' => 'Integrate social media features like profiles, activity feeds, and direct messaging.',
                'status' => 'pending',
                'submitted_at' => now()->subWeeks(3),
            ],
        ]);
    }
}
