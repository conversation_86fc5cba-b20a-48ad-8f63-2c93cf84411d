<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\MarketplaceReview;
use App\Models\Theme;
use App\Models\Module;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class MarketplaceReviewController extends Controller
{
    /**
     * Display a listing of the reviews for a specific item.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'reviewable_type' => 'required|in:theme,module',
            'reviewable_id' => 'required|integer',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:50',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
        
        $type = $request->input('reviewable_type');
        $id = $request->input('reviewable_id');
        $perPage = $request->input('per_page', 10);
        
        // Determine the model class based on the type
        $modelClass = $type === 'theme' ? Theme::class : Module::class;
        
        // Check if the item exists
        $item = $modelClass::find($id);
        if (!$item) {
            return response()->json([
                'success' => false,
                'message' => ucfirst($type) . ' not found'
            ], 404);
        }
        
        // Get approved reviews for the item
        $reviews = MarketplaceReview::with('user')
            ->approved()
            ->forReviewable($modelClass, $id)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
            
        return response()->json([
            'success' => true,
            'data' => $reviews
        ]);
    }

    /**
     * Store a newly created review in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'reviewable_type' => 'required|in:theme,module',
            'reviewable_id' => 'required|integer',
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
        
        $user = Auth::user();
        $type = $request->input('reviewable_type');
        $id = $request->input('reviewable_id');
        
        // Determine the model class based on the type
        $modelClass = $type === 'theme' ? Theme::class : Module::class;
        
        // Check if the item exists
        $item = $modelClass::find($id);
        if (!$item) {
            return response()->json([
                'success' => false,
                'message' => ucfirst($type) . ' not found'
            ], 404);
        }
        
        // Check if user has already reviewed this item
        $existingReview = MarketplaceReview::where('user_id', $user->id)
            ->where('reviewable_type', $modelClass)
            ->where('reviewable_id', $id)
            ->first();
            
        if ($existingReview) {
            return response()->json([
                'success' => false,
                'message' => 'You have already reviewed this ' . $type
            ], 400);
        }
        
        // Create the review
        $review = new MarketplaceReview();
        $review->user_id = $user->id;
        $review->reviewable_type = $modelClass;
        $review->reviewable_id = $id;
        $review->rating = $request->input('rating');
        $review->comment = $request->input('comment');
        $review->is_approved = false; // Require approval by default
        $review->save();
        
        return response()->json([
            'success' => true,
            'message' => 'Review submitted successfully and pending approval',
            'data' => $review
        ], 201);
    }

    /**
     * Update the specified review in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
        
        $user = Auth::user();
        
        // Find the review
        $review = MarketplaceReview::where('id', $id)
            ->where('user_id', $user->id)
            ->first();
            
        if (!$review) {
            return response()->json([
                'success' => false,
                'message' => 'Review not found or you are not authorized to update it'
            ], 404);
        }
        
        // Update the review
        $review->rating = $request->input('rating');
        $review->comment = $request->input('comment');
        $review->is_approved = false; // Require re-approval after update
        $review->save();
        
        return response()->json([
            'success' => true,
            'message' => 'Review updated successfully and pending approval',
            'data' => $review
        ]);
    }

    /**
     * Remove the specified review from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $user = Auth::user();
        
        // Find the review
        $review = MarketplaceReview::where('id', $id)
            ->where('user_id', $user->id)
            ->first();
            
        if (!$review) {
            return response()->json([
                'success' => false,
                'message' => 'Review not found or you are not authorized to delete it'
            ], 404);
        }
        
        // Delete the review
        $review->delete();
        
        return response()->json([
            'success' => true,
            'message' => 'Review deleted successfully'
        ]);
    }
    
    /**
     * Approve a review (admin only).
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function approve($id)
    {
        $user = Auth::user();
        
        // Check if user is admin
        if ($user->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }
        
        // Find the review
        $review = MarketplaceReview::find($id);
        if (!$review) {
            return response()->json([
                'success' => false,
                'message' => 'Review not found'
            ], 404);
        }
        
        // Approve the review
        $review->is_approved = true;
        $review->save();
        
        return response()->json([
            'success' => true,
            'message' => 'Review approved successfully',
            'data' => $review
        ]);
    }
    
    /**
     * Reject a review (admin only).
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function reject($id)
    {
        $user = Auth::user();
        
        // Check if user is admin
        if ($user->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }
        
        // Find the review
        $review = MarketplaceReview::find($id);
        if (!$review) {
            return response()->json([
                'success' => false,
                'message' => 'Review not found'
            ], 404);
        }
        
        // Delete the review (or you could set a 'rejected' status instead)
        $review->delete();
        
        return response()->json([
            'success' => true,
            'message' => 'Review rejected and removed successfully'
        ]);
    }
}
