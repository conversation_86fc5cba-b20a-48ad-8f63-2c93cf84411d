<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Module;
use App\Models\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ModuleController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $query = Module::query();

        // Filter by tenant if specified
        if ($request->has('tenant_id')) {
            $query->where('tenant_id', $request->tenant_id);
        }

        // If user is a tenant, only show their modules and global modules
        if ($user->role === 'tenant') {
            $query->where(function($q) use ($user) {
                $q->where('tenant_id', $user->tenant_id)
                  ->orWhereNull('tenant_id');
            });
        }

        // Filter by active status if specified
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // Filter by premium status if specified
        if ($request->has('is_premium')) {
            $query->where('is_premium', $request->boolean('is_premium'));
        }

        // Filter by featured status if specified
        if ($request->has('is_featured')) {
            $query->where('is_featured', $request->boolean('is_featured'));
        }

        // Filter by system status if specified
        if ($request->has('is_system')) {
            $query->where('is_system', $request->boolean('is_system'));
        }

        // Pagination
        $perPage = $request->input('per_page', 10);
        $modules = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $modules
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'version' => 'required|string|max:50',
            'author' => 'nullable|string|max:255',
            'author_url' => 'nullable|url',
            'thumbnail' => 'nullable|image|max:2048',
            'price' => 'nullable|numeric|min:0',
            'settings' => 'nullable|json',
            'permissions' => 'nullable|json',
            'hooks' => 'nullable|json',
            'dependencies' => 'nullable|json',
            'is_premium' => 'nullable|boolean',
            'is_featured' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();

        // Only admins and tenants can create modules
        if (!in_array($user->role, ['admin', 'tenant'])) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Create slug from name
        $slug = Str::slug($request->name);
        $count = 1;

        // Ensure slug is unique
        while (Module::where('slug', $slug)->exists()) {
            $slug = Str::slug($request->name) . '-' . $count;
            $count++;
        }

        // Create the module
        $module = new Module();
        $module->name = $request->name;
        $module->slug = $slug;
        $module->description = $request->description;
        $module->version = $request->version;
        $module->author = $request->author;
        $module->author_url = $request->author_url;
        $module->price = $request->price ?? 0;
        $module->is_premium = $request->boolean('is_premium', false);
        $module->is_featured = $request->boolean('is_featured', false);
        $module->is_active = false;
        $module->is_system = false;
        $module->status = $user->role === 'admin' ? 'approved' : 'pending';

        // Handle thumbnail upload
        if ($request->hasFile('thumbnail')) {
            $path = $request->file('thumbnail')->store('modules/thumbnails', 'public');
            $module->thumbnail = $path;
        }

        // Handle JSON fields
        if ($request->has('settings')) {
            $module->settings = json_decode($request->settings);
        }
        if ($request->has('permissions')) {
            $module->permissions = json_decode($request->permissions);
        }
        if ($request->has('hooks')) {
            $module->hooks = json_decode($request->hooks);
        }
        if ($request->has('dependencies')) {
            $module->dependencies = json_decode($request->dependencies);
        }

        // Set tenant ID for tenant users
        if ($user->role === 'tenant') {
            $module->tenant_id = $user->tenant_id;
        }

        $module->save();

        return response()->json([
            'success' => true,
            'message' => 'Module created successfully',
            'data' => $module
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(string $id)
    {
        $module = Module::find($id);

        if (!$module) {
            return response()->json([
                'success' => false,
                'message' => 'Module not found'
            ], 404);
        }

        $user = Auth::user();

        // Check if user has access to this module
        if ($user->role === 'tenant' && $module->tenant_id !== null && $module->tenant_id !== $user->tenant_id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $module
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, string $id)
    {
        $module = Module::find($id);

        if (!$module) {
            return response()->json([
                'success' => false,
                'message' => 'Module not found'
            ], 404);
        }

        $user = Auth::user();

        // Check if user has permission to update this module
        if ($user->role === 'tenant' && $module->tenant_id !== $user->tenant_id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'version' => 'sometimes|required|string|max:50',
            'author' => 'nullable|string|max:255',
            'author_url' => 'nullable|url',
            'thumbnail' => 'nullable|image|max:2048',
            'price' => 'nullable|numeric|min:0',
            'settings' => 'nullable|json',
            'permissions' => 'nullable|json',
            'hooks' => 'nullable|json',
            'dependencies' => 'nullable|json',
            'is_premium' => 'nullable|boolean',
            'is_featured' => 'nullable|boolean',
            'is_active' => 'nullable|boolean',
            'status' => 'nullable|string|in:pending,approved,rejected',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Update the module
        if ($request->has('name')) {
            $module->name = $request->name;

            // Update slug if name changes
            $slug = Str::slug($request->name);
            $count = 1;

            // Ensure slug is unique
            while (Module::where('slug', $slug)->where('id', '!=', $id)->exists()) {
                $slug = Str::slug($request->name) . '-' . $count;
                $count++;
            }

            $module->slug = $slug;
        }

        if ($request->has('description')) $module->description = $request->description;
        if ($request->has('version')) $module->version = $request->version;
        if ($request->has('author')) $module->author = $request->author;
        if ($request->has('author_url')) $module->author_url = $request->author_url;
        if ($request->has('price')) $module->price = $request->price;
        if ($request->has('is_premium')) $module->is_premium = $request->boolean('is_premium');
        if ($request->has('is_featured')) $module->is_featured = $request->boolean('is_featured');
        if ($request->has('is_active')) $module->is_active = $request->boolean('is_active');

        // Only admins can change status
        if ($request->has('status') && $user->role === 'admin') {
            $module->status = $request->status;
        }

        // Handle thumbnail upload
        if ($request->hasFile('thumbnail')) {
            // Delete old thumbnail if exists
            if ($module->thumbnail) {
                Storage::disk('public')->delete($module->thumbnail);
            }

            $path = $request->file('thumbnail')->store('modules/thumbnails', 'public');
            $module->thumbnail = $path;
        }

        // Handle JSON fields
        if ($request->has('settings')) {
            $module->settings = json_decode($request->settings);
        }
        if ($request->has('permissions')) {
            $module->permissions = json_decode($request->permissions);
        }
        if ($request->has('hooks')) {
            $module->hooks = json_decode($request->hooks);
        }
        if ($request->has('dependencies')) {
            $module->dependencies = json_decode($request->dependencies);
        }

        $module->save();

        return response()->json([
            'success' => true,
            'message' => 'Module updated successfully',
            'data' => $module
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(string $id)
    {
        $module = Module::find($id);

        if (!$module) {
            return response()->json([
                'success' => false,
                'message' => 'Module not found'
            ], 404);
        }

        $user = Auth::user();

        // Check if user has permission to delete this module
        if ($user->role !== 'admin' && ($user->role !== 'tenant' || $module->tenant_id !== $user->tenant_id)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Don't allow deleting active modules
        if ($module->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete an active module'
            ], 400);
        }

        // Don't allow deleting system modules
        if ($module->is_system) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete a system module'
            ], 400);
        }

        // Delete thumbnail if exists
        if ($module->thumbnail) {
            Storage::disk('public')->delete($module->thumbnail);
        }

        $module->delete();

        return response()->json([
            'success' => true,
            'message' => 'Module deleted successfully'
        ]);
    }

    /**
     * Install a module for a tenant.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function install(Request $request, string $id)
    {
        $module = Module::find($id);

        if (!$module) {
            return response()->json([
                'success' => false,
                'message' => 'Module not found'
            ], 404);
        }

        $user = Auth::user();

        // Only tenants can install modules
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can install modules'
            ], 403);
        }

        // Check if module is approved
        if ($module->status !== 'approved') {
            return response()->json([
                'success' => false,
                'message' => 'This module is not approved for installation'
            ], 400);
        }

        // Get the tenant
        $tenant = Tenant::find($user->tenant_id);

        // Check if module is already installed
        if ($tenant->installedModules()->where('module_id', $module->id)->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Module is already installed'
            ], 400);
        }

        // Check if module dependencies are satisfied
        if (!$module->checkDependencies()) {
            return response()->json([
                'success' => false,
                'message' => 'Module dependencies are not satisfied'
            ], 400);
        }

        // Check if module is premium and requires payment
        if ($module->is_premium && $module->price > 0) {
            // In a real implementation, you would check if the user has purchased this module
            // For now, we'll just allow the installation
        }

        // Install the module
        $tenant->installedModules()->attach($module->id, [
            'is_active' => true,
            'settings' => $module->settings,
        ]);

        // Increment download count
        $module->incrementDownloads();

        return response()->json([
            'success' => true,
            'message' => 'Module installed successfully',
            'data' => $module
        ]);
    }

    /**
     * Uninstall a module from a tenant.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function uninstall(Request $request, string $id)
    {
        $module = Module::find($id);

        if (!$module) {
            return response()->json([
                'success' => false,
                'message' => 'Module not found'
            ], 404);
        }

        $user = Auth::user();

        // Only tenants can uninstall modules
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can uninstall modules'
            ], 403);
        }

        // Get the tenant
        $tenant = Tenant::find($user->tenant_id);

        // Check if module is installed
        if (!$tenant->installedModules()->where('module_id', $module->id)->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Module is not installed'
            ], 400);
        }

        // Check if module is a system module
        if ($module->is_system) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot uninstall a system module'
            ], 400);
        }

        // Uninstall the module
        $tenant->installedModules()->detach($module->id);

        return response()->json([
            'success' => true,
            'message' => 'Module uninstalled successfully'
        ]);
    }

    /**
     * Enable a module for a tenant.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function enable(Request $request, string $id)
    {
        $module = Module::find($id);

        if (!$module) {
            return response()->json([
                'success' => false,
                'message' => 'Module not found'
            ], 404);
        }

        $user = Auth::user();

        // Only tenants can enable modules
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can enable modules'
            ], 403);
        }

        // Get the tenant
        $tenant = Tenant::find($user->tenant_id);

        // Check if module is installed
        $pivotRow = $tenant->installedModules()->where('module_id', $module->id)->first();
        if (!$pivotRow) {
            return response()->json([
                'success' => false,
                'message' => 'Module is not installed'
            ], 400);
        }

        // Check if module is already enabled
        if ($pivotRow->pivot->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Module is already enabled'
            ], 400);
        }

        // Check if module dependencies are satisfied
        if (!$module->checkDependencies()) {
            return response()->json([
                'success' => false,
                'message' => 'Module dependencies are not satisfied'
            ], 400);
        }

        // Enable the module
        $tenant->installedModules()->updateExistingPivot($module->id, [
            'is_active' => true,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Module enabled successfully'
        ]);
    }

    /**
     * Disable a module for a tenant.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function disable(Request $request, string $id)
    {
        $module = Module::find($id);

        if (!$module) {
            return response()->json([
                'success' => false,
                'message' => 'Module not found'
            ], 404);
        }

        $user = Auth::user();

        // Only tenants can disable modules
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can disable modules'
            ], 403);
        }

        // Get the tenant
        $tenant = Tenant::find($user->tenant_id);

        // Check if module is installed
        $pivotRow = $tenant->installedModules()->where('module_id', $module->id)->first();
        if (!$pivotRow) {
            return response()->json([
                'success' => false,
                'message' => 'Module is not installed'
            ], 400);
        }

        // Check if module is already disabled
        if (!$pivotRow->pivot->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Module is already disabled'
            ], 400);
        }

        // Check if module is a system module
        if ($module->is_system) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot disable a system module'
            ], 400);
        }

        // Disable the module
        $tenant->installedModules()->updateExistingPivot($module->id, [
            'is_active' => false,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Module disabled successfully'
        ]);
    }
}
