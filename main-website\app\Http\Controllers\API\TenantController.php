<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class TenantController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $tenants = Tenant::where('status', 'active')->get();
        
        return response()->json([
            'success' => true,
            'data' => $tenants,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'domain' => 'required|string|max:255|unique:tenants,domain',
            'description' => 'nullable|string',
            'logo' => 'nullable|string',
            'owner_id' => 'required|exists:users,id',
            'plan_id' => 'nullable|exists:plans,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $tenant = Tenant::create($validator->validated());

        // Update the owner's tenant_id and role
        $owner = User::find($request->owner_id);
        $owner->tenant_id = $tenant->id;
        $owner->role = 'tenant';
        $owner->save();

        return response()->json([
            'success' => true,
            'message' => 'Tenant created successfully',
            'data' => $tenant,
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $tenant = Tenant::with(['owner', 'plan'])->find($id);
        
        if (!$tenant) {
            return response()->json([
                'success' => false,
                'message' => 'Tenant not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $tenant,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $tenant = Tenant::find($id);
        
        if (!$tenant) {
            return response()->json([
                'success' => false,
                'message' => 'Tenant not found',
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'domain' => 'sometimes|string|max:255|unique:tenants,domain,' . $id,
            'description' => 'nullable|string',
            'logo' => 'nullable|string',
            'status' => 'sometimes|string|in:pending,active,suspended',
            'owner_id' => 'sometimes|exists:users,id',
            'plan_id' => 'nullable|exists:plans,id',
            'theme_id' => 'nullable|exists:themes,id',
            'is_active' => 'sometimes|boolean',
            'expires_at' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $tenant->update($validator->validated());

        return response()->json([
            'success' => true,
            'message' => 'Tenant updated successfully',
            'data' => $tenant,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $tenant = Tenant::find($id);
        
        if (!$tenant) {
            return response()->json([
                'success' => false,
                'message' => 'Tenant not found',
            ], 404);
        }

        // Set tenant users to null tenant_id
        User::where('tenant_id', $tenant->id)->update(['tenant_id' => null]);

        $tenant->delete();

        return response()->json([
            'success' => true,
            'message' => 'Tenant deleted successfully',
        ]);
    }

    /**
     * Approve a pending tenant.
     */
    public function approve(string $id)
    {
        $tenant = Tenant::find($id);
        
        if (!$tenant) {
            return response()->json([
                'success' => false,
                'message' => 'Tenant not found',
            ], 404);
        }

        if ($tenant->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'Tenant is not in pending status',
            ], 422);
        }

        $tenant->status = 'active';
        $tenant->save();

        return response()->json([
            'success' => true,
            'message' => 'Tenant approved successfully',
            'data' => $tenant,
        ]);
    }

    /**
     * Get tenant information for the current subdomain.
     */
    public function info(Request $request)
    {
        $domain = $request->route('domain');
        $tenant = Tenant::where('domain', $domain)->first();
        
        if (!$tenant) {
            return response()->json([
                'success' => false,
                'message' => 'Tenant not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $tenant,
        ]);
    }

    /**
     * Get students for a tenant.
     */
    public function students(Request $request)
    {
        $domain = $request->route('domain');
        $tenant = Tenant::where('domain', $domain)->first();
        
        if (!$tenant) {
            return response()->json([
                'success' => false,
                'message' => 'Tenant not found',
            ], 404);
        }

        $students = $tenant->students()->paginate(10);

        return response()->json([
            'success' => true,
            'data' => $students,
        ]);
    }
}
