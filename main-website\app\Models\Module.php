<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Module extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
        'thumbnail',
        'version',
        'author',
        'author_url',
        'price',
        'settings',
        'permissions',
        'hooks',
        'dependencies',
        'tenant_id',
        'is_active',
        'is_system',
        'is_premium',
        'is_featured',
        'status',
        'downloads',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price' => 'decimal:2',
        'settings' => 'array',
        'permissions' => 'array',
        'hooks' => 'array',
        'dependencies' => 'array',
        'is_active' => 'boolean',
        'is_system' => 'boolean',
        'is_premium' => 'boolean',
        'is_featured' => 'boolean',
        'downloads' => 'integer',
    ];

    /**
     * Get the tenant that owns the module.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the tenants that have installed this module.
     */
    public function tenants(): BelongsToMany
    {
        return $this->belongsToMany(Tenant::class)
            ->withPivot('is_active', 'settings')
            ->withTimestamps();
    }

    /**
     * Check if the module is free.
     */
    public function isFree(): bool
    {
        return $this->price == 0;
    }

    /**
     * Get the formatted price.
     */
    public function getFormattedPriceAttribute(): string
    {
        if ($this->isFree()) {
            return 'Free';
        }

        return '$' . number_format($this->price, 2);
    }

    /**
     * Get the thumbnail URL.
     */
    public function getThumbnailUrlAttribute(): string
    {
        if (!$this->thumbnail) {
            return asset('images/module-placeholder.jpg');
        }

        if (str_starts_with($this->thumbnail, 'http')) {
            return $this->thumbnail;
        }

        return asset('storage/' . $this->thumbnail);
    }

    /**
     * Activate the module.
     */
    public function activate(): bool
    {
        $this->is_active = true;
        return $this->save();
    }

    /**
     * Deactivate the module.
     */
    public function deactivate(): bool
    {
        $this->is_active = false;
        return $this->save();
    }

    /**
     * Increment the download count.
     */
    public function incrementDownloads(): bool
    {
        $this->downloads++;
        return $this->save();
    }

    /**
     * Check if the module dependencies are satisfied.
     */
    public function checkDependencies(): bool
    {
        if (empty($this->dependencies)) {
            return true;
        }

        foreach ($this->dependencies as $slug => $version) {
            $dependency = self::where('slug', $slug)
                ->where('is_active', true)
                ->first();

            if (!$dependency) {
                return false;
            }

            // Simple version check (in a real app, you'd use semantic versioning)
            if (version_compare($dependency->version, $version, '<')) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get the reviews for the module.
     */
    public function reviews(): MorphMany
    {
        return $this->morphMany(Review::class, 'reviewable');
    }

    /**
     * Get the average rating for the module.
     */
    public function getAverageRatingAttribute(): float
    {
        return $this->reviews()->avg('rating') ?? 0;
    }

    /**
     * Get the review count for the module.
     */
    public function getReviewCountAttribute(): int
    {
        return $this->reviews()->count();
    }
}
