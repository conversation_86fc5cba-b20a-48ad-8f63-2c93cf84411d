<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('developer_submissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('developer_account_id')->constrained()->onDelete('cascade');
            $table->enum('type', ['theme', 'module'])->default('theme');
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('version');
            $table->text('description');
            $table->text('features')->nullable();
            $table->text('installation_instructions')->nullable();
            $table->text('usage_instructions')->nullable();
            $table->string('thumbnail')->nullable();
            $table->json('screenshots')->nullable();
            $table->string('demo_url')->nullable();
            $table->string('source_code_url')->nullable();
            $table->decimal('price', 8, 2)->default(0.00);
            $table->foreignId('category_id')->nullable()->constrained('marketplace_categories')->nullOnDelete();
            $table->enum('status', ['draft', 'submitted', 'in_review', 'approved', 'rejected', 'published'])->default('draft');
            $table->text('rejection_reason')->nullable();
            $table->json('test_results')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('developer_submissions');
    }
};
