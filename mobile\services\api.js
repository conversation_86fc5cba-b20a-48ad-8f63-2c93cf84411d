import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
// Import mock data for offline use
import { mockCourses, mockLessons, mockUsers } from './mockData';

// API URL configuration
// Use your computer's IP address instead of localhost
// This allows the app to connect to the backend from both emulators and physical devices
// Replace with your actual IP address for testing on physical devices
// For emulators: Android - use ********, iOS - use localhost
const YOUR_IP_ADDRESS = '********'; // Change this to your computer's IP address

// Use mock data when true (for development without backend)
// Set to true if you don't have a backend running
const USE_MOCK_DATA = false;

const API_URL = USE_MOCK_DATA ? null : `http://${YOUR_IP_ADDRESS}:8000/api`;

// Create axios instance
const api = axios.create({
  baseURL: API_URL || 'http://localhost:8000/api', // Fallback URL, won't be used in mock mode
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
    'Accept': 'application/json',
  },
  timeout: 10000,
});

// Log configuration
console.log(`API configured with USE_MOCK_DATA=${USE_MOCK_DATA}`);
if (!USE_MOCK_DATA) {
  console.log(`API URL: ${API_URL}`);
} else {
  console.log('Using mock data instead of real API');
}

// Add request interceptor to add auth token
api.interceptors.request.use(
  async (config) => {
    try {
      // Get the token from AsyncStorage
      const token = await AsyncStorage.getItem('token');
      console.log('Token from interceptor:', token ? 'Found token' : 'No token');

      if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
      }

      // Add CSRF token for Laravel
      config.headers['X-Requested-With'] = 'XMLHttpRequest';
      config.headers['Accept'] = 'application/json';

      return config;
    } catch (error) {
      console.error('Error in request interceptor:', error);
      return Promise.reject(error);
    }
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle token expiration
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // If the error is 401 and not already retrying
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Attempt to refresh the token
        const response = await api.post('/auth/refresh');
        const { access_token } = response.data;

        // Save the new token
        await AsyncStorage.setItem('token', access_token);

        // Update the authorization header
        originalRequest.headers['Authorization'] = `Bearer ${access_token}`;

        // Retry the original request
        return api(originalRequest);
      } catch (refreshError) {
        // If refresh fails, logout the user
        await AsyncStorage.removeItem('token');

        // Redirect to login (this should be handled by the app's navigation)
        // For now, just return the error
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// Auth services
export const authService = {
  login: async (credentials) => {
    if (USE_MOCK_DATA) {
      console.log('Using mock data for login');
      // Simulate login with mock data
      const user = mockUsers.find(u => u.email === credentials.email);
      if (user && credentials.password === 'password') { // Any password works in mock mode
        return {
          data: {
            access_token: 'mock-token-' + Date.now(),
            user: user
          }
        };
      } else {
        // Simulate login failure
        return Promise.reject({
          response: {
            data: { error: 'Invalid credentials' }
          }
        });
      }
    }

    try {
      // First, get the CSRF token
      await axios.get(`${API_URL}/csrf-token`, {
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        }
      });

      // Now proceed with login
      return api.post('/auth/login', credentials);
    } catch (error) {
      console.error('Error getting CSRF token:', error);
      return Promise.reject(error);
    }
  },
  register: async (userData) => {
    if (USE_MOCK_DATA) {
      console.log('Using mock data for registration');
      // Check if email already exists
      const existingUser = mockUsers.find(u => u.email === userData.email);
      if (existingUser) {
        return Promise.reject({
          response: {
            data: { message: 'Email already in use' }
          }
        });
      }
      // Simulate successful registration
      return {
        data: {
          message: 'User successfully registered',
          user: {
            id: mockUsers.length + 1,
            name: userData.name,
            email: userData.email,
            role: 'student',
            created_at: new Date().toISOString()
          }
        }
      };
    }
    return api.post('/auth/register', userData);
  },
  registerTenant: async (tenantData) => {
    if (USE_MOCK_DATA) {
      console.log('Using mock data for tenant registration');
      return {
        data: {
          message: 'Tenant successfully registered and awaiting approval',
          user: {
            id: mockUsers.length + 1,
            name: tenantData.name,
            email: tenantData.email,
            role: 'tenant',
            created_at: new Date().toISOString()
          },
          tenant: {
            id: mockUsers.length + 1,
            name: tenantData.tenant_name,
            domain: tenantData.domain,
            is_active: false
          }
        }
      };
    }
    return api.post('/auth/register-tenant', tenantData);
  },
  logout: async () => {
    if (USE_MOCK_DATA) {
      console.log('Using mock data for logout');
      return { data: { message: 'User successfully signed out' } };
    }
    return api.post('/auth/logout');
  },
  getProfile: async () => {
    if (USE_MOCK_DATA) {
      console.log('Using mock data for profile');
      // Return the first user as the current user
      return { data: mockUsers[0] };
    }
    return api.get('/auth/profile');
  },
  refreshToken: async () => {
    if (USE_MOCK_DATA) {
      console.log('Using mock data for token refresh');
      return {
        data: {
          access_token: 'mock-token-' + Date.now(),
          token_type: 'bearer',
          expires_in: 3600,
          user: mockUsers[0]
        }
      };
    }
    return api.post('/auth/refresh');
  },
};

// Course services
export const courseService = {
  getAllCourses: async (params) => {
    if (USE_MOCK_DATA) {
      console.log('Using mock data for courses');
      // Filter mock data based on search params if provided
      let filteredCourses = [...mockCourses];
      if (params && params.search) {
        const searchTerm = params.search.toLowerCase();
        filteredCourses = filteredCourses.filter(course =>
          course.title.toLowerCase().includes(searchTerm) ||
          course.description.toLowerCase().includes(searchTerm)
        );
      }
      return { data: filteredCourses };
    }
    return api.get('/courses', { params });
  },
  getCourseById: async (id) => {
    if (USE_MOCK_DATA) {
      console.log('Using mock data for course details');
      const course = mockCourses.find(c => c.id.toString() === id.toString());
      return { data: { course, is_enrolled: false } };
    }
    return api.get(`/courses/${id}`);
  },
  enrollInCourse: async (id) => {
    if (USE_MOCK_DATA) {
      console.log('Using mock data for course enrollment');
      return { data: { success: true, message: 'Enrolled successfully' } };
    }
    return api.post(`/courses/${id}/enroll`);
  },
  updateProgress: async (id, progress) => {
    if (USE_MOCK_DATA) {
      console.log('Using mock data for course progress');
      return { data: { success: true } };
    }
    return api.post(`/courses/${id}/progress`, { progress });
  },
};

// Lesson services
export const lessonService = {
  getLessonsByCourse: async (courseId) => {
    if (USE_MOCK_DATA) {
      console.log('Using mock data for lessons');
      const lessons = mockLessons.filter(lesson => lesson.course_id.toString() === courseId.toString());
      return { data: lessons };
    }
    return api.get('/lessons', { params: { course_id: courseId } });
  },
  getLessonById: async (id) => {
    if (USE_MOCK_DATA) {
      console.log('Using mock data for lesson details');
      const lesson = mockLessons.find(lesson => lesson.id.toString() === id.toString());
      return { data: lesson };
    }
    return api.get(`/lessons/${id}`);
  },
};

export default api;
