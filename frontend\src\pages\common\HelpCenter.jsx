import React, { useState } from 'react';
import {
  Container,
  Box,
  Typography,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  TextField,
  Button,
  Grid,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  InputAdornment
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import SearchIcon from '@mui/icons-material/Search';
import SchoolIcon from '@mui/icons-material/School';
import PaymentIcon from '@mui/icons-material/Payment';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import SecurityIcon from '@mui/icons-material/Security';
import ExtensionIcon from '@mui/icons-material/Extension';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';

const HelpCenter = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');

  const faqCategories = [
    { id: 'all', name: 'All FAQs', icon: <HelpOutlineIcon /> },
    { id: 'account', name: 'Account', icon: <AccountCircleIcon /> },
    { id: 'courses', name: 'Courses', icon: <SchoolIcon /> },
    { id: 'payment', name: 'Payment', icon: <PaymentIcon /> },
    { id: 'security', name: 'Security', icon: <SecurityIcon /> },
    { id: 'extensions', name: 'Extensions', icon: <ExtensionIcon /> }
  ];

  const faqs = [
    {
      id: 1,
      category: 'account',
      question: 'How do I create an account?',
      answer: 'To create an account, click on the "Sign Up" button in the top right corner of the homepage. Fill in your details, agree to the Terms of Service and Privacy Policy, and click "Sign Up". You will receive a verification email to activate your account.'
    },
    {
      id: 2,
      category: 'account',
      question: 'How do I reset my password?',
      answer: 'If you forgot your password, click on the "Forgot Password" link on the login page. Enter your email address, and we will send you a link to reset your password. Follow the instructions in the email to create a new password.'
    },
    {
      id: 3,
      category: 'courses',
      question: 'How do I enroll in a course?',
      answer: 'To enroll in a course, navigate to the course page and click the "Enroll" button. If it\'s a paid course, you will be directed to the payment page. After successful payment or for free courses, you will be automatically enrolled and can access the course content.'
    },
    {
      id: 4,
      category: 'courses',
      question: 'Can I download course materials for offline viewing?',
      answer: 'Yes, most course materials can be downloaded for offline viewing. Look for the download icon next to the lesson or resource. Note that some instructors may disable downloads for certain content.'
    },
    {
      id: 5,
      category: 'payment',
      question: 'What payment methods do you accept?',
      answer: 'We accept credit/debit cards (Visa, MasterCard, American Express), PayPal, and in some regions, we support local payment methods like UPI, bank transfers, and mobile wallets.'
    },
    {
      id: 6,
      category: 'payment',
      question: 'How do I get a refund?',
      answer: 'If you\'re not satisfied with a course, you can request a refund within 30 days of purchase. Go to your account settings, select "Purchase History", find the course, and click "Request Refund". Please note that some courses may have different refund policies.'
    },
    {
      id: 7,
      category: 'security',
      question: 'Is my payment information secure?',
      answer: 'Yes, we use industry-standard encryption and security measures to protect your payment information. We do not store your full credit card details on our servers. All transactions are processed through secure payment gateways.'
    },
    {
      id: 8,
      category: 'security',
      question: 'How can I enable two-factor authentication?',
      answer: 'To enable two-factor authentication, go to your account settings, select "Security", and toggle on "Two-Factor Authentication". You can choose to receive codes via SMS or use an authenticator app. Follow the on-screen instructions to complete the setup.'
    },
    {
      id: 9,
      category: 'extensions',
      question: 'How do I install a theme or module?',
      answer: 'If you are a tenant or admin, you can install themes and modules from the respective management pages. Go to "Themes" or "Modules" in your dashboard, browse the available options, and click "Install" on the one you want to use.'
    },
    {
      id: 10,
      category: 'extensions',
      question: 'Can I create my own extensions?',
      answer: 'Yes, developers can create custom themes and modules for the platform. We provide developer documentation and APIs for building extensions. Visit our Developer Portal for more information and guidelines.'
    }
  ];

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleCategoryClick = (categoryId) => {
    setActiveCategory(categoryId);
  };

  const filteredFaqs = faqs.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) || 
                          faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = activeCategory === 'all' || faq.category === activeCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 8, mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom align="center">
          Help Center
        </Typography>
        <Typography variant="h6" align="center" color="text.secondary" paragraph>
          Find answers to common questions and learn how to use our platform
        </Typography>

        <Paper elevation={3} sx={{ p: 3, mt: 4, borderRadius: 2 }}>
          <TextField
            fullWidth
            placeholder="Search for answers..."
            variant="outlined"
            value={searchQuery}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ mb: 4 }}
          />

          <Grid container spacing={4}>
            <Grid item xs={12} md={3}>
              <Typography variant="h6" gutterBottom>
                Categories
              </Typography>
              <List component="nav">
                {faqCategories.map((category) => (
                  <ListItem
                    button
                    key={category.id}
                    selected={activeCategory === category.id}
                    onClick={() => handleCategoryClick(category.id)}
                  >
                    <ListItemIcon>
                      {category.icon}
                    </ListItemIcon>
                    <ListItemText primary={category.name} />
                  </ListItem>
                ))}
              </List>

              <Divider sx={{ my: 3 }} />

              <Typography variant="h6" gutterBottom>
                Need More Help?
              </Typography>
              <Typography variant="body2" paragraph>
                Can't find what you're looking for? Contact our support team.
              </Typography>
              <Button
                variant="contained"
                color="primary"
                fullWidth
                href="/contact-us"
              >
                Contact Support
              </Button>
            </Grid>

            <Grid item xs={12} md={9}>
              <Typography variant="h5" gutterBottom>
                Frequently Asked Questions
              </Typography>
              
              {filteredFaqs.length === 0 ? (
                <Paper sx={{ p: 3, textAlign: 'center', mt: 2 }}>
                  <Typography variant="h6">No results found</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Try different keywords or browse by category
                  </Typography>
                </Paper>
              ) : (
                filteredFaqs.map((faq) => (
                  <Accordion key={faq.id} sx={{ mb: 2 }}>
                    <AccordionSummary
                      expandIcon={<ExpandMoreIcon />}
                      aria-controls={`panel${faq.id}-content`}
                      id={`panel${faq.id}-header`}
                    >
                      <Typography variant="subtitle1">{faq.question}</Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Typography variant="body1">{faq.answer}</Typography>
                    </AccordionDetails>
                  </Accordion>
                ))
              )}
            </Grid>
          </Grid>
        </Paper>
      </Box>
    </Container>
  );
};

export default HelpCenter;
