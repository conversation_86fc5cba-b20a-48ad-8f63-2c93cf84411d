<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\AppBuild;
use App\Models\AppConfiguration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AppBuildController extends Controller
{
    /**
     * Display a listing of the builds for the authenticated tenant.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        // Only tenants can have app builds
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can have app builds'
            ], 403);
        }
        
        $platform = $request->input('platform');
        $environment = $request->input('environment');
        $status = $request->input('status');
        
        $query = AppBuild::where('tenant_id', $user->tenant_id);
        
        if ($platform) {
            $query->forPlatform($platform);
        }
        
        if ($environment) {
            $query->forEnvironment($environment);
        }
        
        if ($status) {
            $query->withStatus($status);
        }
        
        $builds = $query->orderBy('created_at', 'desc')->paginate(10);
        
        return response()->json([
            'success' => true,
            'data' => $builds
        ]);
    }
    
    /**
     * Create a new build for the authenticated tenant.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'platform' => 'required|in:ios,android,both',
            'environment' => 'required|in:development,staging,production',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
        
        $user = Auth::user();
        
        // Only tenants can create app builds
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can create app builds'
            ], 403);
        }
        
        // Get app configuration
        $appConfiguration = AppConfiguration::where('tenant_id', $user->tenant_id)->first();
        
        if (!$appConfiguration) {
            return response()->json([
                'success' => false,
                'message' => 'App configuration not found. Please create an app configuration first.'
            ], 404);
        }
        
        // Check if there's already a build in progress
        $inProgressBuild = AppBuild::where('tenant_id', $user->tenant_id)
            ->whereIn('status', ['queued', 'building'])
            ->first();
            
        if ($inProgressBuild) {
            return response()->json([
                'success' => false,
                'message' => 'There is already a build in progress. Please wait for it to complete.'
            ], 400);
        }
        
        // Increment build number
        $appConfiguration->incrementBuildNumber();
        
        // Create new build
        $build = new AppBuild();
        $build->app_configuration_id = $appConfiguration->id;
        $build->tenant_id = $user->tenant_id;
        $build->version = $appConfiguration->version;
        $build->build_number = $appConfiguration->build_number;
        $build->platform = $request->input('platform');
        $build->environment = $request->input('environment');
        $build->status = 'queued';
        $build->save();
        
        // In a real implementation, you would trigger the build process here
        // For now, we'll just simulate it by updating the status
        $this->simulateBuildProcess($build);
        
        return response()->json([
            'success' => true,
            'message' => 'Build created successfully and queued for processing',
            'data' => $build
        ], 201);
    }
    
    /**
     * Display the specified build.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $user = Auth::user();
        
        // Only tenants can view their own builds
        if ($user->role !== 'tenant') {
            return response()->json([
                'success' => false,
                'message' => 'Only tenants can view their own builds'
            ], 403);
        }
        
        $build = AppBuild::where('id', $id)
            ->where('tenant_id', $user->tenant_id)
            ->first();
            
        if (!$build) {
            return response()->json([
                'success' => false,
                'message' => 'Build not found'
            ], 404);
        }
        
        return response()->json([
            'success' => true,
            'data' => $build
        ]);
    }
    
    /**
     * List all builds (admin only).
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function adminIndex(Request $request)
    {
        $user = Auth::user();
        
        // Only admins can list all builds
        if ($user->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }
        
        $tenantId = $request->input('tenant_id');
        $platform = $request->input('platform');
        $environment = $request->input('environment');
        $status = $request->input('status');
        
        $query = AppBuild::with('tenant', 'appConfiguration');
        
        if ($tenantId) {
            $query->where('tenant_id', $tenantId);
        }
        
        if ($platform) {
            $query->forPlatform($platform);
        }
        
        if ($environment) {
            $query->forEnvironment($environment);
        }
        
        if ($status) {
            $query->withStatus($status);
        }
        
        $builds = $query->orderBy('created_at', 'desc')->paginate(10);
        
        return response()->json([
            'success' => true,
            'data' => $builds
        ]);
    }
    
    /**
     * Get a specific build (admin only).
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function adminShow($id)
    {
        $user = Auth::user();
        
        // Only admins can view specific builds
        if ($user->role !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }
        
        $build = AppBuild::with('tenant', 'appConfiguration')->find($id);
        
        if (!$build) {
            return response()->json([
                'success' => false,
                'message' => 'Build not found'
            ], 404);
        }
        
        return response()->json([
            'success' => true,
            'data' => $build
        ]);
    }
    
    /**
     * Simulate the build process (for demonstration purposes).
     *
     * @param  \App\Models\AppBuild  $build
     * @return void
     */
    private function simulateBuildProcess(AppBuild $build)
    {
        // In a real implementation, you would trigger an actual build process
        // For now, we'll just simulate it by updating the status after a delay
        
        // Update to building status
        $build->updateStatus('building', 'Build process started');
        
        // Simulate build process
        $buildLog = "Starting build process for {$build->platform} in {$build->environment} environment...\n";
        $buildLog .= "Generating Expo configuration...\n";
        $buildLog .= "Preparing build files...\n";
        $buildLog .= "Building app...\n";
        
        // Randomly succeed or fail
        $success = rand(0, 10) > 2; // 80% success rate
        
        if ($success) {
            $buildLog .= "Build completed successfully!\n";
            
            // Set build URLs
            if ($build->platform === 'ios' || $build->platform === 'both') {
                $build->ios_build_url = "https://expo.dev/artifacts/ios/{$build->app_configuration_id}/{$build->build_number}";
            }
            
            if ($build->platform === 'android' || $build->platform === 'both') {
                $build->android_build_url = "https://expo.dev/artifacts/android/{$build->app_configuration_id}/{$build->build_number}";
            }
            
            $build->updateStatus('completed', $buildLog);
        } else {
            $buildLog .= "Build failed. See logs for details.\n";
            $build->updateStatus('failed', $buildLog);
        }
    }
}
