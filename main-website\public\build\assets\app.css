/* Tailwind CSS */
@import 'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css';

/* Custom styles */
:root {
    --primary-color: #ff7700;
    --primary-color-light: #ff9a40;
    --primary-color-dark: #cc5c00;
    --secondary-color: #0369a1;
    --secondary-color-light: #38bdf8;
    --secondary-color-dark: #075985;
}

body {
    font-family: 'Inter', sans-serif;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

.bg-primary-100 {
    background-color: rgba(255, 119, 0, 0.1) !important;
}

.text-primary-500 {
    color: var(--primary-color) !important;
}

.bg-secondary {
    background-color: var(--secondary-color) !important;
}

.text-secondary {
    color: var(--secondary-color) !important;
}

.border-secondary {
    border-color: var(--secondary-color) !important;
}

.sidebar {
    width: 280px;
    min-height: 100vh;
    background-color: #1e293b;
    color: white;
    transition: all 0.3s;
}

.sidebar-collapsed {
    width: 80px;
}

.sidebar-collapsed .nav-text {
    display: none;
}

.sidebar-collapsed .nav-item {
    display: flex;
    justify-content: center;
}

.content {
    width: calc(100% - 280px);
    transition: all 0.3s;
}

.content-expanded {
    width: calc(100% - 80px);
}

.nav-item {
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.2s;
}

.nav-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.nav-item.active {
    background-color: var(--primary-color);
    color: white;
}

.nav-icon {
    margin-right: 0.75rem;
}

.card {
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    background-color: white;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-color-dark);
    border-color: var(--primary-color-dark);
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background-color: var(--secondary-color-dark);
    border-color: var(--secondary-color-dark);
}
